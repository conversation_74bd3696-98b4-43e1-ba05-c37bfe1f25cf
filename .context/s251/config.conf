[system]
sample = "Test-102bit-v2"
env_name = "S251"
point_label = "idle_point_iqmix"
invoker_addr = "tcp://***********:8088"
baseband_freq = 1050
m_baseband_freq = 1050
qaio_type = 72
save_type = "local"
local_root = "/home/<USER>/work/Data/data"
log_path = "/home/<USER>/work/Data/log"
config_path = "/home/<USER>/work/Data/conf"

[naga]
url = "tcp://***********:31101"
web = "http://***********:8030"
log_port = 27017

[minio]
s3_root = "***********:9000"
s3_access_key = "admin"
s3_secret_key = "bylz@2021"

[run]
exp_save_mode = 0
simulator_data_path = ""
use_simulator = True
dag_save_mode = 1
use_backtrace = False
register_dag = False
simulator_delay = 0.0
async_win_size = 10
reset_empty_xy_power = False
run_mode = None

[mongo]
inst_host = "***********"
inst_port = 27017
