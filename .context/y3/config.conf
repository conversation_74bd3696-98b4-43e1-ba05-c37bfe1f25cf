[system]
sample = "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）"
env_name = "Y3"
point_label = "1q_point_102"
invoker_addr = "tcp://************:20303"
baseband_freq = 1050
m_baseband_freq = 1050
qaio_type = 72
save_type = "local"
local_root = "/home/<USER>/work/Data/data"
log_path = "/home/<USER>/work/Data/log"
config_path = "/home/<USER>/work/Data/conf"
[naga]
url = "tcp://************:20305"
web = "http://************:20307"
log_port = 27017

[minio]
s3_root = "***********:9000"
s3_access_key = "admin"
s3_secret_key = "bylz@2021"

[run]
exp_save_mode = 0
simulator_data_path = ""
use_simulator = True
dag_save_mode = 1
use_backtrace = False
register_dag = False
simulator_delay = 0.0
async_win_size = 10
reset_empty_xy_power = False
run_mode = None

[mongo]
inst_host = "***********"
inst_port = 27017

