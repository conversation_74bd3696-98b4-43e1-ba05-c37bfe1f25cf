import asyncio
import time
from datetime import datetime
from multiprocessing import Process
from threading import Thread
from pyQCat.invoker import Invoker, DataCenter, AsyncDataCenter


def process_func(counts):
    db = DataCenter()
    # Invoker.load_account()
    while True:
        res = db.query_user_info()
        print(f"p-{counts}", res)
        time.sleep(1)


def creat_process(num):
    for x in range(num):
        proc = Process(target=process_func, args=(x,), daemon=True)
        proc.start()


def create_thread(num):
    for x in range(num):
        proc = Thread(target=process_func, args=(x,), daemon=False)
        proc.start()


async def test(num=8000):
    adb = AsyncDataCenter()
    asyncio.create_task(adb.query_chip())
    task_list = []
    t1 = time.time()
    print(f"{t1}--start")
    for x in range(num):
        task_list.append(asyncio.create_task(adb.query_user_info()))
    res2 = await asyncio.gather(*task_list)
    t2 = time.time()
    print(f"{t2}---end")

    nums = 0
    res = None
    for item in res2:
        if item["code"] != 200:
            nums += 1
            res = item

    print(f"{(t2 - t1)} timeout:{nums}")


async def test_async_with_sync():
    adb = AsyncDataCenter()
    task = asyncio.create_task(adb.query_chip())

    db = DataCenter()
    print(db.query_version())

    print(await asyncio.gather(task))


def test_multi_thread(num=10):
    def thread_run():
        asyncio.run(test(1000))
        print("end")

    def thread_test_sync():
        print("start")
        db = DataCenter()
        for x in range(1000):
            if x // 100 != 0:
                print(x)
            res = db.query_user_info()
        print("end")

    t_list = []
    for x in range(num):
        t = Thread(name=f"x:{x}", target=thread_run)
        t.start()
        t_list.append(t)

    for t in t_list:
        t.join()
    print("---------------------")


if __name__ == "__main__":
    # Invoker.set_env(invoker_addr="tcp://***********:8300")
    print("test_connect_1", Invoker.test_connect(ip="***********", port=8300))
    # print("test_connect_2", Invoker.test_connect(ip="************", port=8088))
    # print(Invoker.verify_account("job", "123456"))
    print(Invoker.load_account())
    asyncio.run(test(16000))
    print
    test_multi_thread(10)
    # creat_process(10)
    # create_thread(10)
    # time.sleep(5)
    # res2 = Invoker.verify_account("test01", "123456")
    # print("*****************", res2)
    # time.sleep(5)
