from pyQCat.invoker import Invoker, DataCenter
from threading import Thread
import  time

class TokenThread(Thread):

    def run(self) -> None:
        db = DataCenter()
        count = 0
        while True:
            # time.sleep(0.1)
            try:
                res = db.query_group()
                if res["code"] == 340:
                    print(f"get error msg, count:{count}")
                    count = 0
            except Exception as err:
                print("***")
            count += 1
            if count % 100 == 0:
                print("____")



def test_multi_theard_get_msg(worker_num = 10):
    worker_dict = {}
    for x in range(worker_num):
        temp_worker = TokenThread()
        temp_worker.start()
        worker_dict.update({x: temp_worker})

    while True:
        time.sleep(2)
        keep_alive =  0
        for temp in worker_dict.values():
            if temp.is_alive():
                keep_alive += 1

        print(f"keep_alive {keep_alive}")



if __name__ == '__main__':
    Invoker.load_account()
    time.sleep(0.1)
    test_multi_theard_get_msg(10)