import socket
import weakref
from functools import lru_cache
from typing import Union, Any


class Cached(type):
    """
    Cached father class
    """

    def __init__(cls, *args, **kwargs):
        super().__init__(*args, **kwargs)
        cls.__cache = weakref.WeakValueDictionary()

    def __call__(cls, *args, **kwargs):
        if args in cls.__cache:
            return cls.__cache[args]
        else:
            obj = super().__call__(*args, **kwargs)
            cls.__cache[args] = obj
            return obj


@lru_cache()
def get_version():
    try:
        from pyQCat import get_version
        return get_version()
    except ImportError:
        return "UNKNOWN"

@lru_cache()
def get_device():
    try:
        from pyQCat import DEVICE
        return DEVICE
    except ImportError:
        return None


def comparing_monster_version(local_version: Union[str, Any],
                              courier_version: Union[str, Any]) -> int:
    """
    if
        local version < courier version, return -1
        local version = courier version, return 0
        local version > courier version, return 1
    else:
    return False
    """

    if local_version == courier_version:
        return 0
    version = [local_version, courier_version]
    version.sort(key=lambda x: [int(v) if v.isdigit() else v for v in x.split(".")])
    if version[-1] == courier_version:
        return -1
    else:
        return 1


def get_ipv4_addresses():
    addresses = []
    for interface in socket.getaddrinfo(socket.gethostname(), None):
        family, _, _, _, address = interface
        if family == socket.AF_INET:
            addresses.append(address[0])
    return addresses
