import pickle
import sys

# from orjson import orjson

from ..util import Cached
from ._base import BaseClient, REQUIRE_METHOD
import zmq
from zmq.asyncio import Context, Socket
import time
from typing import Union, List
from ...const import CRED_DIGIT
from ..json import loads
import asyncio

WAIT_TIME = 0.05


class InvokerClient(BaseClient, client_identity=b"invoker", metaclass=Cached):
    def __init__(self, addr: str = None):
        super().__init__(addr)
        if sys.platform.startswith("win"):
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
        self._sock: Socket = None
        self.connect(self.addr)

    def _check_upper(self):
        if self._digit != CRED_DIGIT[0]:
            self._digit = CRED_DIGIT[0]
            return True
        else:
            return False

    async def send(self, url: Union[str, bytes], data: Union[bytes, List[bytes]] = b"", method: bytes = b"post",
                   token: bytes = None):
        if type(data) is not list:
            data = [data]
        require_certification = BaseClient.require_certification()
        token = token if token is not None else self.token
        if not self.is_connect:
            self.re_connect(self.addr)
            if not self.is_connect:
                return self._no_conn_deal()
        self.lock.acquire()
        await self._sock.send_multipart(
            [self.client_identity, b"", token, url, method, require_certification, self.envision] + data)
        recv = None
        if method in REQUIRE_METHOD:
            recv = await self.recv(require_certification=require_certification)
            self.lock.release()
            if recv[0] == require_certification:
                return recv[1:]
            else:
                # recv = self.recv(require_certification=require_certification)
                # if recv[0] == require_certification:
                #     return recv[1:]
                self.re_connect(self.addr)
                return self._recv_auth_failed()
        if recv is None:
            self.lock.release()
            return self._recv_none_msg()
        return recv

    async def _recv(self, **kwargs):
        res = await self._sock.recv_multipart()
        return res[2:]

    def _no_conn_deal(self, **kwargs):
        no_conn_msg = {
            "code": 330,
            "data": {},
            "msg": "no Conn, need invoker addr, please set envrion."
        }

        data_json = self.encode(no_conn_msg)
        return [data_json]

    def _recv_auth_failed(self, **kwargs):
        auth_failed_msg = {
            "code": 340,
            "data": {},
            "msg": "get recv msg require certification failed! error msg"
        }
        data_json = self.encode(auth_failed_msg)
        return [data_json]

    def _recv_none_msg(self):
        none_msg = {"code": 300, "data": {}, "msg": "Nothing to return! pls check Invoker!"}
        data_json = self.encode(none_msg)
        return [data_json]

    def _recv_timeout_deal(self, **kwargs):
        timeout_msg = {
            "code": 300,
            "data": {},
            "msg": "get recv msg timeout."
        }

        data_json = self.encode(timeout_msg)
        require_certification = kwargs.get("require_certification", b"")
        return [require_certification, data_json]

    def connect(self, url: str, use: bool = True):
        if self._sock:
            self._sock.close()
            self._sock = None
        flag = False
        if not url or not isinstance(url, str) or url.strip() == "":
            return {"code": 305, "data": None, "msg": f"connect failed, url:{url} error"}
        url = url.strip()
        ctx = Context.instance()
        sock = ctx.socket(zmq.ROUTER)
        sock.setsockopt(zmq.RCVTIMEO, 1500)
        self._sock = sock
        try:
            sock.connect(url)
            time.sleep(WAIT_TIME)
            self.is_connect = True
            res = asyncio.run(self.send(b"/test/connect", token=b"", method=b"get"))
            code = loads(res[0].decode()).get("code")
            # code = orjson.loads(res[0]).get("code")
        except zmq.error.Again:
            code = 300
        if code in [300, 330]:
            msg = {"code": 300, "data": None, "msg": "conn addr error, can;t connect."}
        else:
            msg = {"code": 200, "data": None, "msg": "socket connect success."}
            flag = True

        if use and flag:
            self.addr = url
            self.is_connect = True
            self._sock.setsockopt(zmq.RCVTIMEO, 8000)
        else:
            self.dis_connect()
        return msg

    def dis_connect(self):
        self.addr = None
        self.is_connect = False
        if self._sock:
            self._sock.close()
        self._sock = None
