# -*- coding: utf-8 -*-

# This code is part of pyqcat-legend.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an 'AS IS' BASIS
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/09/11
# __author:       <PERSON>
"""The Invoker base client.
"""
from datetime import datetime

from pyQCat.invoker.qmq.util import Cached
import sys
import asyncio
from pyQCat.invoker.const import CRED_DIGIT
from pyQCat.invoker.qmq.credentials import Credentials, Environ
from typing import Any, Union, List
from pyQCat.invoker.qmq.json import dumps
from uuid import uuid4
from abc import abstractmethod
from threading import RLock


class BaseClient(metaclass=Cached):
    """
    Base sock Client.
    """
    client_identity = b""
    _digit_corner = -1

    def __init__(self):
        self._platform_adaptation()
        self.environ: bytes = b""
        self.addr: str = None
        self._digit = CRED_DIGIT[self._digit_corner]
        self._token: bytes = b""
        self.lock = RLock()

    def _platform_adaptation(self):
        """
        platform adaptation.
        """
        if sys.platform.startswith("win"):
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

        if len(CRED_DIGIT) < self._digit_corner or len(CRED_DIGIT) == 0:
            for x in range(self._digit_corner + 1 - len(CRED_DIGIT)):
                CRED_DIGIT.append("")

    @property
    def token(self):
        if self._digit != CRED_DIGIT[self._digit_corner]:
            cred = Credentials.read()
            self._token = cred.token
        return self._token

    @staticmethod
    def encode(data: Any):
        if isinstance(data, dict):
            # data = orjson.dumps(data, option=orjson.OPT_SERIALIZE_NUMPY)
            data = dumps(data)
        if isinstance(data, str):
            data = data.encode("utf-8")
        return data

    @staticmethod
    def require_certification() -> bytes:
        return uuid4().bytes

    def re_connect(self, url: str):
        """
        reconnect url.
        """
        self.lock.acquire()
        print("{:-^100}".format("invoker reconnect start"))
        self.dis_connect()
        self.addr = url
        self.connect(url)
        print("{:-^100}".format("invoker reconnect end"))
        self.lock.release()

    def set_environment(self, environ: Environ = None):
        environ = environ or Environ.read()
        self.environ = environ.env()
        if environ.invoker_addr is not None and (not self.connect_status() or self.addr != environ.invoker_addr):
            self.re_connect(environ.invoker_addr)

    @abstractmethod
    def connect_status(self) -> bool:
        pass

    @abstractmethod
    def connect(self, url: str) -> bool:
        pass

    @abstractmethod
    def dis_connect(self):
        pass

    @abstractmethod
    def send(self, url: Union[str, bytes], data: Union[bytes, List[bytes]] = b"", method: bytes = b"post",
             token: bytes = None):
        pass

    @abstractmethod
    def async_recv(self, require_id: str, time_out=None):
        pass


    @abstractmethod
    def recv(self, require_id: str, time_out=None):
        pass

    def _recv_timeout_deal(self, **kwargs):
        timeout_msg = {
            "code": 300,
            "data": {},
            "msg": f"get recv msg timeout.{kwargs} {datetime.now()}"
        }

        return [self.encode(timeout_msg)]

    def _no_conn_deal(self, **kwargs):
        no_conn_msg = {
            "code": 330,
            "data": {},
            "msg": "no Conn, need invoker addr, please set environ."
        }
        return [self.encode(no_conn_msg)]

    def _loss_require(self, **kwargs):
        no_conn_msg = {
            "code": 340,
            "data": {},
            "msg": "loss require"
        }
        return [self.encode(no_conn_msg)]
