# -*- coding: utf-8 -*-

# This code is part of pyqcat-legend.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an 'AS IS' BASIS
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/09/11
# __author:       <PERSON>
"""The Invoker Client by courier.
"""
import asyncio
import threading
import time
from datetime import datetime
from queue import Queue
from typing import Dict, List, Union

import zmq
from zmq.asyncio import Context, Poller

from pyQCat.invoker.qmq.util import Cached

from .base_client import BaseClient


class LinkThread(threading.Thread):
    def __init__(
            self,
            addr: str,
            require_map: Dict,
            waiting_queue: Queue,
            send_func,
            recv_func,
    ):
        super().__init__(name="invokcer_courier", daemon=True)
        self.addr = addr
        self.require_map = require_map
        self.waiting_queue = waiting_queue
        self._sock = None
        self._poll = None
        self._poll_signal = False
        self.send_func = send_func
        self.recv_func = recv_func

        self._main_thread: threading.Thread = None

    @property
    def is_connect(self):
        if self._sock and self._poll and self._poll_signal:
            return True

        return False

    def _link(self):
        try:
            ctx = Context.instance()
            self._sock = ctx.socket(zmq.ROUTER)
            self._sock.setsockopt(zmq.CONNECT_TIMEOUT, 500)
            self._sock.connect(self.addr)
            self._poll = Poller()
            self._poll.register(self._sock, zmq.POLLIN)

        except zmq.error.Again:
            self.release_resource()

        self._poll_signal = True
        self._main_thread = threading.main_thread()

    def release_resource(self):
        self._poll_signal = False
        if self._poll:
            self._poll.unregister(self._sock)
            del self._poll
            self._poll = None

        if self._sock:
            self._sock.close()
            self._sock = None

        self._main_thread = None

        self.release_cache()
        print("release.")

    def release_cache(self):
        self.require_map.clear()
        if not self.waiting_queue.empty():
            while not self.waiting_queue.empty():
                self.waiting_queue.get()

    async def send_require(self):
        while self._poll_signal:
            if not self.waiting_queue.empty():
                queue_len = self.waiting_queue.qsize() % 100
                for _ in range(queue_len):
                    msg = self.waiting_queue.get()
                    self.send_func(sock=self._sock, **msg)
                await asyncio.sleep(0.001)
            else:
                await asyncio.sleep(0.01)

    async def check_status(self):
        while self._poll_signal:
            if not self._main_thread.is_alive():
                print("will ending..............")
                self._poll_signal = False
            else:
                await asyncio.sleep(0.2)

    async def recv_require(self):
        while self._poll_signal:
            msg_signal = dict(await self._poll.poll(100))
            if self._sock in msg_signal:
                msg = await self._sock.recv_multipart()
                require_id, data = self.recv_func(msg)
                if require_id in self.require_map:
                    self.require_map.update({require_id: data})
            else:
                await asyncio.sleep(0.001)

    async def sock_run(self):
        send_msg_task = asyncio.create_task(self.send_require())
        recv_msg_task = asyncio.create_task(self.recv_require())
        # check_task = asyncio.create_task(self.check_status())

        await asyncio.gather(send_msg_task, recv_msg_task)

    def run(self) -> None:
        self._link()
        asyncio.run(self.sock_run())
        self.release_resource()

    def close(self):
        self._poll_signal = False


class CourierClient(BaseClient, metaclass=Cached):
    """
    courier client.
    """

    client_identity = b"invoker"
    _digit_corner = 0

    def __init__(self):
        super().__init__()
        self._link_thread: LinkThread = None
        self._cache_msg = {}
        self._cache_time = {}
        self._cache_time_pre = {}
        self._requre_queue = Queue()
        self._time_out = 20
        self._sleep_event = threading.Event()

    def _send_msg(
            self,
            sock,
            url: Union[str, bytes],
            data: Union[bytes, List[bytes]] = b"",
            method: bytes = b"post",
            token: bytes = b"",
            require_certification: bytes = None,
    ) -> bytes:
        require_certification = (
                require_certification or CourierClient.require_certification()
        )
        token = token if token is not None else self.token
        if isinstance(data, bytes):
            data = [data]
        msg_list = [
                       self.client_identity,
                       b"",
                       token,
                       url,
                       method,
                       require_certification,
                       self.environ,
                   ] + data
        self._cache_time_pre.update({require_certification: time.perf_counter()})
        self.lock.acquire()
        self._cache_time.update({require_certification: time.perf_counter()})
        sock.send_multipart(msg_list)
        self.lock.release()
        return require_certification

    def sleep(self, wait_time: float):
        self._sleep_event.wait(wait_time)

    def _deal_recv_msg(self, res_msg: List[bytes]):
        require_id, *data = res_msg[2:]
        return require_id, data

    def _create_sock(self, context, url, rcv_timout=0):
        """
        create client socket.
        """
        sock = context.socket(zmq.ROUTER)
        if rcv_timout != 0:
            sock.setsockopt(zmq.RCVTIMEO, rcv_timout)
        sock.connect(url)
        return sock

    def connect_status(self) -> bool:
        if self._link_thread and self._link_thread.is_connect:
            return True
        return False

    def test_connect(self, url):
        if self.connect_status():
            self.dis_connect()

        ctx = zmq.Context.instance()
        sock = self._create_sock(context=ctx, url=url, rcv_timout=1500)
        # if remove sleep, sock send will loss.
        time.sleep(0.2)
        self._send_msg(sock=sock, url=b"/test/connect", method=b"get")
        try:
            _, data = self._deal_recv_msg(res_msg=sock.recv_multipart())
            return {"code": 200, "data": None, "msg": "socket connect success."}
        except zmq.error.Again:
            return {"code": 300, "data": None, "msg": "conn addr error, can't connect."}
        finally:
            sock.close()

    def connect(self, url):
        if self.connect_status():
            self.dis_connect()

        self._link_thread = LinkThread(
            addr=self.addr,
            require_map=self._cache_msg,
            waiting_queue=self._requre_queue,
            send_func=self._send_msg,
            recv_func=self._deal_recv_msg,
        )
        self._link_thread.start()

        self.sleep(0.5)
        if not self._link_thread.is_connect:
            print("connect failed")
            self.dis_connect()

    def send(
            self,
            url: Union[str, bytes],
            data: Union[bytes, List[bytes]] = b"",
            method: bytes = b"post",
            token: bytes = None,
    ):
        if not self.connect_status():
            return b""
        token = token if token is not None else self.token

        send_msg = {
            "url": url,
            "require_certification": CourierClient.require_certification(),
            "data": data,
            "method": method,
            "token": token,
        }
        self._requre_queue.put(send_msg)
        require_id = send_msg["require_certification"]
        self._cache_msg.update({require_id: None})
        return require_id

    def recv_msg(self, require_id, start_time, time_, time_out):
        if require_id in self._cache_msg:
            if require_id in self._cache_time:
                start_time_ = self._cache_time[require_id]
            else:
                start_time_ = self._cache_time_pre.get(require_id)
            if not start_time_:
                start_time_ = start_time
            if self._cache_msg[require_id]:
                self._cache_time.pop(require_id, None)
                self._cache_time_pre.pop(require_id, None)
                return self._cache_msg.pop(require_id)
            else:
                if time.perf_counter() - start_time_ > time_out:
                    true_time = self._cache_msg.pop(require_id, None)
                    pre_time = self._cache_time_pre.pop(require_id, None)
                    now_time_perf = time.perf_counter()
                    now_time = datetime.now()
                    return self._recv_timeout_deal(require_id=require_id,
                                                   start_time=time_,
                                                   true_time=true_time,
                                                   pre_time=pre_time,
                                                   now_time_perf=now_time_perf,
                                                   now_time=now_time,
                                                   cache_id_len=len(self._cache_time),
                                                   cache_id=list(self._cache_time.keys()),
                                                   cache_id_pre_len=len(self._cache_time_pre),
                                                   cache_id_pre=list(self._cache_time_pre.keys()))
                else:
                    return None
        else:
            return self._loss_require()

    async def async_recv(self, require_id, time_out=None):
        if require_id == b"":
            return self._no_conn_deal()
        time_out = time_out or self._time_out
        start_time = time.perf_counter()
        time_ = datetime.now()
        while True:
            res = self.recv_msg(require_id, start_time, time_, time_out)
            if res is None:
                await asyncio.sleep(0.001)
            else:
                return res

    def recv(self, require_id, time_out=None):
        if require_id == b"":
            return self._no_conn_deal()
        time_out = time_out or self._time_out
        start_time = time.perf_counter()
        time_ = datetime.now()
        while True:
            res = self.recv_msg(require_id, start_time, time_, time_out)
            if res is None:
                time.sleep(0.01)
            else:
                return res

    def dis_connect(self):
        if self._link_thread:
            self._link_thread.close()
            self._link_thread.join()
            self._link_thread.release_cache()
            self._link_thread = None
