from abc import abstractmethod

# from orjson import orjson

from ..json import dumps
from typing import Any, Union, List
from uuid import uuid4
from ..util import Cached
from pyQCat.invoker.qmq.credentials import Credentials, Environ
import zmq
from threading import RLock

REQUIRE_METHOD = [b"post", b"get", b"delete", b"put", b"head", b"options", b"trace"]


class BaseClient(metaclass=Cached):
    """
    the client link to courier.

    Unlike normal tcp and http request clients,
    the client needs to store a communication token with the server,
    but once the token is wrong, it cannot connect to the server.

    the communication token is save in client_identity.
    Each subclass can specify its own communication token.

    such as InvokerClient(BaseClient, client_identity=b"invoker", metaclass=Cached):

    """

    @classmethod
    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__()
        if "client_identity" in kwargs:
            setattr(cls, "client_identity", kwargs["client_identity"])

    def __init__(self, addr: str = None):
        self.is_connect = False
        self.addr = addr
        self.envision = b""
        self.lock = RLock()
        self._digit = None
        self._token = b""

    async def recv(self, **kwargs):
        """
        Uniformly accept data functions.
        Args:
            **kwargs:

        Returns:

        """
        try:
            res = await self._recv(**kwargs)
            return res
        except zmq.error.Again:
            self.re_connect(self.addr)
            return self._recv_timeout_deal(**kwargs)

    def encode(self, data: Any) -> bytes:

        if isinstance(data, dict):
            # data = orjson.dumps(data, option=orjson.OPT_SERIALIZE_NUMPY)
            data = dumps(data)
        if isinstance(data, str):
            data = BaseClient._encode(data)
        return data

    @staticmethod
    def _encode(data: str) -> bytes:
        return data.encode("utf-8")

    @staticmethod
    def require_certification() -> bytes:
        return uuid4().bytes

    @abstractmethod
    def _check_upper(self):
        pass

    @property
    def token(self):
        if self._check_upper():
            cred = Credentials.read()
            self._token = cred.token
        return self._token

    @abstractmethod
    def send(self, url: Union[str, bytes], data: Union[bytes, List[bytes]] = b"", method: bytes = b"post",
             token: bytes = None):
        pass

    @abstractmethod
    def _recv(self, **kwargs):
        """
        Each client values the data acceptance mode and method.
        Args:
            **kwargs: special args.

        Returns:
            any
        """
        pass

    @abstractmethod
    def _recv_timeout_deal(self, **kwargs):
        """
        if recv timeout, use this function retrun deal reason.
        Args:
            **kwargs:

        Returns:

        """
        pass

    @abstractmethod
    def _no_conn_deal(self, **kwargs):
        """
        deal with connect failed.
        Args:
            **kwargs:

        Returns:

        """
        pass

    @abstractmethod
    def connect(self, url: str):
        pass

    @abstractmethod
    def dis_connect(self):
        pass

    def re_connect(self, url: str):
        self.lock.acquire()
        self.dis_connect()
        self.addr = url
        self.connect(url)
        self.lock.release()

    def __bool__(self):
        if self.addr is not None and self.is_connect:
            return True
        else:
            return False

    def set_environment(self, envision: Environ = None):
        envision = envision or Environ.read()
        self.envision = envision.env()
        if envision.invoker_addr is not None and not self.is_connect:
            self.re_connect(envision.invoker_addr)
