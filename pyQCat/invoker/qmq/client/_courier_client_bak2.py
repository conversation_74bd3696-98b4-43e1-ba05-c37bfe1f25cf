# -*- coding: utf-8 -*-

# This code is part of pyqcat-legend.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an 'AS IS' BASIS
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/09/11
# __author:       <PERSON>
"""The Invoker Client by courier.
"""
import asyncio
import time

from .base_client import BaseClient
from pyQCat.invoker.qmq.util import Cached
import zmq
from typing import Union, List
from threading import Thread


class CourierClient(BaseClient, metaclass=Cached):
    """
    courier client.
    """
    client_identity = b"invoker"
    _digit_corner = 0

    def __init__(self):
        super().__init__()
        self._sock = None
        self._poll = None
        self._poll_signal = True
        self._link_thread = None
        self._cache_msg = {}
        self._time_out = 8

    def _send_msg(self, sock, url: Union[str, bytes], data: Union[bytes, List[bytes]] = b"",
                  method: bytes = b"post",
                  token: bytes = b"") -> bytes:
        require_certification = CourierClient.require_certification()
        token = token if token is not None else self.token
        if isinstance(data, bytes):
            data = [data]
        msg_list = [self.client_identity, b"", token, url, method, require_certification, self.environ] + data
        self.lock.acquire()
        sock.send_multipart(msg_list)
        self.lock.release()
        return require_certification

    def _deal_recv_msg(self, res_msg: List[bytes]):
        require_id, *data = res_msg[2:]
        return require_id, data

    def _create_sock(self, context, url, rcv_timout=0):
        """
        create client socket.
        """
        sock = context.socket(zmq.ROUTER)
        if rcv_timout != 0:
            sock.setsockopt(zmq.RCVTIMEO, rcv_timout)
        sock.connect(url)
        return sock

    def connect_status(self) -> bool:
        if self._link_thread and self._sock and not self._poll_signal:
            return True

        return False

    def test_connect(self, url):
        if self.connect_status():
            self.dis_connect()

        ctx = zmq.Context.instance()
        sock = self._create_sock(context=ctx, url=url, rcv_timout=1500)
        # if remove sleep, sock send will loss.
        time.sleep(0.2)
        self._send_msg(sock=sock, url=b"/test/connect", method=b"get")
        try:
            _, data = self._deal_recv_msg(res_msg=sock.recv_multipart())
            return {"code": 200, "data": None, "msg": "socket connect success."}
        except zmq.error.Again:
            return {"code": 300, "data": None, "msg": "conn addr error, can't connect."}
        finally:
            sock.close()

    def connect(self, url):
        if self.connect_status():
            self.dis_connect()
        self._sock = self._create_sock(context=zmq.Context.instance(), url=url, rcv_timout=1)
        self._poll = zmq.Poller()
        self._poll.register(self._sock, zmq.POLLIN)
        # use to deal sync socket create, will faster send require will loss.
        time.sleep(0.1)
        self._poll_signal = False
        self._link_thread = Thread(name="invoker_courier", target=self.link_thread_run, daemon=True)
        self._link_thread.start()

    def link_thread_run(self):
        self._recv_async_task()
        self._poll.unregister(self._sock)
        del self._poll
        self._poll = None
        self._sock.close()
        self._sock = None
        self._cache_msg.clear()

    def _recv_async_task(self):
        while True:
            if self._poll_signal:
                return
            try:
                msg_signal = dict(self._poll.poll(1))
                if self._sock in msg_signal:
                    sock_res = self._sock.recv_multipart()
                    require_id, data = self._deal_recv_msg(sock_res)
                    if require_id in self._cache_msg:
                        self._cache_msg.update({require_id: data})
            except zmq.error.Again:
                pass

    def send(self, url: Union[str, bytes], data: Union[bytes, List[bytes]] = b"", method: bytes = b"post",
             token: bytes = None):
        if not self.connect_status():
            return b""
        token = token if token is not None else self.token
        require_id = self._send_msg(sock=self._sock, url=url, data=data, method=method, token=token)
        self._cache_msg.update({require_id: None})
        return require_id

    async def async_recv(self, require_id, time_out=None):
        if require_id == b"":
            return self._no_conn_deal()
        time_out = time_out or self._time_out
        start_time = time.perf_counter()
        while True:
            if require_id in self._cache_msg:
                if self._cache_msg[require_id]:
                    return self._cache_msg.pop(require_id)
                else:
                    if time.perf_counter() - start_time > time_out:
                        self._cache_msg.pop(require_id)
                        return self._recv_timeout_deal()
                    else:
                        await asyncio.sleep(0.001)
            else:
                return self._loss_require()

    def recv(self, require_id, time_out=None):
        if require_id == b"":
            return self._no_conn_deal()
        time_out = time_out or self._time_out
        start_time = time.perf_counter()
        while True:
            if require_id in self._cache_msg:
                if self._cache_msg[require_id]:
                    return self._cache_msg.pop(require_id)
                else:
                    if time.perf_counter() - start_time > time_out:
                        self._cache_msg.pop(require_id)
                        return self._recv_timeout_deal()
                    else:
                        time.sleep(0.01)
            else:
                return self._loss_require()

    def dis_connect(self):
        self._poll_signal = True
        if self._link_thread:
            self._link_thread.join()
            self._link_thread = None
        if self._poll:
            self._poll.unregister(self._sock)
            del self._poll
            self._poll = None
        if self._sock:
            self._sock.close()
            self._sock = None
        self._cache_msg.clear()
