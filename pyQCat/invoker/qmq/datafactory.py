# -*- coding: utf-8 -*-

# This code is part of pyqcat-legend.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an 'AS IS' BASIS
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/09/23
# __author:       <PERSON>
"""
the qmq database factory
"""
import sys
from .api import User, Dag, Expeirment, System, Data, Permission
from .api.chip import ChipV1 as Chip
from .client import CourierClient
from .credentials import Environ, Credentials
from .util import get_version, comparing_monster_version
from ..const import update_cred_digit, DEFAULT_FOLDER
from .api import require, CALLBACK

ENFORCEMENT = True if "-y" in sys.argv or "-Y" in sys.argv else False
ENFORCEMENT_MEG = """
If you want to start normally, first ensure that the cache in the current environment is not being used by other programs. 
If it is not in use, you can choose to add the "-y" startup parameter. 
If the cache is being used by other programs, you can add a numeric startup parameter and select a different cache directory.
"""


class ModifyError(Exception):
    def __init__(self, *args) -> None:
        super().__init__(*args)


class DataFactory:
    """
    Data Factory acts as a bridge between User and DataBase.
    DataFactory can log in to load the account configuration and experimental environment variables.
    Usually, if you want to log in to Invoker to obtain experimental data and chip configuration information,\
    you need to log in to obtain the account key first, and then use the key to initialize the user information\
    and configure the experimental environment.

    In the first step, you need to configure the remote address of B and set it as follows:
    INVOKER.set_env(invoker_addr: str = "tcp://********:8088")

    If you don't have an account, you can register for an account first,such as:
    INVOKER.register_account("ck01", "123456","123456","<EMAIL>")

    if you have account ,just login, cloud use:
    INVOKER.verify_account("ck01", "123456")

    if you get a token want to link courier, you cloud use:
    INVOKER.save_account("token")

    if you do experiment last time, you cloud use:
    INVOKER.load_account()

    After completing the experiment, if it is on a public platform,
    it is recommended to log out to prevent token leakage.
    INVOKER.logout_account()
    """

    def __init__(self):
        self.job_client = CourierClient()
        self._user = User(client=self.job_client)
        self._dag = Dag(client=self.job_client)
        self._chip = Chip(client=self.job_client)
        self._exp = Expeirment(client=self.job_client)
        self._data = Data(client=self.job_client)
        self._system = System(client=self.job_client)
        self._perms = Permission(client=self.job_client)
        self._require_model_ = [
            self._user,
            self._dag,
            self._exp,
            self._chip,
            self._data,
            self._system,
            self._perms,
        ]

    def __call__(self, *args, **kwargs):
        if args:
            for s_class in args:
                if isinstance(s_class, object):
                    for param in dir(s_class):
                        if not param.startswith("__") and hasattr(self, param):
                            setattr(s_class, param, getattr(self, param))

    def __getattr__(self, item):
        for model in self._require_model_:
            if hasattr(model, item):
                return getattr(model, item)

    def _init_env(self):
        environment_ = Environ.read()
        if environment_.invoker_addr is not None:
            self.job_client.set_environment(environment_)
        else:
            print("can't get courier addr info please set addr first")

    def set_env(
            self,
            invoker_addr: str = None,
            point_label: str = None,
            sample: str = None,
            env_name: str = None,
            enforcement_flag=False,
            **kwargs,
    ):
        """
        set invoker environment variable
        Args:
            invoker_addr: invoker connect addr.
            point_label:
            sample: the chip id.
            env_name: the env name such D5_0901
            **kwargs:

        Returns:

        """
        env = Environ.read()

        if not ENFORCEMENT and not enforcement_flag:
            if (
                    invoker_addr is not None
                    and env.invoker_addr != invoker_addr
                    and env.invoker_addr is not None
            ):
                self.rasie_enforcement_error("set_env")
            if (
                    point_label is not None
                    and env.point_label != point_label
                    and env.point_label is not None
            ):
                self.rasie_enforcement_error("set_env")
            if sample is not None and env.sample != sample and env.sample is not None:
                self.rasie_enforcement_error("set_env")
            if (
                    env_name is not None
                    and env.env_name != env_name
                    and env.env_name is not None
            ):
                self.rasie_enforcement_error("set_env")

        env.update(
            invoker_addr=invoker_addr,
            point_label=point_label,
            sample=sample,
            env_name=env_name,
        )
        env.save(True)
        self.job_client.set_environment(env)

    def get_env(self) -> Environ:
        """
        get invoker environment variable.
        Returns:
            Envrions obj, include addr\ sample \ env_name \ point_lable.
        """

        return Environ.read()

    def load_account(self):
        """
        load account.
        Returns:
            dict such as {"code": xxx, "msg":xxxx, "data": {}}


        """
        self._init_env()
        cred = Credentials.read()
        update_cred_digit()
        if cred.token == b"":
            return {
                "code": 340,
                "data": {},
                "msg": "can't find user token, please save_account before.",
            }
        else:
            res = require(self._user.query_user_info)()
            if res["code"] == 330:
                self.job_client.re_connect(self.job_client.addr)
                res = require(self._user.query_user_info)()

            return res

    def save_account(self, token: str, user: str, enforcement_flag=False):
        """
        save account token to system.
        Args:
            token:

        Returns:
            dict such as {"code": xxx, "msg":xxxx, "data": {}}


        """
        if token is not None and token != "":
            if (
                    Credentials.read().token != b""
                    and not ENFORCEMENT
                    and not enforcement_flag
            ):
                self.rasie_enforcement_error("save_account")

            cred = Credentials(token=token, user=user)
            cred.save(update=True)
            update_cred_digit()
            self._init_env()
            res = require(self._user.query_user_info)()
            if res["code"] == "200":
                return {"code": 200, "data": {}, "msg": "save account succ."}
            else:
                return res
        else:
            return {"code": 340, "data": {}, "msg": "the token illegal."}

    def verify_account(
            self, username: str, password: str, enforcement_flag=False
    ):
        """
        loging account, will get token save into env and use to experiment.
        Args:
            username: you personal username
            password:

        Returns:
            dict such as {"code": xxx, "msg":xxxx, "data": {}}


        """

        if Credentials.read().token != b"" and not ENFORCEMENT and not enforcement_flag:
            self.rasie_enforcement_error("verify_account")

        self._init_env()
        token_res = require(self._user.login)(username=username, password=password)
        if token_res["code"] in [330, 300]:
            self.job_client.re_connect(self.job_client.addr)
            token_res = require(self._user.login)(username=username, password=password)
        if token_res["code"] == 200 and token_res["data"]:
            token = token_res["data"]["token"]
            Credentials(token=token, user=username).save(True)
        return token_res

    def logout_account(self, retry: bool = False):
        """
        logout account, the token can;t use later, need login next use.
        Returns:
            dict such as {"code": xxx, "msg":xxxx, "data": {}}


        """
        res = require(self._user.logout)()
        if res["code"] != 200 and retry:
            self.logout_account()
        else:
            Credentials(token=b"").save()
        return res

    def register_account(
            self,
            username: str,
            passwd: str,
            repassed: str,
            email: str,
            group: str = "normal",
            enforcement_flag=False,
    ):
        """
        register account.
        Args:
            username: username, use to login and uuid for user.
            passwd: password
            repassed: same as passwd.
            email: the personal email, the only wey to reset password.
            group: group name.

        Returns:
            dict such as {"code": xxx, "msg":xxxx, "data": {}}

        """
        if Credentials.read().token != b"" and not ENFORCEMENT and not enforcement_flag:
            self.rasie_enforcement_error("register_account")
        self._init_env()
        res = require(self._user.register_user)(
            username, passwd, repassed, email, group
        )
        if res["code"] == 200:
            token = res["data"]["token"]
            Credentials(token=token, user=username).save(True)
        return res

    def test_connect(self, ip: str = "1********", port: int = 8088):
        invoker_addr = f"tcp://{ip}:{port}"
        return self.job_client.test_connect(invoker_addr)

    def get_version(self) -> str:
        res = require(self._system.query_version)()

        if res.get("code") == 200:
            return res["data"]["version"]

    def get_version_list(self):
        res = require(self._system.query_version)()

        if res.get("code") == 200:
            return res["data"]["version_list"]

    def check_version(self) -> int:
        # courier_version = self.get_version()
        # local_monster_version = get_version()
        # print(courier_version, local_monster_version)
        # return comparing_monster_version(local_monster_version, courier_version)

        monster_versions = self.get_version_list()
        local_monster_version = get_version()
        return 0 if monster_versions is not None and local_monster_version in monster_versions else 1

    def rasie_enforcement_error(self, api: str):
        """
        print local message and raise error.
        """
        print("|| enforcement error ||")
        print(
            f"the api:<<< {api} >>> will change invoker cache config.please check!!!",
            ENFORCEMENT_MEG,
        )
        print("invoker cached location:", DEFAULT_FOLDER)
        print("local env msg:", self.get_env())
        print("local token:", Credentials.read())
        sys.exit(1)
        # raise ModifyError()

    def set_callback(self, call_flag, callback_func=None, permission_callback_func=None, callback_args=None):
        """
        set invoker callback
        """
        if call_flag:
            CALLBACK["call_flag"] = True
        else:
            CALLBACK["call_flag"] = False

        CALLBACK["callback_func"] = callback_func
        CALLBACK["permission_callback_func"] = permission_callback_func
        CALLBACK["callback_args"] = callback_args
