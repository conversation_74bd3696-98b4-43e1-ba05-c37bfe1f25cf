# -*- coding: utf-8 -*-

# This code is part of pyqcat-legend.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an 'AS IS' BASIS
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/09/23
# __author:       <PERSON>
"""The Courier.
"""
# from orjson import orjson

from .json import loads, dumps
import os
from typing import Union
from .util import get_version, get_device
from prettytable import PrettyTable

try:
    from pyQCat.invoker import DEFAULT_FOLDER
    from pyQCat.invoker.const import update_cred_digit
except ImportError:
    DEFAULT_FOLDER = os.path.join(os.path.expanduser("~"), ".pyqcat")

DEFAULT_PYQCAT_TOKEN_PATH = os.path.join(DEFAULT_FOLDER, "invokerc")
DEFAULT_PYQCAT_ENV_PATH = os.path.join(DEFAULT_FOLDER, "invokenv")


def init_local():
    if not os.path.exists(DEFAULT_FOLDER):
        os.makedirs(DEFAULT_FOLDER)
    Credentials().save(update=False)
    Environ().save(update=False)


class Credentials:
    """
    Credentials Class.

    """

    __slots__ = ["_token", "user"]

    def __init__(self, token=None, user=None, **kwargs):
        self._token = None
        self.user = user
        self.token = token

    @property
    def token(self):
        if self._token is not None:
            return self._token
        else:
            return b""

    @token.setter
    def token(self, token: Union[str, bytes]):
        if token is None:
            return
        if isinstance(token, str):
            token = token.encode()
        self._token = token

    def __bool__(self):
        """
        just token is None, the cred is false, else True.
        Returns:

        """
        if self._token:
            return True
        return False

    def __json_encode__(self):
        return {
            "token": self._token.decode(),
            "user": self.user,
        }

    def __str__(self) -> str:
        return f"['{self.user}' || {self.token}]"

    @classmethod
    def read(cls):
        if not os.path.isfile(DEFAULT_PYQCAT_TOKEN_PATH):
            return cls()
        with open(DEFAULT_PYQCAT_TOKEN_PATH) as f:
            res = f.read()
            if res:
            # res = orjson.loads(res) if res else {}
                res = loads(res)
                return cls(**res)
            else:
                return cls()

    def save(self, update=True):
        if not os.path.isfile(DEFAULT_PYQCAT_TOKEN_PATH) or update:
            old = Credentials.read()
            if old.token != self.token:
                update_cred_digit()
            if self._token is not None:
                with open(DEFAULT_PYQCAT_TOKEN_PATH, "w+") as f:
                    # f.write(orjson.dumps(self.__json_encode__()).decode())
                    f.write(dumps(self))


class Environ:
    """
    the Environment variable class.
    use to save and show evnerions.
    """

    def __call__(self, *args, **kwargs):
        for key, val in kwargs.items():
            if val is not None:
                setattr(self, key, val)

    def __init__(
        self,
        invoker_addr: str = None,
        point_label: str = None,
        sample: str = None,
        env_name: str = None,
        **kwargs,
    ):
        self.invoker_addr = invoker_addr
        self.point_label = point_label
        self.sample = sample
        self.env_name = env_name
        self.version = get_version()
        self.device = get_device()
        for key, val in kwargs.items():
            if key not in ["version"]:
                setattr(self, key, val)

    def __bool__(self):
        if self.invoker_addr is None:
            return False
        return True

    def update(self, **kwargs):
        for key, value in kwargs.items():
            if hasattr(self, key) and value is not None:
                setattr(self, key, value)

    def __str__(self):
        table = PrettyTable()
        table.field_names = ["Item", "Value"]
        for key, val in self.__dict__.items():
            if val is None:
                val = ""
            table.add_row([str(key), str(val)])
        return "Invoker Environ\n" + str(table)

    def __json_encode__(self):
        return self.__dict__.copy()

    def __eq__(self, other):
        if isinstance(other, Environ):
            for key, value in other.__dict__.items():
                if key == "version":
                    continue

                if not (hasattr(self, key) and getattr(self, key) == value):
                    return False

            return True
        return False

    def env(self):
        env_dict = self.__dict__.copy()
        env_dict.pop("invoker_addr")
        # return orjson.dumps(env_dict)
        return dumps(env_dict).encode()

    def save(self, update=False):
        if not os.path.isfile(DEFAULT_PYQCAT_ENV_PATH) or update:
            if update and self == Environ.read():
                update_cred_digit()
            with open(DEFAULT_PYQCAT_ENV_PATH, "w+", encoding="utf-8") as f:
                # f.write(orjson.dumps(self.__json_encode__()).decode())
                f.write(dumps(self))

    @classmethod
    def read(cls):
        if not os.path.isfile(DEFAULT_PYQCAT_ENV_PATH):
            return cls()
        with open(DEFAULT_PYQCAT_ENV_PATH) as f:
            res = f.read()
            # res = orjson.loads(res) if res else {}
            res = loads(res)
            return cls(**res)
