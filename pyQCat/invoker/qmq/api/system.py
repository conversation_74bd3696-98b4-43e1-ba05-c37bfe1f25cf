from typing import Union

from .baseapi import ApiBase
from ..client import CourierClient


class System(ApiBase, prefix_url=b"/", default_client=CourierClient):
    URL_MAP = {
        "check_version": (b"version", b"get"),
        "courier_version": (b"version/courier", b"get"),
        "test_connect": (b"test/connect", b"get"),
        "test_token": (b"test/token", b"get"),
        "broadcast_chip": (b"system/chip/broadcast", b"post"),
        "broadcast_user": (b"system/user/broadcast", b"post"),
    }

    def query_version(self):
        return self.template_requirement("check_version")

    def courier_version(self):
        return self.template_requirement("courier_version")

    def test_connect(self):
        return self.template_requirement("test_connect")

    def broadcast_chip(self, state: Union[str, bytes], data: Union[str, bytes] = None):
        """
        send to courier for broadcast to chip
        Args:
            state: b"calibrate_start", b"calibrate_end", b"qs_state"
            data:

        Returns:

        """
        api_data = {
            "state": state,
            "data": data
        }
        return self.template_requirement("broadcast_chip", api_data)

    def broadcast_user(self, username: str, state: Union[str, bytes], data: Union[str, bytes] = None):
        """
        send to courier for broadcast to user
        Args:
            username:
            state:
            data:

        Returns:

        """
        api_data = {
            "username": username,
            "state": state,
            "data": data
        }
        return self.template_requirement("broadcast_user", api_data)

    def test_token(self, token: str):
        """
        send to courier for broadcast to user
        Args:
            token:

        Returns:

        """
        api_data = {
            "token": token,
        }
        return self.template_requirement("test_token", api_data)
