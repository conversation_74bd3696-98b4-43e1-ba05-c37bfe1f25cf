from .baseapi import ApiBase
from ..client import CourierClient
from typing import Union, List


class Permission(ApiBase, prefix_url=b"/perms/", default_client=CourierClient):
    URL_MAP = {
        "query_perms_list": (b"list", b"get"),
        "query_platform": (b"platform", b"get"),
        "add_platform": (b"platform", b"post"),
        "del_platform": (b"platform", b"delete"),
        "query_perms_group": (b"group", b"get"),
        "add_perms_group": (b"group", b"post"),
        "del_perms_group": (b"group", b"delete"),
        "query_perms_user": (b"user", b"get"),
        "add_perms_user": (b"user", b"post"),
        "del_perms_user": (b"user", b"delete"),
        "query_perms_note": (b"note", b"get"),
        "query_platform_perms": (b"platform/perms", b"get"),
        "operate_platform_perms": (b"platform/perms", b"post"),
    }

    def query_perms_list(self, group: str = None, username: str = None, platform_ident: str = None):
        """
        get permission for self
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = dict(group=group, username=username, platform_ident=platform_ident)
        return self.template_requirement("query_perms_list", api_data=api_data)

    def query_platform(
            self,
            page_num: int = 1,
            page_size: int = 20,
    ):
        """
        get platform of permission
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        return self.template_requirement("query_platform")

    def add_platform(self, sample: str, env_name: str):
        """
        add platform of permission
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = dict(sample=sample,
                        env_name=env_name)
        return self.template_requirement("add_platform", api_data=api_data)

    def del_platform(self, name: str, sample: str = None, env_name: str = None):
        """
        delete platform of permission(super)
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = dict(name=name,
                        sample=sample,
                        env_name=env_name)
        return self.template_requirement("del_platform", api_data=api_data)

    def query_perms_group(self, group: str = None, platform_ident: str = None):
        """
        query group of permission(super)
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = dict(group=group, platform_ident=platform_ident)
        return self.template_requirement("query_perms_group", api_data)

    def add_perms_group(self, perms_ids: Union[str, List], group: str, platform_ident: str = ""):
        """
        add group of permission(super)
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = dict(
            perms_ids=perms_ids,
            group=group,
            platform_ident=platform_ident,
        )
        return self.template_requirement("add_perms_group", api_data=api_data)

    def del_perms_group(self, perms_ids: Union[str, List], group: str, platform_ident: str = ""):
        """
        delete group of permission(super)
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = dict(
            perms_ids=perms_ids,
            group=group,
            platform_ident=platform_ident,
        )
        return self.template_requirement("del_perms_group", api_data=api_data)

    def query_perms_user(self, username: str = None, platform_ident: str = ""):
        """
        query user of permission(admin/super)
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = dict(username=username, platform_ident=platform_ident)
        return self.template_requirement("query_perms_user", api_data=api_data)

    def add_perms_user(self, perms_ids: Union[str, List], username: str, platform_ident: str = ""):
        """
        add user of permission(admin/super)
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = dict(
            perms_ids=perms_ids,
            username=username,
            platform_ident=platform_ident,
        )
        return self.template_requirement("add_perms_user", api_data=api_data)

    def del_perms_user(self, perms_ids: Union[str, List], username: str, platform_ident: str = ""):
        """
        delete user of permission(admin/super)
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = dict(
            perms_ids=perms_ids,
            username=username,
            platform_ident=platform_ident,
        )
        return self.template_requirement("del_perms_user", api_data=api_data)

    def query_perms_note(self, username: str = None,
                         types: str = None,
                         perms_id: str = None,
                         page_num: int = 1,
                         page_size: int = 10):
        """
        query change note of permission(admin/super)
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = dict(perms_id=perms_id,
                        types=types,
                        username=username,
                        page_num=page_num,
                        page_size=page_size)
        return self.template_requirement("query_perms_note", api_data=api_data)

    def query_platform_perms(self, perms_id: str = None,
                             perms_name: str = None,
                             perms_type: str = None):
        """
        query platform of permission
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = dict(perms_id=perms_id,
                        perms_name=perms_name,
                        perms_type=perms_type)
        return self.template_requirement("query_platform_perms", api_data=api_data)

    def operate_platform_perms(self, perms_id: str = None,
                               perms_name: str = None,
                               perms_type: str = None,
                               target: Union[str, List] = None):
        """
        operate platform of permission (group/username)
        target: usernames or group names
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = dict(perms_id=perms_id,
                        perms_name=perms_name,
                        perms_type=perms_type,
                        target=target)
        return self.template_requirement("operate_platform_perms", api_data=api_data)
