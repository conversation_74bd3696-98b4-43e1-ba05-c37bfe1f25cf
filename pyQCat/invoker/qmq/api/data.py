# from typing import Dict
from pyQCat.invoker.const import Dict
from .baseapi import ApiBase
from ..client import CourierClient
from typing import Union, List
from datetime import datetime


class Data(ApiBase, prefix_url=b"/data/", default_client=CourierClient):
    URL_MAP = {
        "data_for_test": (b"test/user", b"get"),
        "server_message": (b"server/message", b"post"),
    }

    def get_user_test(
        self,
        start_date: Union[datetime, str],
        end_date: Union[datetime, str] = None,
        username: str = None,
        aggregate: bool = False,
    ):
        """
        Args:
            start_date: start date.
            end_date:   end date, default is None, means same to start date.
            username:  default is None, means all user.
            aggregate: whether to aggregate date query.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        if isinstance(start_date, datetime):
            start_date = start_date.strftime("%Y-%m-%d")
        if isinstance(end_date, datetime):
            end_date = end_date.strftime("%Y-%m-%d")
        api_data = {
            "start_date": start_date,
            "end_date": end_date,
            "username": username,
            "aggregate": aggregate,
        }
        return self.template_requirement("data_for_test", api_data)

    def traceback_user_record(self, **kwargs):
        """
        Args:
            kwargs: level.time.message.version.

        Returns:
            data()
        """
        return self.template_requirement("server_message", api_data=kwargs)
