import pickle
from typing import Dict, Union

# import orjson
from pyQCat.invoker.qmq.client import Base<PERSON>lient
from typing import <PERSON>ple
from ..json import loads
from functools import wraps
import inspect

CALLBACK = {
    "call_flag": False,
    "callback_func": None,
    "callback_args": None,
    "permission_callback_func": None,
}


class ApiBase:
    """Base class for REST adapters."""

    URL_MAP = {}  # type: ignore[var-annotated]
    """Mapping between the internal name of an endpoint and the actual URL."""

    # _HEADER_JSON_CONTENT = {'Content-Type': 'application/json'}

    @classmethod
    def __init_subclass__(cls, **kwargs) -> None:
        super().__init_subclass__()
        if "prefix_url" in kwargs:
            setattr(cls, "prefix_url", kwargs["prefix_url"])
        if "default_client" in kwargs:
            setattr(cls, "default_client", kwargs["default_client"])

    def __init__(self, client: BaseClient = None) -> None:
        """RestAdapterBase constructor.

        Args:
            session: Client use to link to invoker.
            prefix_url: String to be prepend to all URLs.
        """
        self._client = client

    def get_url(self, identifier: str) -> Tuple[bytes, bytes]:
        """Return the resolved URL for the specified identifier.

        Args:
            identifier: Internal identifier of the endpoint.

        Returns:
            The resolved URL of the endpoint (relative to the session base URL).
        """
        url, method = self.URL_MAP[identifier]
        return self.prefix_url + url, method

    @property
    def client(self):
        """
        the api use client to send msg and recv.
        Returns:
            BaseClient or subclass obj.
        """
        if self._client:
            return self._client
        else:
            self._client = self.default_client()
            return self._client

    def template_requirement(
            self,
            identifier: str,
            api_data: Union[Dict, bytes] = None,
            token: bytes = None,
            time_out=None,
    ):
        """
        the requirement template, if the requirement not need another deal.
        Args:
            identifier: the url_map key.
            api_data: the data json you need send.
            token: the requir token.

        Returns:
            response, just like
            {
            "code": 200,
            "data": any,
            "msg": xxxxxx
            }
        """

        url, method = self.get_url(identifier)
        api_data = api_data if api_data is not None else {}
        api_data = self._client.encode(api_data)
        # use to adapter async and sync require.
        require_msg = {"url": url, "method": method, "token": token, "data": api_data}
        return self, require_msg, time_out


def require(require_func, db_async=False):
    def execute_callback(code, msg, url, method, require_id, token, args, kwargs):
        if not (code > 300 and CALLBACK["call_flag"]):
            return
        callback_op = None
        if code == 405 and CALLBACK["permission_callback_func"]:
            callback_op = "permission_callback_func"
        elif CALLBACK["callback_func"]:
            callback_op = 'callback_func'

        if callback_op:
            if CALLBACK["callback_args"]:
                CALLBACK[callback_op](*CALLBACK['callback_args'],
                                                     code=code,
                                                     msg=msg,
                                                     func_name=require_func.__name__,
                                                     url=url,
                                                     method=method,
                                                     token=token,
                                                     require_id=require_id, args=args,
                                                     kwargs=kwargs)
            else:
                CALLBACK[callback_op](code=code,
                                                     msg=msg,
                                                     func_name=require_func.__name__,
                                                     url=url,
                                                     method=method,
                                                     token=token,
                                                     require_id=require_id, args=args,
                                                     kwargs=kwargs)

    @wraps(require_func)
    def wrapper(*args, **kwargs):
        client, require_msg, time_out = require_func(*args, **kwargs)
        require_id = client._client.send(**require_msg)
        res = client._client.recv(require_id=require_id, time_out=time_out)
        res = loads(res[0].decode())
        execute_callback(code=res['code'], msg=res['msg'], url=require_msg.get("url", None), token=client._client.token,
                         method=require_msg.get("method", None), require_id=require_id, args=args,
                         kwargs=kwargs)
        return res

    @wraps(require_func)
    async def async_wrapper(*args, **kwargs):
        client, require_msg, time_out = require_func(*args, **kwargs)
        # require_id = client._client.async_send(**require_msg)
        require_id = client._client.send(**require_msg)
        res = await client._client.async_recv(require_id=require_id, time_out=time_out)
        res = loads(res[0].decode())
        execute_callback(code=res['code'], msg=res['msg'], url=require_msg.get("url", None), token=client._client.token,
                         method=require_msg.get("method", None), require_id=require_id, args=args,
                         kwargs=kwargs)
        return res

    if db_async:
        return async_wrapper
    else:
        return wrapper
