# from typing import Dict
from pyQCat.invoker.const import Dict
from .baseapi import ApiBase
from ..client import CourierClient


class Dag(ApiBase, prefix_url=b"/dag/", default_client=CourierClient):
    URL_MAP = {
        "get_list": (b"list", b"get"),
        "dag_details": (b"option", b"get"),
        "create_dag": (b"option", b"post"),
        "update_dag": (b"option", b"put"),
        "delete_dag": (b"option", b"delete"),
        "execute_dag": (b"execute", b"post"),
        "node_result_put": (b"result", b"post"),
        "dag_conf": (b"result/bit", b"post"),
        "get_history_list": (b"history/list", b"get"),
        "get_history": (b"history", b"get"),
        "reset_dag": (b"reset", b"post"),
        "query_custom_task": (b"custom/task", b"get"),
        "add_custom_task": (b"custom/task", b"post"),
        "update_custom_task": (b"custom/task", b"put"),
        "delete_custom_task": (b"custom/task", b"delete"),
        "query_custom_task_his": (b"custom/task/his", b"get"),
        "add_custom_task_his": (b"custom/task/his", b"post"),
        "update_custom_task_his": (b"custom/task/his", b"put"),
        "calibrate_start": (b"calibrate", b"post"),
        "calibrate_done": (b"calibrate", b"put"),
    }

    def query_dag_list(self):
        """
        get user dag list.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            if ok, the return data is list. such as:
             [
                {
                  "username": "A_9527",
                  "dag_name": "single qubit dag 0",
                  "execute_params": {
                    "is_traceback": false,
                    "is_report": false,
                    "ajust_params": {}
                  },
                  "official": true,
                  "create_time": "2022-09-10 10:07:44",
                  "id": "6327cef05fda5c2f52568bf4"
                }
              ]

        """
        return self.template_requirement("get_list")

    def query_dag_details(self, name: str):
        """
        query single dag details by dag name.
        Args:
            name: dag name.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data: {
                    "username": "A_9527",
                    "dag_name": "single qubit dag 0",
                    "node_edges": [],
                    "execute_params": {
                      "is_traceback": true,
                      "is_report": false,
                      "ajust_params": {}
                    },
                    "official": true,
                    "node_params": {},
                    "create_time": "2022-09-10 10:07:44",
                    "id": "6327cef05fda5c2f52568bf4"
                  }

        """
        api_data = {
            "dag_name": name
        }
        return self.template_requirement("dag_details", api_data)

    def create_dag(self, name: str, node_edges: Dict, execute_params: Dict, node_params: Dict):
        """
        create new dag.
        Args:
            name: dag name.
            node_edges: node e-v dict.
            execute_params: execute params{"is_traceback": bool, "is_report": bool, "ajust_params":dict}
            node_params: node params
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "dag_name": name,
            "node_edges": node_edges,
            "execute_params": execute_params,
            "node_params": node_params
        }
        return self.template_requirement("create_dag", api_data)

    def delete_dag(self, name: str):
        """
        delete normal dag.
        Args:
            name: dag name.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "dag_name": name
        }
        return self.template_requirement("delete_dag", api_data)

    # def update_dag(self, name: str, node_edges: Dict, execute_params: Dict, node_params: Dict):
    #     """
    #     update dag.
    #     Args:
    #         name: dag name.
    #         node_edges: node e-v dict.
    #         execute_params: execute params{"is_traceback": bool, "is_report": bool, "ajust_params":dict}
    #         node_params: node params
    #     Returns:
    #         the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
    #     """
    #     api_data = {
    #         "dag_name": name,
    #         "node_edges": node_edges,
    #         "execute_params": execute_params,
    #         "node_params": node_params
    #     }
    #     return self.template_requirement("update_dag", api_data)

    def execute_dag(self, name: str, node_edges: Dict, execute_params: Dict, node_params: Dict, is_save: bool,
                    conf_work: list = None):
        """
        update dag.
        Args:
            name: dag name.
            node_edges: node e-v dict.
            execute_params: execute params{"is_traceback": bool, "is_report": bool, "ajust_params":dict}
            node_params: node params
            is_save: bool
            conf_work: list, the dag env working volt, such as [["q0","DC",4.22],["q1","DC",4.12]]
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        if not conf_work:
            conf_work = []
        api_data = {
            "dag_name": name,
            "node_edges": node_edges,
            "execute_params": execute_params,
            "node_params": node_params,
            "is_save": is_save,
            "conf_work": conf_work,
        }
        return self.template_requirement("execute_dag", api_data)

    def put_node_result(self, id: str, node_id: str, result: Dict, loop_flag: bool = False):
        """

        Args:
            id:dag history id.
            node_id: the node id.
            result: the node result.
            loop_flag: the dag run end flag, if true, the dag execute end. default is False.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """

        api_data = {
            "id": id,
            "node_id": node_id,
            "result": result,
            "loop_flag": loop_flag,
        }

        return self.template_requirement("node_result_put", api_data)

    def query_dag_execute_history(self, dag_name: str, page_num: int = 1, page_size: int = 20):
        """
        Query DAG execution records.
        By default, the interface displays the latest 20 pieces of data,
        which can be used to query the previous records using page num and page size.

        Args:
            dag_name: DAG name.
            page_num: The number of query starting bars.
            page_size: The number of records per query.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data is a list, Per record is a dict such as :
            [
                {
                  "execute_params": {},
                  "official": false,
                  "dag_id": "6327cef05fda5c2f52568bf5",
                  "username": "xiao",
                  "dag_name": "single qubit dag",
                  "node_edges": {},
                  "node_params": {},
                  "node_result": {},
                  "dag_report": "",
                  "execute_node": ["A","B"],
                  "traceback_note": [ 0, 0],
                  "version": "0.0.1",
                  "create_time": "2022-09-19 10:07:54",
                  "id": "6327d0f573d591592b670dd5"
                }
              ]
        """
        api_data = {
            "dag_name": dag_name,
            "page_num": page_num,
            "page_size": page_size
        }
        return self.template_requirement("get_history_list", api_data)

    def query_dag_record(self, dag_name: str = None, dag_id: str = None):
        """
        Query DAG execution records.
        By default, the interface displays the latest 20 pieces of data,
        which can be used to query the previous records using page num and page size.

        Args:
            dag_name: DAG name.
            dag_id: DAG id.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data is a list, Per record is a dict such as :
                {
                  "execute_params": {},
                  "official": false,
                  "dag_id": "6327cef05fda5c2f52568bf5",
                  "username": "xiao",
                  "dag_name": "single qubit dag",
                  "node_edges": {},
                  "node_params": {},
                  "node_result": {},
                  "dag_report": "",
                  "execute_node": ["A","B"],
                  "traceback_note": [ 0, 0],
                  "version": "0.0.1",
                  "create_time": "2022-09-19 10:07:54",
                  "id": "6327d0f573d591592b670dd5"
                }
        """
        api_data = {
            "dag_name": dag_name,
            "dag_id": dag_id,
        }
        return self.template_requirement("get_history", api_data)

    def reset_user_standard_dag(self, dag_name: str = None, dag_id: str = None):
        """
        reset user standard dag struct and params to default standard.
        Args:
            dag_name: DAG name.
            dag_id: DAG id.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

        api_data = {
            "dag_name": dag_name,
            "dag_id": dag_id,
        }
        return self.template_requirement("reset_dag", api_data)

    def set_dag_history_conf(self, dag_id: str, conf_pre: Dict = None, conf_suf: Dict = None, conf_work: Dict = None):
        """
        set dag history config. use to report and dag execute.
        Args:
            dag_id: Dag history id.
            conf_pre: qubit info set before node execute.
            conf_suf: qubit info set after node execute.
            conf_work: wording dc set before node execute.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "dag_id": dag_id,
            "conf_pre": conf_pre,
            "conf_suf": conf_suf,
            "conf_work": conf_work,
        }

        return self.template_requirement("dag_conf", api_data)

    def query_custom_task(self, task_id: str = None,
                          task_name: str = None,
                          sub_type: str = None,
                          sub_name: str = None):
        """
        query custom task list
        Args:
            task_id: task id.
            task_name: task name.
            sub_type: task type: Dag/Exp
            sub_name: Dag name/ Exp name
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "task_id": task_id,
            "task_name": task_name,
            "sub_type": sub_type,
            "sub_name": sub_name,
        }

        return self.template_requirement("query_custom_task", api_data)

    def add_custom_task(self, task_data: Dict):
        """
        add custom task
        Args:
            task_data: task data.
                    {
                        "task_name": "interval 10 min",
                        "task_desc": "calibration per 10minute",
                        "username": "",
                        "policy":{
                          "type": "schedule/repeat",
                          "options":{}#实际运行的策略信息
                        },
                        "status": "stopped",
                        "sub_type": "",
                        "sub_name":"exp_name/dag_name"
                    }

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = task_data

        return self.template_requirement("add_custom_task", api_data)

    def update_custom_task(self, task_data: Dict):
        """
        update custom task
        Args:
            task_data: task data.
                    {
                        "task_name": "interval 10 min",
                        "task_desc": "calibration per 10minute",
                        "username": "",
                        "policy":{
                          "type": "schedule/repeat",
                          "options":{}#实际运行的策略信息
                        },
                        "status": "stopped",
                        "sub_type": "",
                        "sub_name":"exp_name/dag_name"
                    }

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = task_data

        return self.template_requirement("update_custom_task", api_data)

    def delete_custom_task(self, task_name: str):
        """
        delete custom task list
        Args:
            task_name: task name.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "task_name": task_name
        }

        return self.template_requirement("delete_custom_task", api_data)

    def add_custom_task_his(self, task_data: Dict, bit_data: Dict):
        """
        add custom task note
        Args:
            task_data: task data.
                    {
                        "task_name": "interval 10 min",
                        "task_desc": "calibration per 10minute",
                        "username": "",
                        "policy":{
                          "type": "schedule/repeat",
                          "options":{}#实际运行的策略信息
                        },
                        "status": "stopped",
                        "sub_type": "",
                        "sub_name":"exp_name/dag_name"
                    }
            bit_data: {
                        "qubits":["q0", "q1", "q2", "q3"],
                        "couplers":["c0-1", "c11-12", c"2-3"],
                        "pairs": ["q0q1", "q2q3"],
                        "discriminators":["q0", "q1", "q2", "q3"],
                        "compensates":["q0", "q1", "q2", "q3"],
                        }

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            {"doc_id": "id"}
        """
        api_data = {
            "task_data": task_data,
            "bit_data": bit_data,
        }

        return self.template_requirement("add_custom_task_his", api_data)

    def update_custom_task_his(self, doc_id: str, task_data: Dict = None, bit_data: Dict = None):
        """
        update custom task note done
        Args:
            doc_id: task id
            task_data: task data. default to None, not update.
                    {
                        "task_name": "interval 10 min",
                        "task_desc": "calibration per 10minute",
                        "username": "",
                        "policy":{
                          "type": "schedule/repeat",
                          "options":{}#实际运行的策略信息
                        },
                        "status": "stopped",
                        "sub_type": "",
                        "sub_name":"exp_name/dag_name"
                    }
            bit_data: default to None, not update.
                    {
                        "qubits":["q0", "q1", "q2", "q3"],
                        "couplers":["c0-1", "c11-12", c"2-3"],
                        "pairs": ["q0q1", "q2q3"],
                        "discriminators":["q0", "q1", "q2", "q3"],
                        "compensates":["q0", "q1", "q2", "q3"],
                        }

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "doc_id": doc_id,
            "task_data": task_data,
            "bit_data": bit_data,
        }

        return self.template_requirement("update_custom_task_his", api_data)

    def query_custom_task_his(self, doc_id: str = None,
                              task_name: str = None,
                              sub_type: str = None,
                              sub_name: str = None,
                              sample: str = None,
                              env_name: str = None,
                              point_label: str = None,
                              ):
        """
        query custom task list for history
        Args:
            doc_id: task id.
            task_name: task name.
            sub_type: task type: Dag/Exp
            sub_name: Dag name/ Exp name
            sample:
            env_name:
            point_label:
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "doc_id": doc_id,
            "task_name": task_name,
            "sub_type": sub_type,
            "sub_name": sub_name,
            "sample": sample,
            "env_name": env_name,
            "point_label": point_label,
        }

        return self.template_requirement("query_custom_task_his", api_data)
