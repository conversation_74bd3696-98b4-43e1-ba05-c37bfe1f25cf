from datetime import datetime
from typing import List, Union
from pyQCat.invoker.const import Dict

from ..client import CourierClient
from .baseapi import ApiBase


class Chip(ApiBase, prefix_url=b"/chip/", default_client=CourierClient):
    URL_MAP = {
        "chip_info": (b"line", b"get"),
        "create_chip_line": (b"line", b"post"),
        "sync_chip_line": (b"line", b"put"),
        "sync_chip_line_new": (b"line/sync", b"put"),
        "config_list": (b"config/list", b"get"),
        "config_details": (b"config", b"get"),
        "init_config": (b"config", b"post"),
        "update_config": (b"config", b"put"),
        "update_config_list": (b"config/list", b"put"),
        "q_o_u_config": (b"other_config", b"get"),
        "c_o_u_config": (b"other_config", b"post"),
        "init_qubit": (b"bit", b"post"),
        "query_bit": (b"bit", b"get"),
        "query_bit_list": (b"bit/list", b"get"),
        "update_bit": (b"bit", b"put"),
        "update_bit_list": (b"bit/list", b"put"),
        "query_bit_history": (b"bit/history", b"get"),
        "query_bit_attr_history": (b"bit/history/attr", b"get"),
        "query_all_chip": (b"conf/all", b"get"),
        "query_all_config": (b"conf/all/config", b"get"),
        "query_all_bit": (b"conf/all/bit", b"get"),
        "query_all_chip_simple": (b"conf/all/simple", b"get"),
        "query_sample_list": (b"sample/list", b"get"),
        "query_env_name_list": (b"env_name/list", b"get"),
        "query_point_label_list": (b"point_label/list", b"get"),
        "query_conf_type_list": (b"conf_type/list", b"get"),
        "query_chip": (b"struct", b"get"),
        "create_chip": (b"struct", b"post"),
        "update_chip": (b"struct", b"put"),
        "delete_chip": (b"struct", b"delete"),
        "query_workspace": (b"work_space", b"get"),
        "update_workspace": (b"work_space", b"post"),
        "delete_workspace": (b"work_space", b"delete"),
        "workspace_info": (b"work_space/info", b"get"),
        "save_workspace_info": (b"work_space/info", b"post"),
        "add_workspace_info": (b"work_space/info", b"put"),
        "delete_workspace_info": (b"work_space/info", b"delete"),
        "copy_workspace": (b"work_space/copy", b"post"),
        "workspace_set_auto": (b"work_space/option", b"post"),
        "workspace_pull_push": (b"work_space/option", b"put"),
        "control_qs_server": (b"struct/option", b"post"),
        "sync_qs_status": (b"struct/option", b"put"),
        "control_qs_server_start": (b"struct/start", b"post"),
        "control_qs_server_stop": (b"struct/stop", b"post"),
        "control_qs_server_force_stop": (b"struct/force_stop", b"post"),
        "control_qs_server_reconnect": (b"struct/reconnect", b"post"),
        "control_qs_server_restart": (b"struct/restart", b"post"),
        "chip_sample_data": (b"chip/sample_data", b"get"),
        "space_sample_data": (b"space/sample_data", b"get"),
        "chip_line_space": (b"line/space", b"get"),
        "query_workspace_his": (b"work_space/his", b"get"),
        "query_storm_list": (b"storm", b"get"),
        "update_storm": (b"storm", b"post"),
        "delete_storm": (b"storm", b"delete"),
        "control_storm": (b"storm/option", b"post"),
        "query_storm_sample_data": (b"storm/sample_data", b"get"),
        "query_storm_conf": (b"storm/info", b"get"),
        "update_storm_conf": (b"storm/info", b"put"),
        "revert_bit": (b"bit/revert", b"post"),
        "query_revert_bits": (b"bits/revert", b"get"),
        "revert_more_bits": (b"bits/revert", b"post"),
        "query_chip_env_id": (b"env/id", b"get"),
    }

    def query_chip_line(self):
        """
        get chip info that search by sample. sample set with INVOKER env.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            if os, return chip info, include sample, env_name, qubit and couple wiring information.
            such as:
            {
                "sample": "fivago",
                "env_name": "D5_env_0824",
                "QubitCount": 2,
                "CouplerCount": 1,
                "QubitParams": {
                  "q0": {
                    "xy_channel": 5,
                    "z_dc_channel": 1,
                    "z_flux_channel": 1,
                    "readout_channel": 1,
                    "probe_freq": 6546.2,
                    "sample_delay": 500,
                    "sample_width": 500
                  },
                  "q1": {
                    "xy_channel": 6,
                    "z_dc_channel": 2,
                    "z_flux_channel": 2,
                    "readout_channel": 1,
                    "probe_freq": 6401.6,
                    "sample_delay": 500,
                    "sample_width": 500
                  }
                },
                "CouplerParams": {
                  "c0": {
                    "drive_bit": 0,
                    "probe_bit": 1,
                    "z_dc_channel": 5,
                    "z_flux_channel": 5,
                    "probe_pi_width": 180,
                    "probe_pi_offset": 10
                  }
                }
              }

        """
        return self.template_requirement("chip_info")

    def create_chip_line(self, chip_line_dict: Dict):
        """
        create chip by chip_line_dict.
        Args:
            chip_line_dict:{
                "sample": str,
                "env_name": str,
                "QubitCount": str,
                "CouplerCount": str,
                "QubitParams": Dict,
                "CouplerParams": Dict
            }

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        return self.template_requirement("create_chip_line", chip_line_dict)

    def sync_chip_line(self):
        """
        sync chip line channel.
        Args:

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        return self.template_requirement("sync_chip_line")

    def sync_chip_line_new(self):
        """
        sync chip line channel.
        Args:

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        return self.template_requirement("sync_chip_line_new")

    def query_data_names(self):
        """
        Query user base data names, normal four data type.
        `Qubit`, `Coupler`, `QubitPair`, `Config`
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data:{
                "Qubit": ["q0", "q1", "q2", "q3" ],
                "Coupler": ["c0",  "c1",  "c2"  ],
                "QubitPair": ["q0q1"],
                "Config": ["distortion_q0.dat","q0.bin","crosstalk.json","character.json","instrument.json"]
              }

        """
        return self.template_requirement("config_list")

    def query_config(self, config_file_name: Union[List[str], str]):
        """
        get config details file,
        Args:
            config_file_name: the config file name, if need file > 1, input filename list.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        api_data = {"filename": config_file_name}
        return self.template_requirement("config_details", api_data)

    def init_base_qubit_data(
        self,
        params: Dict,
        bit_data: Dict = None,
        bit_names: List = None,
        delete: bool = False,
    ):
        """
        Initial user base data, by chip_line data.
        Args:
            params: the chip qubit freq and other params, such as
                    {"xy_baseband_freq": 1050, "m_baseband_freq": 1200}
            bit_data: init base qubit data {"qubit_fmt": {}, "coupler_fmt": {}}
            bit_names: init qubit names, if None: all bits
            delete: whether is init after delete

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        api_data = {
            "params": params,
            "bit_data": bit_data,
            "bit_names": bit_names,
            "delete": delete,
        }
        return self.template_requirement("init_qubit", api_data)

    def init_config_data(
        self,
        params: Dict,
        instrument_data: Dict,
        base_qubit_names: list = None,
        delete: bool = False,
    ):
        """
        Initial user base data, by chip_line data.
        Args:
            params: the chip qubit freq and other params, such as {"xy_baseband_freq": 1050, "m_baseband_freq": 1200}
            instrument_data: Chip measurement all-in-one machine data.
            base_qubit_names: list of qubits name, default None: all bits.
            delete: whether delete config and create new.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        api_data = {
            "params": params,
            "instrument_data": instrument_data,
            "base_qubit_names": base_qubit_names,
            "delete": delete,
        }
        return self.template_requirement("init_config", api_data)

    def update_single_config(
        self,
        file_name: str,
        file_data: Union[bytes, Dict],
        bin_abbr: Union[bytes, Dict] = None,
    ):
        """
        update config file, just could update or put one file pre time.
        Args:
            file_name: the config file name.
            file_data: the config file data, usually is bytes.
            bin_abbr: file data abbreviation

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {"filename": file_name, "file_data": file_data, "bin_abbr": bin_abbr}
        return self.template_requirement("update_config", api_data)

    def update_many_config(self, conf_data: list):
        """
        update config file, just could update or put one file pre time.
        Args:
            conf_data: the config list [{filename: "", "file_data": ...}].

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "conf_data": conf_data,
        }
        return self.template_requirement("update_config_list", api_data)

    def query_other_user_data(
        self,
        username: str,
        type_name: str = None,
        sample: str = None,
        env_name: str = None,
        point_label: str = None,
    ):
        """
        Query other base data, by type_name.
        Args:
            username: prepare query the username.
            type_name: the config type, must in ["BaseQubit", "Config"], if None: all type
            sample (str):
            env_name (str):
            point_label (str):

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        if type_name and type_name not in ["BaseQubit", "Config"]:
            print(f"type_name:{type_name!r} not support.")
        api_data = {
            "other_name": username,
            "type_name": type_name,
            "sample": sample,
            "env_name": env_name,
            "point_label": point_label,
        }
        return self.template_requirement("q_o_u_config", api_data)

    def copy_other_user_data(
        self,
        from_user: str,
        from_sample: str,
        from_env_name: str,
        from_point_label: str,
        local: bool = True,
        to_user: str = None,
        to_sample: str = None,
        to_env_name: str = None,
        to_point_label: str = None,
        element_names: List[str] = None,
        element_configs: List[str] = None,
        copy_qubit: bool = True,
    ):
        """
        copy another user config to self database.
        Args:
            from_user(str): from user`s username.
            from_sample(str): from sample name.
            from_env_name(str): from env_name name.
            from_point_label(str): from point_label name.
            local(bool): if True, copy data to local env and to_user、to_sample、
                        to_env_name、to_point_label default to myself local env data.
            to_user(str): copy to user`s username, if local is True, default=None: local user.
            to_sample(str): copy to sample, if local is True, default=None: local sample.
            to_env_name(str): copy to env_name, if local is True, default=None: local env_name.
            to_point_label(str): copy to point_label, if local is True, default=None: local point_label.
            element_names: the config params with type if None: all bits.
            element_configs: the config params with type if None: not copy config everything.
                             option from [character,crosstalk,distortion,bin,instrument,union_readout].
                             The bin option is very special, and when selected, copies all bits of bin data.
            copy_qubit(bool): whether copy qubits
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        # if type_name and type_name not in ["BaseQubit", "Config"]:
        #     print(f"type_name:{type_name!r} not support.")
        # api_data = {
        #     "other_name": username,
        #     "type_name": type_name,
        #     "element_names": element_names,
        #     "sample": sample,
        #     "env_name": env_name,
        #     "point_label": point_label,
        # }
        api_data = {
            "from_user": from_user,
            "from_sample": from_sample,
            "from_env_name": from_env_name,
            "from_point_label": from_point_label,
            "local": local,
            "to_user": to_user,
            "to_sample": to_sample,
            "to_env_name": to_env_name,
            "to_point_label": to_point_label,
            "element_names": element_names,
            "element_configs": element_configs,
            "copy_qubit": copy_qubit,
        }
        return self.template_requirement("c_o_u_config", api_data, time_out=20)

    def query_qcomponent(self, name: str = None, bit_id: str = None):
        """
        get the user bit params that search with point_label, sample and user.
        Args:
            name: the bit number. such as q1, c1
            bit_id: the bit id. such as "63f32eb80a8544e30801aa71"

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            if ok, return qubit dict, such as {q1}
            data = {
                      "name": "q1",
                      "sample": "fivago",
                      "username": "A_9527",
                      "point_label": "freestyle",
                      "bit_type": "Qubit",
                      "parameters": {
                        "xy_channel": 6,
                        "z_dc_channel": 2,
                        "z_flux_channel": 2,
                        "readout_channel": 1,
                        "probe_freq": 6401.6,
                        "sample_delay": 500,
                        "sample_width": 500,
                        "XYwave": {
                          "drive_IF": 566.667
                        }
                      },
                      "create_time": "2022-09-26 20:58:25",
                      "id": "6331a1f146391b743245daec"
                    }
        """
        if name is not None and bit_id is None:
            if len(name) > 16:
                name, bit_id = bit_id, name
        api_data = {
            "bit_name": name,
            "bit_id": bit_id,
        }
        return self.template_requirement("query_bit", api_data)

    def query_qcomponent_list(self, name_list: List[str], chip_type: int = 6):
        """
        get the user bit params that search with point_label, sample and user.
        Args:
            name_list: the bit number list. such as [q1, c1, q2]
            chip_type: user to query qubits info when the name_list is None or [], default 6 bit chip.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            if ok, return qubit info list, such as [{q1}, {c1}, {q2}]
            data = [{
                      "name": "q1",
                      "sample": "fivago",
                      "username": "A_9527",
                      "point_label": "freestyle",
                      "bit_type": "Qubit",
                      "parameters": {
                        "xy_channel": 6,
                        "z_dc_channel": 2,
                        "z_flux_channel": 2,
                        "readout_channel": 1,
                        "probe_freq": 6401.6,
                        "sample_delay": 500,
                        "sample_width": 500,
                        "XYwave": {
                          "drive_IF": 566.667
                        }
                      },
                      "create_time": "2022-09-26 20:58:25",
                      "id": "6331a1f146391b743245daec"
                    }
                    ]
        """
        if name_list is None:
            name_list = []
        if not isinstance(name_list, list):
            name_list = [name_list]
        api_data = {"bit_name": name_list, "chip_type": chip_type}
        return self.template_requirement("query_bit_list", api_data)

    def update_qcomponent(self, name: str, params: Dict, update_list: list = None,
                          record_id: str = "",
                          source: Dict = None):
        """
        update single qubit parameters, pre time just put one qubit.
        Args:
            name: bit name, such as "q1", "c1".
            params: qubits params.
            update_list: need update params to database, such as ["XY_Wave.Xpi", "probe_power"]
            record_id(str): experiment record id
            source(Dict): change source data

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        api_data = {
            "bit_name": name,
            "parameters": params,
            "record_id": record_id,
            "source": source,
        }
        if update_list:
            api_data.update({"update_list": update_list})
        return self.template_requirement("update_bit", api_data)

    def update_qcomponent_list(self, bits: list, update_list: List = None,
                               record_id: str = "",
                               source: Dict = None):
        """
        update many bits parameters, pre time just put one qubit.
        Args:
            bits(list[dict]): [{
                                "name": “”,
                                "parameters":{}
                                }]
            update_list(List(str)): update parameter`s name
            record_id(str): experiment record id
            source(Dict): change source data

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        api_data = {
            "bits": bits,
            "update_list": update_list,
            "record_id": record_id,
        }
        return self.template_requirement("update_bit_list", api_data)

    def query_qcomponent_history(
        self,
        name: str,
        username: str = None,
        sample: str = None,
        env_name: str = None,
        point_label: str = None,
        page_num: int = 1,
        page_size: int = 10,
    ):
        """
         query bit history.
        Args:
            name: the query bit name.
            username: str username, default None, find self config.
            sample: str sample, default None, find self config.
            env_name: str env_name, default None, find self config.
            point_label: str point_label, default None, find self config.
            page_num: The number of query starting bars.
            page_size: The number of records per query.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data is a list, per bit record is a dict, such as:
            [
                {
                  "name": "q0",
                  "sample": "fivago",
                  "username": "xiao",
                  "point_label": "freestyle",
                  "bit_type": "Qubit",
                  "parameters": {
                    "bit": 0,
                    "name": "q0",
                    "sample": "fivago",
                    "point_label": "freestyle",
                    "tunable": true,
                    "goodness": false,
                    "drive_freq": 5550.128,
                    "drive_power": -21.3,
                    "probe_freq": 6545.208,
                    "probe_power": -20,
                    "tls_freq": 6000,
                    "anharmonicity": 250,
                    "dc": 0.744732,
                    "dc_max": 0.740594,
                    "dc_min": -0.221014,
                    "ac": 0,
                    "T1": 11778.8,
                    "T2": 17954.03319439,
                    "z_flux_channel": 1,
                    "z_dc_channel": 1,
                    "update_time": null,
                    "idle_point": 0,
                    "_row": null,
                    "_col": null,
                    "xy_channel": 5,
                    "readout_channel": 1,
                    "sample_delay": 500,
                    "sample_width": 2850,
                    "XYwave": {
                      "Xpi": 0.5,
                      "Xpi2": 0.33165,
                      "Ypi": 1,
                      "Ypi2": 0.5,
                      "Zpi": 1,
                      "drive_IF": 566.667,
                      "delta": -240,
                      "detune_pi": -7,
                      "detune_pi2": -7,
                      "alpha": 1,
                      "offset": 5,
                      "time": 20
                    },
                    "Zwave": {
                      "width": 1000,
                      "amp": 1
                    },
                    "Mwave": {
                      "width": 4000,
                      "amp": 0.7,
                      "IF": 600
                    },
                    "union_readout": {
                      "width": 4000,
                      "amp": 0.7,
                      "index": [],
                      "probe_IF": 578.65
                    },
                    "readout_point": {
                      "amp": 0,
                      "sigma": 1.25,
                      "buffer": 5
                    }
                  },
                  "create_time": "2022-10-11 10:29:39",
                  "id": "6344d5134ccd5279f4112b50"
                }]
        """

        api_data = {
            "bit_name": name,
            "username": username,
            "sample": sample,
            "env_name": env_name,
            "point_label": point_label,
            "page_num": page_num,
            "page_size": page_size,
        }
        return self.template_requirement("query_bit_history", api_data)

    def query_chip_all(
        self,
        name: Union[str, list] = None,
        username: str = None,
        sample: str = None,
        env_name: str = None,
        point_label: str = None,
        qid: str = None,
    ):
        """
        query chip all config and bit.
        This interface is used to query other people's chip configuration information and parameters.
        Args:
            name: str config name, such as q1, crosstalk,default None, find all.
            username: str username, default None, find self config.
            sample: str sample name, default None, find all.
            point_label: str point_label, default None, find all.
            env_name: str env name, default None, find all.
            qid: qubit id or coupler id
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            return a dict, the key is configfile name, such as :
            {
            "q0": ...,
            "distortion_q0.dat": ...,
            }
        """

        api_data = {
            "name": name,
            "qid": qid,
            "username": username,
            "sample": sample,
            "point_label": point_label,
            "env_name": env_name,
        }
        return self.template_requirement("query_all_chip", api_data, time_out=15)

    def query_chip_all_bit(
        self,
        name: Union[str, list] = None,
        username: str = None,
        sample: str = None,
        env_name: str = None,
        point_label: str = None,
        qid: str = None,
    ):
        """
        query chip all bit.
        This interface is used to query other people's chip configuration information and parameters.
        Args:
            name: str config name, such as q1, crosstalk,default None, find all.
            username: str username, default None, find self config.
            sample: str sample name, default None, find all.
            point_label: str point_label, default None, find all.
            env_name: str env name, default None, find all.
            qid: qubit id or coupler id
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            return a dict, the key is configfile name, such as :
            {
            "q0": ...,
            "distortion_q0.dat": ...,
            }
        """

        api_data = {
            "name": name,
            "qid": qid,
            "username": username,
            "sample": sample,
            "point_label": point_label,
            "env_name": env_name,
        }
        return self.template_requirement("query_all_bit", api_data)

    def query_bit_attr_history(
        self,
        name: Union[str, List],
        attr: Union[str, List],
        time_start: Union[str, datetime] = None,
        time_end: Union[str, datetime] = None,
        value_min: Union[int, float] = None,
        value_max: Union[int, float] = None,
        step: Union[int, str] = None,
        origin: bool = None,
    ):
        """

        Args:
            name:   qubit/pair name, support many bits, such as "q0,q1" or ["q0", "q1"]
            attr:   qubit/pair only one attribute. Note if it is an attribute under the secondary menu,
                    such as: "dc_min" "dc_max" "XYwave.Xpi" "f12_options.delta" "Zwave.width"
            time_start:  start time, "2023-09-05 00:00:00"
            time_end:    end time, "2023-09-05 00:00:00"
            value_min:   value minimum
            value_max:   value maximum
            step:  Filter step size, unit: minute
            origin: whether return origin data, if True: Filters such as value_min, value_max, and step become invalid.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data is a dict, per attr record is a dict, such as:
            {
                "probe_freq": {
                    "q41": {
                        "x_data": [...],
                        "y_data": [...],
                        "x_label": "Datetime"，
                        "y_label": "probe_freq"
                    },
                    "q59": {
                        "x_data": [...],
                        "y_data": [...],
                        "x_label": "Datetime"，
                        "y_label": "probe_freq"
                    }
                },
                "dc_max": {
                    "q41": {
                        "x_data": [...],
                        "y_data": [...],
                        "x_label": "Datetime"，
                        "y_label": "probe_freq"
                    },
                    "q59": {
                        "x_data": [...],
                        "y_data": [...],
                        "x_label": "Datetime"，
                        "y_label": "probe_freq"
                    }
            }
        """
        api_data = {
            "name": name,
            "attr": attr,
            "time_start": time_start,
            "time_end": time_end,
            "value_min": value_min,
            "value_max": value_max,
            "step": step,
            "origin": origin,
        }
        return self.template_requirement("query_bit_attr_history", api_data)

    def query_chip_all_config(
        self,
        name: Union[str, list] = None,
        username: str = None,
        sample: str = None,
        env_name: str = None,
        point_label: str = None,
        qid: str = None,
    ):
        """
        query chip all config.
        This interface is used to query other people's chip configuration information and parameters.
        Args:
            name: str config name, such as q1, crosstalk,default None, find all.
            username: str username, default None, find self config.
            sample: str sample name, default None, find all.
            point_label: str point_label, default None, find all.
            env_name: str env name, default None, find all.
            qid: qubit id or coupler id
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            return a dict, the key is configfile name, such as :
            {
            "q0": ...,
            "distortion_q0.dat": ...,
            }
        """

        api_data = {
            "name": name,
            "qid": qid,
            "username": username,
            "sample": sample,
            "point_label": point_label,
            "env_name": env_name,
        }
        return self.template_requirement("query_all_config", api_data, time_out=15)

    def query_chip_all_simple(
        self,
        name: Union[str, list] = None,
        username: str = None,
        sample: str = None,
        env_name: str = None,
        point_label: str = None,
        qid: str = None,
    ):
        """
        query chip all config and bits simple info, such as [id,name,create_time/update_time].
        This interface is used to query other people's chip configuration information and parameters.
        Args:
            name: str config name, such as q1, crosstalk,default None, find all.
            username: str username, default None, find self config.
            sample: str sample name, default None, find all.
            point_label: str point_label, default None, find all.
            env_name: str env name, default None, find all.
            qid: qubit id or coupler id
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            return a dict, the key is configfile name, such as :
            {
            "q0": ...,
            "distortion_q0.dat": ...,
            }
        """

        api_data = {
            "name": name,
            "qid": qid,
            "username": username,
            "sample": sample,
            "point_label": point_label,
            "env_name": env_name,
        }
        return self.template_requirement("query_all_chip_simple", api_data)

    def query_sample_list(self, username: str):
        """
        query all samples for one user
        Args:
            username: the user username.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "username": username,
        }
        return self.template_requirement("query_sample_list", api_data)

    def query_env_name_list(self, username: str, sample: str):
        """
        query all env_names for user and sample
        Args:
            username: the user username.
            sample: str sample name
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "username": username,
            "sample": sample,
        }
        return self.template_requirement("query_env_name_list", api_data)

    def query_point_label_list(self, username: str, sample: str, env_name: str):
        """
        query all env_names for user and sample and env_name
        Args:
            username: the user username.
            sample: str sample name
            env_name: str env name
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "username": username,
            "sample": sample,
            "env_name": env_name,
        }
        return self.template_requirement("query_point_label_list", api_data)

    def query_conf_type_list(self):
        """
        Query the type of the config file.
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
            data: ["character", "crosstalk", "distortion", "bin"]
        """
        api_data = {}
        return self.template_requirement("query_conf_type_list", api_data)

    def query_chip(
        self,
        groups: str = None,
        sample: str = None,
        env_name: str = None,
        show_all: bool = False,
    ):
        """
        Query the chip
        Args:
            groups: the user group.
            sample: str sample name
            env_name: str sample name
            show_all: show all chip
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """
        api_data = {
            "groups": groups,
            "sample": sample,
            "env_name": env_name,
            "show_all": show_all,
        }
        return self.template_requirement("query_chip", api_data)

    def create_chip(
        self,
        sample: str,
        env_name: str,
        inst_ip: str,
        inst_port: Union[int, str] = 27017,
        groups: str = None,
        core_num: Union[int, str] = 1,
        debug: Union[int, str] = 0,
        window_size: Union[int, str] = 10,
        alert_dis: Union[int, str] = 1,
        secure_dis: Union[int, str] = 2,
        **kwargs,
    ):
        """
        create the chip
        Args:
            groups: the user group.
            sample: str sample name
            env_name: str sample name
            inst_ip: str qstream db ip
            inst_port: str qstream db port
            core_num: thread core num
            debug: whether it use debug
            window_size:
            alert_dis:
            secure_dis:
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """
        api_data = {
            "groups": groups,
            "sample": sample,
            "env_name": env_name,
            "inst_ip": inst_ip,
            "inst_port": inst_port,
            "core_num": core_num,
            "debug": debug,
            "window_size": window_size,
            "alert_dis": alert_dis,
            "secure_dis": secure_dis,
        }
        api_data.update(kwargs)
        return self.template_requirement("create_chip", api_data)

    def update_chip(
        self,
        sample: str,
        env_name: str,
        inst_ip: str,
        inst_port: int = 27017,
        groups: str = None,
        core_num: int = 1,
        debug: int = 0,
        window_size: int = 10,
        alert_dis: int = 1,
        secure_dis: int = 2,
        **kwargs,
    ):
        """
        update the chip
        Args:
            groups: the user group.
            sample: str sample name
            env_name: str sample name
            inst_ip: str qstream db ip
            inst_port: str qstream db port
            core_num: thread core num
            debug: whether it use debug
            window_size:
            alert_dis:
            secure_dis:
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """
        api_data = {
            "groups": groups,
            "sample": sample,
            "env_name": env_name,
            "inst_ip": inst_ip,
            "inst_port": inst_port,
            "core_num": core_num,
            "debug": debug,
            "window_size": window_size,
            "alert_dis": alert_dis,
            "secure_dis": secure_dis,
        }
        api_data.update(kwargs)
        return self.template_requirement("update_chip", api_data)

    def delete_chip(self, sample: str, env_name: str):
        """
        delete the chip
        Args:
            sample: str sample name
            env_name: str sample name
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """
        api_data = {
            "sample": sample,
            "env_name": env_name,
        }
        return self.template_requirement("delete_chip", api_data)

    def query_workspace(
        self, username: str = None, sample: str = None, env_name: str = None
    ):
        """
        Query user workspace
        Args:
            username: the user group.
            sample: str sample name
            env_name: str sample name
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """
        api_data = {
            "username": username,
            "sample": sample,
            "env_name": env_name,
        }
        return self.template_requirement("query_workspace", api_data)

    def update_workspace(
        self,
        username: str,
        sample: str,
        env_name: str,
        bit_names: List,
        conf_names: List,
    ):
        """
        create/update user workspace
        Args:
            username: the user group.
            sample: str sample name
            env_name: str sample name
            bit_names: str qubit names
            conf_names: str config names
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """
        api_data = {
            "username": username,
            "sample": sample,
            "env_name": env_name,
            "bit_names": bit_names,
            "conf_names": conf_names,
        }
        return self.template_requirement("update_workspace", api_data)

    def delete_workspace(
        self, username: str = None, sample: str = None, env_name: str = None
    ):
        """
        delete user workspace
        Args:
            username: the user group.
            sample: str sample name
            env_name: str sample name
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """
        api_data = {
            "username": username,
            "sample": sample,
            "env_name": env_name,
        }
        return self.template_requirement("delete_workspace", api_data)

    def workspace_info(self):
        """
        Query self workspace info
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """
        api_data = {}
        return self.template_requirement("workspace_info", api_data)

    def workspace_set_auto(self, auto_push: int = None, auto_pull: int = None):
        """
        setting auto pull/push option
        Args:
            auto_push(int): 0 / 1 / None
            auto_pull(int): 0 / 1 / None
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """
        api_data = {"auto_push": auto_push, "auto_pull": auto_pull}
        return self.template_requirement("workspace_set_auto", api_data)

    def workspace_pull_push(self, option: Union[bool, int, str] = None):
        """
        pull/push user workspace qubits or configs
        Args:
            option(bool, int, str): if option: pull else False(except None)
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """
        api_data = {"option": option}
        return self.template_requirement("workspace_pull_push", api_data)

    def control_qs_server(self, sample: str, env_name: str, option: str):
        """
        start/stop/restart qs server
        Args:
            sample(str):
            env_name(str):
            option(str): start/stop/restart
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """
        api_data = {
            "sample": sample,
            "env_name": env_name,
            "option": option,
        }
        if option == "start":
            return self.template_requirement("control_qs_server_start", api_data)
        elif option == "stop":
            return self.template_requirement("control_qs_server_stop", api_data)
        elif option == "force_stop":
            return self.template_requirement("control_qs_server_force_stop", api_data)
        elif option == "reconnect":
            return self.template_requirement("control_qs_server_reconnect", api_data)
        elif option == "restart":
            return self.template_requirement("control_qs_server_restart", api_data)

    def sync_qs_status(self, sample: str = None, env_name: str = None):
        """
        sync qs status
        Args:
            sample(str):
            env_name(str):
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """
        api_data = {
            "sample": sample,
            "env_name": env_name,
        }
        return self.template_requirement("sync_qs_status", api_data)

    def query_chip_sample_data(self):
        """
        query chip data for sample/env_name data
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """
        api_data = {}
        return self.template_requirement("chip_sample_data", api_data)

    def query_chip_line_space(self, sample: str, env_name: str):
        """
        query qubit/config for chip_line data
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """
        api_data = {
            "sample": sample,
            "env_name": env_name,
        }
        return self.template_requirement("chip_line_space", api_data)

    def query_workspace_his(
        self,
        username: str = None,
        sample: str = None,
        env_name: str = None,
        name: str = None,
        page_num: int = 1,
        page_size: int = 10,
    ):
        """
        query workspace history
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """
        api_data = {
            "username": username,
            "sample": sample,
            "env_name": env_name,
            "name": name,
            "page_num": page_num,
            "page_size": page_size,
        }
        return self.template_requirement("query_workspace_his", api_data)

    def query_storm_list(self, sample: str, env_name: str):
        """
        query storm info list
        Args:
            sample:
            env_name:

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        api_data = {
            "sample": sample,
            "env_name": env_name,
        }
        return self.template_requirement("query_storm_list", api_data)

    def update_storm(self, sample: str, env_name: str):
        """
        add/update storm
        Args:
            sample:
            env_name:

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        api_data = {
            "sample": sample,
            "env_name": env_name,
        }
        return self.template_requirement("update_storm", api_data)

    def delete_storm(self, sample: str, env_name: str):
        """
        delete storm
        Args:
            sample:
            env_name:

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        api_data = {
            "sample": sample,
            "env_name": env_name,
        }
        return self.template_requirement("delete_storm", api_data)

    def control_storm(self, sample: str, env_name: str, option: str):
        """
        control storm
        Args:
            sample:
            env_name:
            option: start/stop

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        api_data = {
            "sample": sample,
            "env_name": env_name,
            "option": option,
        }
        return self.template_requirement("control_storm", api_data)

    def query_storm_sample_data(self):
        """
        query storm sample/env_name data
        Args:

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        api_data = {}
        return self.template_requirement("query_storm_sample_data", api_data)

    def query_storm_conf(self, sample: str, env_name: str):
        """
        query storm sample/env_name data
        Args:
            sample:
            env_name:

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        api_data = {"sample": sample, "env_name": env_name}
        return self.template_requirement("query_storm_conf", api_data)

    def update_storm_conf(self, sample: str, env_name: str, online_conf: Dict):
        """
        query storm sample/env_name data
        Args:
            sample:
            env_name:
            online_conf: chip online qubit config

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        api_data = {
            "sample": sample,
            "env_name": env_name,
            "online_conf": online_conf,
        }
        return self.template_requirement("update_storm_conf", api_data)

    def save_workspace_info(
        self,
        bit_names: Union[List, str],
        conf_names: Union[List, str],
        point_label: Union[List, str] = None,
        bit_attr: Union[List, str] = None,
    ):
        """
        save workspace infos
        Args:
            bit_names:  workspace qubit names
            conf_names: workspace config names
            point_label: workspace point_label names
            bit_attr: qubit attr names

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        api_data = {
            "bit_names": bit_names,
            "conf_names": conf_names,
            "point_label": point_label,
            "bit_attr": bit_attr,
        }
        return self.template_requirement("save_workspace_info", api_data)

    def add_workspace_info(
        self,
        work_type: int,
        bit: Union[str, List] = None,
        bit_attr: str = None,
        point_label: str = None,
    ):
        """
        add workspace infos, only add
        Args:
            work_type:  0,1,2
            bit: bit name(work_type=0)
            bit_attr: bit attribute(work_type=1)
            point_label: point label(work_type=2)

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        api_data = {
            "work_type": work_type,
            "bit": bit,
            "bit_attr": bit_attr,
            "point_label": point_label,
        }
        return self.template_requirement("add_workspace_info", api_data)

    def delete_workspace_info(
        self, work_type: int, bit: Union[str, List] = None, bit_attr: str = None
    ):
        """
        delete workspace infos, only delete
        Args:
            work_type:  0,1
            bit: bit name(work_type=0)
            bit_attr: bit attribute(work_type=1)

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        api_data = {
            "work_type": work_type,
            "bit": bit,
            "bit_attr": bit_attr,
        }
        return self.template_requirement("delete_workspace_info", api_data)

    def copy_workspace(
        self, from_user: str, from_sample: str, from_env: str, to_user: str
    ):
        """
        copy workspace range
        Args:
            from_user:  from username
            from_sample: from sample
            from_env: from env_name
            to_user: copy to username

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        api_data = {
            "from_user": from_user,
            "from_sample": from_sample,
            "from_env": from_env,
            "to_user": to_user,
        }
        return self.template_requirement("copy_workspace", api_data)

    def revert_bit(self, doc_id: str):
        """
        revert BaseQubitHis bit to local
        Args:
            doc_id:  QubitHistory doc_id

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        api_data = {"doc_id": doc_id}
        return self.template_requirement("revert_bit", api_data)

    def query_revert_bits(self, time_node: str):
        """
        query revert BaseQubitHis many bits to local (for time node)
        Args:
            time_node:  datetime str (%Y-%m-%d %H:%M:%S)

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        api_data = {"time_node": time_node}
        return self.template_requirement("query_revert_bits", api_data)

    def revert_more_bits(self, time_node: str):
        """
        revert BaseQubitHis many bits to local (for time node)
        Args:
            time_node:  datetime str (%Y-%m-%d %H:%M:%S)

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        api_data = {"time_node": time_node}
        return self.template_requirement("revert_more_bits", api_data)


class ChipV1(Chip):
    """Chip version V1: support many point_label bin file."""

    URL_MAP = Chip.URL_MAP
    URL_MAP.update(
        {
            "config_details_v1": (b"config/v1", b"get"),
            "update_config_v1": (b"config/v1", b"put"),
            "query_all_chip_v1": (b"conf/all/v1", b"get"),
            "q_o_u_config_v1": (b"other_config/v1", b"get"),
            "c_o_u_config_v1": (b"other_config/v1", b"post"),
        }
    )

    def query_chip_all(
        self,
        name: Union[str, list] = None,
        username: str = None,
        sample: str = None,
        env_name: str = None,
        point_label: str = None,
        qid: str = None,
    ):
        """
        query chip all config and bit.
        This interface is used to query other people's chip configuration information and parameters.
        Args:
            name: str config name, such as q1, crosstalk,default None, find all.
            username: str username, default None, find self config.
            sample: str sample name, default None, find all.
            point_label: str point_label, default None, find all.
            env_name: str env name, default None, find all.
            qid: qubit id or coupler id
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            return a dict, the key is configfile name, such as :
            {
            "q0": ...,
            "distortion_q0.dat": ...,
            }
        """

        api_data = {
            "name": name,
            "qid": qid,
            "username": username,
            "sample": sample,
            "point_label": point_label,
            "env_name": env_name,
        }
        return self.template_requirement("query_all_chip_v1", api_data, time_out=15)

    def update_single_config(
        self,
        file_name: str,
        file_data: Union[bytes, Dict],
        bin_abbr: Union[bytes, Dict] = None,
    ):
        """
        update config file, just could update or put one file pre time.
        Args:
            file_name: the config file name.
            file_data: the config file data, usually is bytes.
            bin_abbr: file data abbreviation

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {"filename": file_name, "file_data": file_data, "bin_abbr": bin_abbr}
        if file_name.endswith("bin"):
            resp = self.template_requirement("update_config_v1", api_data)
        else:
            resp = self.template_requirement("update_config", api_data)
        return resp

    def query_config(self, config_file_name: Union[List[str], str]):
        """
        get config details file,
        Args:
            config_file_name: the config file name, if need file > 1, input filename list.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        api_data = {"filename": config_file_name}
        return self.template_requirement("config_details_v1", api_data)

    def query_other_user_data(
        self,
        username: str,
        type_name: str = None,
        sample: str = None,
        env_name: str = None,
        point_label: str = None,
    ):
        """
        Query other base data, by type_name.
        Args:
            username: prepare query the username.
            type_name: the config type, must in ["BaseQubit", "Config"], if None: all type
            sample (str):
            env_name (str):
            point_label (str):

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        if type_name and type_name not in ["BaseQubit", "Config"]:
            print(f"type_name:{type_name!r} not support.")
        api_data = {
            "other_name": username,
            "type_name": type_name,
            "sample": sample,
            "env_name": env_name,
            "point_label": point_label,
        }
        return self.template_requirement("q_o_u_config_v1", api_data)

    def copy_other_user_data(
        self,
        from_user: str,
        from_sample: str,
        from_env_name: str,
        from_point_label: str,
        local: bool = True,
        to_user: str = None,
        to_sample: str = None,
        to_env_name: str = None,
        to_point_label: str = None,
        element_names: List[str] = None,
        element_configs: List[str] = None,
        copy_qubit: bool = True,
    ):
        """
        copy another user config to self database.
        Args:
            from_user(str): from user`s username.
            from_sample(str): from sample name.
            from_env_name(str): from env_name name.
            from_point_label(str): from point_label name.
            local(bool): if True, copy data to local env and to_user、to_sample、
                        to_env_name、to_point_label default to myself local env data.
            to_user(str): copy to user`s username, if local is True, default=None: local user.
            to_sample(str): copy to sample, if local is True, default=None: local sample.
            to_env_name(str): copy to env_name, if local is True, default=None: local env_name.
            to_point_label(str): copy to point_label, if local is True, default=None: local point_label.
            element_names: the config params with type if None: all bits.
            element_configs: the config params with type if None: not copy config everything.
                             option from [character,crosstalk,distortion,bin,instrument,union_readout].
                             The bin option is very special, and when selected, copies all bits of bin data.
            copy_qubit(bool): whether copy qubits
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        # if type_name and type_name not in ["BaseQubit", "Config"]:
        #     print(f"type_name:{type_name!r} not support.")
        # api_data = {
        #     "other_name": username,
        #     "type_name": type_name,
        #     "element_names": element_names,
        #     "sample": sample,
        #     "env_name": env_name,
        #     "point_label": point_label,
        # }
        api_data = {
            "from_user": from_user,
            "from_sample": from_sample,
            "from_env_name": from_env_name,
            "from_point_label": from_point_label,
            "local": local,
            "to_user": to_user,
            "to_sample": to_sample,
            "to_env_name": to_env_name,
            "to_point_label": to_point_label,
            "element_names": element_names,
            "element_configs": element_configs,
            "copy_qubit": copy_qubit,
        }
        return self.template_requirement("c_o_u_config_v1", api_data, time_out=20)

    def query_chip_env_id(self,
                          sample: str = None,
                          env_name: str = None,
                          point_label: str = None,
                          username: str = None):

        """
        query chip env id
        """
        api_data = {
            "sample": sample,
            "env_name": env_name,
            "point_label": point_label,
            "username": username,
        }
        return self.template_requirement("query_chip_env_id", api_data)
