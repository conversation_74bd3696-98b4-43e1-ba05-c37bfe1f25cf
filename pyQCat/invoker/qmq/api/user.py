import os
import uuid
# from typing import Dict, List
from pyQCat.invoker.const import Dict
from .baseapi import ApiBase
from ..client import CourierClient
from ..util import get_ipv4_addresses
import platform

_is_linux = platform.system() == "Linux"

if _is_linux:
    import pwd


class User(ApiBase, prefix_url=b"/user/", default_client=CourierClient):
    URL_MAP = {
        "login": (b"login", b"post"),
        "logout": (b"logout", b"post"),
        "register_user": (b"register", b"post"),
        "reset_passwd": (b"password", b"post"),
        "change_passwd": (b"password", b"put"),
        "get_group": (b"group", b"get"),
        "create_group": (b"group", b"post"),
        "modify_group": (b"group", b"put"),
        "modify_group_leader": (b"group/leader", b"post"),
        "modify_platform_leader": (b"platform/leader", b"post"),
        "query_group_user": (b"group/users", b"get"),
        "query_perms_user_list": (b"perms/list", b"get"),
        "query_user_info": (b"info", b"get"),
        "query_groups": (b"group/list", b"get"),
        "query_group_names": (b"group/names", b"get"),
        "query_config": (b"config", b"get"),
        "modify_config": (b"config", b"post"),
        "name_list": (b"name/list", b"get"),
        "cache_data": (b"cache/data", b"get"),
        "update_cache_data": (b"cache/data", b"post"),
        "check_user_version": (b"check/version", b"get"),
        "query_white_list": (b"white", b"get"),
        "add_white_list": (b"white", b"post"),
        "remove_white_list": (b"white", b"delete"),
        "query_black_list": (b"black", b"get"),
        "add_black_list": (b"black", b"post"),
        "remove_black_list": (b"black", b"delete"),
        "get_exclusive_user": (b"exclusive", b"get"),
        "del_exclusive_user": (b"exclusive", b"delete"),
        "time_exclusive_user": (b"exclusive/time", b"post"),
        "cron_exclusive_user": (b"exclusive/corn", b"post"),
        "interval_exclusive_user": (b"exclusive/interval", b"post"),
        "query_default_node": (b"node/env", b"get"),
    }

    def login(self, username: str, password: str):
        """
        login in pyqcat service.
        Args:
            username: you register username.
            password: you password.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            if succ:
                data = {
                    "token" : "your token."
                }
        """
        if _is_linux:
            record_username = pwd.getpwuid(os.getuid()).pw_name
        else:
            record_username = os.getlogin()
        records = {
            "windows_name": record_username,
            "ipv4": get_ipv4_addresses(),
            "mac": ':'.join(['{:02x}'.format((uuid.getnode() >> i) & 0xff)
                             for i in range(0, 48, 8)]),
        }
        api_data = {
            "username": username,
            "password": password,
            "records": records,
        }

        return self.template_requirement(identifier="login", api_data=api_data, token=b"")

    def logout(self):
        """
        logout, The token cannot continue to be used.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        return self.template_requirement("logout")

    def register_user(self, username: str, password: str, repeat_password: str, email: str, group: str = "normal"):
        """

        Args:
            username:prepare register username, the username user to login.
            password: any str.
            repeat_password: same with password
            email: email for yoursele.
            group: group name.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data = {
                "token" : "your token."
            }
        """
        repeat_password = repeat_password if repeat_password is not None else password
        api_data = {
            "username": username,
            "password": password,
            "repeat_password": repeat_password,
            "email": email,
            "group": group,
        }
        return self.template_requirement(identifier="register_user", api_data=api_data, token=b"")

    def reset_passwd(self, user_name: str, password: str, new_password: str, email: str):
        """
        reset user password
        Args:
            user_name:you register username.
            password: old password.
            new_password: new password.
            email: your personal email with register.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

        api_data = {
            "username": user_name,
            "password": password,
            "new_password": new_password,
            "email": email
        }
        return self.template_requirement("reset_passwd", api_data)

    def change_passwd(self, new_password: str):
        """
        change user password
        Args:
            new_password: new password.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

        api_data = {
            "new_password": new_password,
        }
        return self.template_requirement("change_passwd", api_data)

    def query_group(self, target_group: str = None):
        """
        Query a single group set of details.

        Super group: all groups can be queried.
        Non-super groups : query their own group.

         Args:
            target_group: the name you will find.
            Do not fill in the default query for your own group.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data ={
                "name": "super",
                "description": null,
                "users": [
                  "A_9527",
                  "A_9528"
                ],
                "leaders": [
                  "A_9527",
                  "A_9528"
                ],
                "counts": 2
              }

        """
        api_data = {
            "target_group": target_group
        } if target_group is not None else {}
        return self.template_requirement("get_group", api_data)

    def create_group(self, group_name: str, description: str = ""):
        """
        create new group.
        Args:
            group_name: With the creation group name.
            description:Group description information.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "description": description,
            "groupname": group_name
        }
        return self.template_requirement("create_group", api_data)

    def change_user_group(self, target_user: str, target_group: str):
        """
        Modify the group that the user is in.
        Args:
            target_user: The user to be modified.
            target_group:The group that the user is going to.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "target_user": target_user,
            "target_group": target_group
        }
        return self.template_requirement("modify_group", api_data)

    def change_group_leader(self, target_user: str, target_group: str, is_admin: bool):
        """
        Modify whether user in group becomes an administrator.
        Args:
            target_user: the user will be change.
            target_group: group name.
            is_admin: the user is admin, if true, the user will become admin.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "target_user": target_user,
            "target_group": target_group,
            "is_admin": is_admin
        }
        return self.template_requirement("modify_group_leader", api_data)

    def change_platform_leader(self, username: str, platform_ident: str, is_admin: bool):
        """
        Modify whether user in group becomes an administrator.
        Args:
            username: the user will be change.
            platform_ident: platform_ident.
            is_admin: the user is admin, if true, the user will become admin.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "username": username,
            "platform_ident": platform_ident,
            "is_admin": is_admin
        }
        return self.template_requirement("modify_platform_leader", api_data)

    def query_group_info(self, target_group: str = None):
        """
        get group user.

        Args:
            target_group: the group name need query. if note group name query the group which self in.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data is a list, usually such as:
            data = [
                    {
                      "username": [
                        "A_9527",
                        "A_9528"
                      ],
                      "groups": "super",
                      "is_super": true,
                      "is_admin": true
                    }
                  ]
        """
        api_data = {"groupname": target_group} if target_group is not None else {}
        return self.template_requirement("query_group_user", api_data)

    def query_perms_user_list(self, perm_type=None, platform_ident="", perms_ident="", group_name: str = ""):
        """"""
        api_data = {
            "perm_type": perm_type,
            "platform_ident": platform_ident,
            "perms_ident": perms_ident,
            "group_name": group_name,
        }
        return self.template_requirement("query_perms_user_list", api_data)

    def query_user_info(self, target_user: str = None):
        """
        query user details info.
        Args:
            target_user: the userame need query, if None  query self.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data is a dict, show user detail msg:
            data = {
                    "username": "A_9527",
                    "groups": "super",
                    "email": "<EMAIL>",
                    "phone_num": null,
                    "is_super": true,
                    "is_admin": true,
                    "status": 0,
                    "create_time": "2022-09-21 17:13:46",
                    "last_login_time": "2022-10-11 10:35:03",
                    "id": "632ad5caa90f6134fe0e9504"
                  }
        """
        api_data = {"target_user": target_user} if target_user is not None else {}
        return self.template_requirement("query_user_info", api_data)

    def query_all_groups(self, perm_type=None, platform_ident="", perms_ident=""):
        """
        get system all groups.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data is a list, show all groups and user. such as:
            data = [
            {
              "name": "super",
              "description": null,
              "leaders": ["A_9527","A_9528"]
            },
            {
              "name": "normal",
              "description": null,
              "leaders": [  "A_9527","A_9528"]
            }
          ]
        """
        api_data = {
            "perm_type": perm_type,
            "platform_ident": platform_ident,
            "perms_ident": perms_ident,
        }
        return self.template_requirement("query_groups", api_data=api_data)

    def query_group_names(self, all_group=True):
        """
        get system all group names.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data is a list, show all group`s name. such as:
            data = ["normal", "super"]
        """
        api_data = {
            "all_group": all_group,
        }
        return self.template_requirement("query_group_names", api_data)

    def query_user_config(self):
        """
        query user config info.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data is a dict, show user config info. such as:
            data = {
                "username": "A_9527",
                "system": {
                  "chip_label": "",
                  "platform": "",
                  "root": "",
                  "filepath": "",
                  "use_s3": false,
                  "qaio_type": "8bit",
                  "work_point": "freestyle"
                },
                "service": {
                  "qaio_ip": "",
                  "qaio_port": "",
                  "data_service_ip": "",
                  "data_service_port": ""
                },
                "minio": {
                  "address": "1",
                  "access_key": "",
                  "secret_key": ""
                }
              }
        """
        return self.template_requirement("query_config")

    def change_user_config(self, system: Dict = None, service: Dict = None, minio: Dict = None):
        """
        modify user config info.
        Args:
            system: the user system config, if None system is null dict.
            service: the user service config, if None service is null dict.
            minio: the user s3 service config, if None s3 service is null dict.
            now s3 service is minio.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "system": system or {},
            "service": service or {},
            "minio": minio or {},
        }
        return self.template_requirement("modify_config", api_data)

    def query_usernames(self, groups: str = None):
        """
        query usernames list for users
        if groups: query usernames for groups
        else: all usernames
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "groups": groups
        }
        return self.template_requirement("name_list", api_data)

    def query_cache_data(self):
        """
        query user`s cache data
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
        }
        return self.template_requirement("cache_data", api_data)

    def update_cache_data(self, parameters: Dict):
        """
        update user`s cache data
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "parameters": parameters,
        }
        return self.template_requirement("update_cache_data", api_data)

    def check_user_version(self, username: str, version: str):
        """check user version"""
        api_data = {
            "username": username,
            "version": version
        }
        return self.template_requirement("check_user_version", api_data)

    def query_white_list(self):
        """query user white list"""
        api_data = {
        }
        return self.template_requirement("query_white_list", api_data)

    def add_white_list(self, username: str):
        """add user to white list"""
        api_data = {
            "username": username,
        }
        return self.template_requirement("add_white_list", api_data)

    def remove_white_list(self, username: str):
        """remove user to white list"""
        api_data = {
            "username": username,
        }
        return self.template_requirement("remove_white_list", api_data)

    def query_black_list(self):
        """query user black list"""
        api_data = {
        }
        return self.template_requirement("query_black_list", api_data)

    def add_black_list(self, username: str):
        """add user to black list"""
        api_data = {
            "username": username,
        }
        return self.template_requirement("add_black_list", api_data)

    def remove_black_list(self, username: str):
        """remove user to black list"""
        api_data = {
            "username": username,
        }
        return self.template_requirement("remove_black_list", api_data)

    def get_exclusive_user(self):
        """get exclusive user and expire_time"""
        api_data = {
        }
        return self.template_requirement("get_exclusive_user", api_data)

    def add_exclusive_date(self, username: str,
                           start_time: str,
                           timeout: int,
                           sample: str = None,
                           env_name: str = None):
        """add chimera exclusive user from once scheduler(trigger: date)
        Args:
            username(str):
            start_time(str): "%Y-%m-%d %H:%M:%S" -> "2024-01-25 12:00:03"
            timeout(int): timeout seconds, Radius(10 minute, 10 hour)
            sample(str): default to None(self env)
            env_name(str): default to None(self env)
        Returns
            code
                200： success
                400： parameters validate error
                403: Cannot be created because it is already occupied

        """
        api_data = {
            "username": username,
            "timeout": timeout,
            "sample": sample,
            "env_name": env_name,
            "trigger": "date",
            "start_time": start_time,

        }
        return self.template_requirement("time_exclusive_user", api_data)

    def add_exclusive_cron(self, username: str,
                           timeout: int,
                           sample: str = None,
                           env_name: str = None,
                           year: str = None,
                           month: str = None,
                           week: str = None,
                           day: str = None,
                           hour: str = None,
                           minute: str = None,
                           second: str = None,
                           ):
        """add chimera exclusive user from once scheduler(trigger: date)
        Args:
            username(str):
            timeout(int):(seconds) Valid timeout period
            sample(str): default to None(self env)
            env_name(str): default to None(self env)
            year(str): such as "2024"  "*/2"  "2024,2025"  "*"
            month(str): (1-12) such as "1"  "*/2"  "1,2,5"  "*"
            week(str): (0-6)   such as "1"  "*/2"  "1,2,5"  "*"
            day(str): (1-31)   such as "3"  "*/2"  "1,2,5"  "*"
            hour(str): (0-23)   such as "4"  "*/2"  "1,2,5"  "*"
            minute(str): (0-59)   such as "5"  "*/2"  "1,2,5,7"  "*"
            second(str): (0-59)   such as "6"
        Returns
            code
                200： success
                400： parameters validate error
                403: Cannot be created because it is already occupied

        """
        api_data = {
            "username": username,
            "timeout": timeout,
            "sample": sample,
            "env_name": env_name,
            "trigger": "cron",
            "year": year,
            "month": month,
            "week": week,
            "day": day,
            "hour": hour,
            "minute": minute,
            "second": second,
        }
        return self.template_requirement("cron_exclusive_user", api_data)

    def add_exclusive_interval(self, username: str,
                               timeout: int,
                               sample: str = None,
                               env_name: str = None,
                               weeks: int = None,
                               days: int = None,
                               hours: int = None,
                               minutes: int = None,
                               start_time: str = None,
                               end_time: str = None):
        """add chimera exclusive user from once scheduler(trigger: date)
        Args:
            username(str):
            timeout(int):(seconds) Valid timeout period
            start_time(str): "%Y-%m-%d %H:%M:%S" -> "2024-01-25 12:00:03"
            end_time(str): "%Y-%m-%d %H:%M:%S" -> "2025-01-25 12:00:03"
            sample(str): default to None(self env)
            env_name(str): default to None(self env)
            weeks(int): The number of weeks between triggers.
            days(int): The number of days between triggers.
            hours(int): The hours of days between triggers.
            minutes(int): The minutes of days between triggers.
            # seconds(int): The seconds of days between triggers.
        Returns
            code
                200： success
                400： parameters validate error
                403: Cannot be created because it is already occupied

        """
        api_data = {
            "username": username,
            "timeout": timeout,
            "trigger": "interval",
            "sample": sample,
            "env_name": env_name,
            "weeks": weeks,
            "days": days,
            "hours": hours,
            "minutes": minutes,
            # "seconds": seconds,
            "start_time": start_time,
            "end_time": end_time,
        }
        return self.template_requirement("interval_exclusive_user", api_data)

    def del_exclusive_user(self,
                           sample: str = None,
                           env_name: str = None):
        """del chimera exclusive user
        Args:
            sample(str): default to None(self env)
            env_name(str): default to None(self env)

        """
        api_data = {
            "sample": sample,
            "env_name": env_name,
        }
        return self.template_requirement("del_exclusive_user", api_data)

    def query_default_node(self):
        """query default node
        """
        api_data = {
        }
        return self.template_requirement("query_default_node", api_data)
