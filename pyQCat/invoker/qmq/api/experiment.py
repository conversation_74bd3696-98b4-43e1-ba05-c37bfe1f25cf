# from typing import Dict
from pyQCat.invoker.const import Dict
from .baseapi import ApiBase
from ..client import CourierClient
from typing import Union, List


class Expeirment(ApiBase, prefix_url=b"/exp/", default_client=CourierClient):
    URL_MAP = {
        "get_list": (b"list", b"get"),
        "save_exp_list": (b"list", b"post"),
        "exp_options": (b"exp", b"post"),
        "get_exp_options": (b"exp", b"get"),
        "execute": (b"execute", b"post"),
        "update_execute": (b"execute", b"put"),
        "exp_record": (b"history", b"get"),
        "exp_execute_history_list": (b"history/list", b"get"),
        "init": (b"init", b"post"),
        "init_customer": (b"init/customer", b"post"),
        "delete_customer": (b"init/customer", b"delete"),
        "get_simulator_data": (b"simulator/data", b"get"),
        "run_experiment": (b"run", b"post"),
        "delete_experiment": (b"option", b"delete"),
        "compare_exp_policy": (b"policy/compare", b"get"),
        "query_exp_policy": (b"policy", b"get"),
        "query_task_performer": (b"task/performer", b"get"),
        "calibration": (b"calibration", b"post"),
        "add_exp_record": (b"record", b"post"),
        "batch_start_signal": (b"batch/record", b"post"),
        "batch_end_signal": (b"batch/record", b"put"),
        "batch_flow_start_signal": (b"batch/flow/record", b"post"),
        "batch_flow_end_signal": (b"batch/flow/record", b"put"),
        "batch_exp_end_signal": (b"batch/exp/record", b"put"),
    }

    def query_exp_list(self, exp_type: str = None):
        """
        get experiment list and options.
        Args:
            exp_type: (str) The type of experiment to be queried, must in
            [PreliminaryExperiment、BaseExperiment，CompositeExperiment],
            if None will find all experiment. default is None.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            the data such as
            {
                "exp_name": "",
                "exp_params": {},
                "official": false
              }
        """
        if exp_type is None:
            api_data = {}
        else:
            api_data = {
                "exp_type": exp_type
            }
        return self.template_requirement("get_list", api_data)

    def save_exp_list(self, exp_data: list):
        """
            save experiment list and options.
            Args:
                exp_data: (list) : the experiment data list
                                    [{}, {}, {}]
            Returns:
                the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

            """
        api_data = {
            "exp_data": exp_data
        }
        return self.template_requirement("save_exp_list", api_data)

    def query_exp_options(self, name: str):
        """
        query experiment options.
        Args:
            name: the experiment options.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            the data such as
            {
                "exp_name": "",
                "exp_params": {},
                "official": false
              }
        """
        api_data = {
            "exp_name": name
        }
        return self.template_requirement("get_exp_options", api_data)

    def save_exp_options(self, exp_name: str, exp_params: Dict):
        """
        save user experiment params and options in service.
        Args:
            exp_name: experiment name
            exp_params:the experiment

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "exp_name": exp_name,
            "exp_params": exp_params
        }
        return self.template_requirement("exp_options", api_data)

    def execute_exp(self, exp_id: str, exp_name: str, exp_type: str,
                    extra: Dict, is_paternal: bool = True, chimera_data: Dict = None,
                    paternal_id: str = "", is_simulate: bool = False,
                    is_parallel: bool = False, index: int = 0):
        """
        upload execute experiment detail info.
        Args:
            exp_id: experiment execute id.
            exp_name: experiment name, such as rabi.
            exp_type: single or composite.
            extra: the experiment extra info and params.
            is_paternal: is paternal experiment or not.
            paternal_id: paternal experiment`s id.
            is_simulate(bool): use simulate or not.
            is_parallel(bool): is parallel experiment or not.
            index(int): parallel exp index.default=0
            chimera_data(dict): save single experiment chimera run doc.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "exp_id": exp_id,
            "exp_name": exp_name,
            "exp_type": exp_type,
            "chimera_data": chimera_data,
            "extra": extra,
            "is_paternal": is_paternal,
            "paternal_id": paternal_id,
            "is_simulate": is_simulate,
            "is_parallel": is_parallel,
            "index": index,
        }
        return self.template_requirement("execute", api_data, time_out=60)

    def update_execute_exp(self, exp_id: str, status: int, quality: Union[str, float],
                           quality_des: str, index: int = 0):
        """
        upload execute experiment detail info.
        Args:
            exp_id: experiment execute id.
            status(int): success of fail.
            quality(str, float): 0.982(R²).
            quality_des(str): perfect.
            index(int): parallel exp index.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "exp_id": exp_id,
            "status": status,
            "quality": quality if isinstance(quality, (float, int)) else 0,
            "quality_des": quality_des,
            "index": index,
        }
        return self.template_requirement("update_execute", api_data)

    def add_exp_record(self, data: Dict):
        """
        add experiment record
        Args:
            data: (Dict) the experiment data
            /
            data such as:
            {
                "record_id": "be1ea089-b4cc-4939-8bae-4b788f797de2",
                "version": "0.22.3",
                "device": "B",
                "username": "zyc_y3",
                "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）",
                "env_name": "Y3",
                "point_label": "2q_gate",
                "exp_type": 1,
                "exp_class": "SingleShot",
                "date": "2025-2-20",
                "context_meta": {},
                "execute_meta": {},
            }
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        return self.template_requirement("add_exp_record", data)

    def query_exp_record(self, experiment_id: str = None, experiment_name: str = None):
        """
        query experiment record, just get one record pre query.
        Args:
            experiment_id: (str) the experiment execute id, default is None, then query by experiment name.
            experiment_name: (str)the experiment name. default is None, then query by experiment id.
            if id and name all not None, then query by id.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data such as:
            {
                  "username": "xiao",
                  "exp_type": "single",
                  "exp_id": "63455f8293a3b120094e01b2",
                  "exp_name": "StateTomography",
                  "extra": {
                    "dirs": "E:\\LocalTest\\SimulatorResult\\fivago\\ProcessTomography\\q0q1
                    \\2022-10-11\\20.20.18\\StateTomography\\20-20-18-[['I'], ['I']]\\"
             }
        """
        api_data = {
            "exp_id": experiment_id,
            "exp_name": experiment_name
        }
        return self.template_requirement("exp_record", api_data)

    def query_exp_history(self,
                          exp_name: str = None,
                          username: str = None,
                          page_num: int = 1,
                          page_size: int = 10):
        """
        query experiment execute history.
        Args:
            exp_name: the query experiment name. Default is None, then query history near execute.
            username: username. if none all user
            page_num: The number of query starting bars.
            page_size: The number of records per query.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data is a list, per experiment record is a dict, such as:
            [{
                  "username": "xiao",
                  "exp_type": "single",
                  "exp_id": "63455f8293a3b120094e01b2",
                  "exp_name": "StateTomography",
                  "extra": {
                    "dirs": "E:\\LocalTest\\SimulatorResult\\fivago\\ProcessTomography\\q0q1
                    \\2022-10-11\\20.20.18\\StateTomography\\20-20-18-[['I'], ['I']]\\"
                  },
                  "version": "0.1.6",
                  "create_time": "2022-10-11 20:20:19",
                  "id": "63455f83b3bbec53fc57e2e4"
                }]
        """
        api_data = {
            "exp_name": exp_name,
            "username": username,
            "page_num": page_num,
            "page_size": page_size
        }
        return self.template_requirement("exp_execute_history_list", api_data)

    def init_experiment_and_dag(self, exp_data: Dict, dag_data: List[Dict] = None):
        """
        init courier experiment and dag config, use after visage login.
        this api not recommended for common users.
        Args:
            exp_data: Dict, the experiment data.
            dag_data: list, the dag struct dict in list.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

        api_data = {
            "exp_data": exp_data,
            "dag_data": dag_data
        }
        return self.template_requirement("init", api_data)

    def init_customer_exp(self, exp_data: List):
        """
            init courier customer experiment, only operate custom experiments, will filter others
            Args:
                exp_data: List, the experiment data list.

            Returns:
                the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            """
        api_data = {
            "exp_data": exp_data,
        }
        return self.template_requirement("init_customer", api_data)

    def delete_customer_exp(self, exp_name: Union[str, List[str]]):
        """
            delete courier customer experiment, only operate custom experiments, will filter others
            Args:
                exp_name: Union[str, List[str]], the experiment name.

            Returns:
                the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            """
        api_data = {
            "exp_name": exp_name,
        }
        return self.template_requirement("delete_customer", api_data)

    def get_simulator_data(self, exp_name: str, index: int = 0,
                           simulator_remote_path: str = None):
        """
        get simulator data to use simulator
        Args:
            exp_name: experiment name
            index: default is 0, when CompositeExperiment must be input index
            simulator_remote_path: simulator path

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "exp_name": exp_name,
            "index": index,
            "simulator_remote_path": simulator_remote_path,
        }
        return self.template_requirement("get_simulator_data", api_data)

    def run_experiment(self, task: Dict):
        """
        run experiment
        Args:
            task: experiment data

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "task": task,
        }
        return self.template_requirement("run_experiment", api_data, time_out=60)

    def delete_experiment(self, task: Union[str, List[str]], force: bool = True):
        """
        delete experiment, use to remove task if task send to chimera but not running.
        Args:
            task: experiment task id or id list.
            force: whether force to stop exp task.
        Returns:
            the standard response.
        """

        api_data = {
            "task_ids": task,
            "force": force,
        }
        return self.template_requirement("delete_experiment", api_data)

    def compare_exp_policy(self, exp_ids: Union[str, List[str]]):
        """
        Compare the calibration strategies between different experiments
        Args:
            exp_ids: experiment id or id list.
        Returns:
            the standard response.
        """

        api_data = {
            "exp_ids": exp_ids,
        }
        return self.template_requirement("compare_exp_policy", api_data)

    def query_exp_policy(self,
                         exp_id: Union[str, List[str]] = None,
                         username: str = None,
                         exp_name: str = None,
                         sample: str = None,
                         env_name: str = None,
                         page_num: int = 1,
                         page_size: int = 10):
        """
        Compare the calibration strategies between different experiments
        Args:
            exp_id: experiment id or id list.
            username: username default to None：all user
            exp_name: .
            sample: .
            env_name: .
            page_num: .
            page_size: .
        Returns:
            the standard response.
        """

        api_data = {
            "exp_id": exp_id,
            "username": username,
            "exp_name": exp_name,
            "sample": sample,
            "env_name": env_name,
            "page_num": page_num,
            "page_size": page_size,
        }
        return self.template_requirement("query_exp_policy", api_data)

    def query_task_performer(self, task_ids: Union[list, str]):
        """
        Query the execution time of a task in each module.
        Args:
            task_ids: task id or id list.
        Returns:
            the standard response.
        """

        api_data = {
            "task_ids": task_ids,
        }
        return self.template_requirement("query_task_performer", api_data)

    def calibration(
            self,
            item_type: str,
            cali_type: Union[str, int],
            total_num: int,
            success_num: int,
            fail_num: int,
            fault_num: int,
            result: Dict,
    ):
        """
        add auto calibration data.
        """

        api_data = dict(
            item_type=item_type,
            cali_type=cali_type,
            total_num=total_num,
            success_num=success_num,
            fail_num=fail_num,
            fault_num=fault_num,
            result=result,
        )
        return self.template_requirement("calibration", api_data)

    def batch_start_signal(
            self,
            record_id: str,
            system_meta: Dict,
            context_meta: Dict,
            execute_meta: Dict,
    ):
        """
        add batch start record.
        """

        api_data = dict(
            record_id=record_id,
            system_meta=system_meta,
            context_meta=context_meta,
            execute_meta=execute_meta,
        )
        return self.template_requirement("batch_start_signal", api_data)

    def batch_end_signal(
            self,
            **kwargs
    ):
        """
        update batch result.
        """
        api_data = dict(kwargs)
        return self.template_requirement("batch_end_signal", api_data)

    def batch_flow_start_signal(
            self,
            batch_id: str,
            flow_id: str,
            name: str,
            exp_flows: List[str],
            start_time: str,
            physical_units: List[str],
            end_time: str = "",
            experiments: List[str] = None,
            pass_units: List[str] = None,
            fail_units: List[str] = None,
            *args,
            **kwargs
    ):
        """
        add batch flow record.
        """

        api_data = dict(
            batch_id=batch_id,
            flow_id=flow_id,
            name=name,
            exp_flows=exp_flows,
            start_time=start_time,
            physical_units=physical_units,
            end_time=end_time,
            experiments=experiments or [],
            pass_units=pass_units or [],
            fail_units=fail_units or [],
            **kwargs
        )
        return self.template_requirement("batch_flow_start_signal", api_data)


    def batch_flow_end_signal(
            self,
            batch_id: str,
            flow_id: str,
            end_time: str,
            pass_units: List[str] = None,
            fail_units: List[str] = None,
    ):
        """
        update batch flow result.
        """

        api_data = dict(
            batch_id=batch_id,
            flow_id=flow_id,
            end_time=end_time,
            pass_units=pass_units or [],
            fail_units=fail_units or [],
        )
        return self.template_requirement("batch_flow_end_signal", api_data)

    def batch_exp_end_signal(
            self,
            batch_id: str,
            flow_id: str,
            record_id: str,
            name: str,
            physical_units: List[str],
            pass_units: List[str] = None,
            fail_units: List[str] = None,
            update_data: List = None,
            *args,
            **kwargs
    ):
        """
        update batch flow result.
        """

        api_data = dict(
            batch_id=batch_id,
            flow_id=flow_id,
            record_id=record_id,
            name=name,
            physical_units=physical_units,
            pass_units=pass_units or [],
            fail_units=fail_units or [],
            update_data=update_data or [],
            **kwargs
        )
        return self.template_requirement("batch_exp_end_signal", api_data)



