# -*- coding: utf-8 -*-

# This code is part of pyqcat-invoker.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an 'AS IS' BASIS
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2022/09/23
# __author:       <PERSON>
"""The Monster DataService.
"""
import weakref
from datetime import datetime
from typing import List, Union
from pyQCat.invoker.const import Dict


class Cached(type):
    """
    Cached father class
    """

    def __init__(cls, *args, **kwargs):
        super().__init__(*args, **kwargs)
        cls.__cache = weakref.WeakValueDictionary()

    def __call__(cls, *args, **kwargs):
        if args in cls.__cache:
            return cls.__cache[args]
        else:
            obj = super().__call__(*args, **kwargs)
            cls.__cache[args] = obj
            return obj


class DataCenter(metaclass=Cached):
    """
    DataBase Class create all function to require data.
    """
    async_mode = False

    # experiment
    def query_exp_list(self, exp_type: str = None):
        """
        get experiment list and options.
        Args:
            exp_type: (str) The type of experiment to be queried, must in
            [PreliminaryExperiment、BaseExperiment，CompositeExperiment],
            if None will find all experiment. default is None.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            the data such as
            {
                "exp_name": "",
                "exp_params": {},
                "official": false
              }
        """
        pass

    def save_exp_list(self, exp_data: list):
        """
            save experiment list and options.
            Args:
                exp_data: (list) : the experiment data list
                                    [{}, {}, {}]
            Returns:
                the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

            """
        pass

    def query_exp_options(self, name: str):
        """
        query experiment options.
        Args:
            name: the experiment options.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            the data such as
            {
                "exp_name": "",
                "exp_params": {},
                "official": false
              }
        """
        pass

    def save_exp_options(self, exp_name: str, exp_params: Dict):
        """
        save user experiment params and options in service.
        Args:
            exp_name: experiment name
            exp_params:the experiment

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        pass

    def execute_exp(self, exp_id: str, exp_name: str, exp_type: str,
                    extra: Dict, is_paternal: bool = True,
                    chimera_data: Dict = None,
                    paternal_id: str = "", is_simulate: bool = False,
                    is_parallel: bool = False, index: int = 0):
        """
        upload execute experiment detail info.
        Args:
            exp_id: experiment execute id.
            exp_name: experiment name, such as rabi.
            exp_type: single or composite.
            extra: the experiment extra info and params.
            is_paternal: is paternal experiment or not.
            paternal_id: paternal experiment`s id.
            is_simulate(bool): use simulate or not.
            is_parallel(bool): is parallel experiment or not.
            index(int): parallel exp index.default=0
            chimera_data(dict): save single experiment chimera run doc.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        pass

    def update_execute_exp(self, exp_id: str, status: int, quality: Union[str, float],
                           quality_des: str, index: int = 0):
        """
        upload execute experiment detail info.
        Args:
            exp_id: experiment execute id.
            status(int): success of fail.
            quality(str, float): 0.982(R²).
            quality_des(str): perfect.
            index(int): parallel exp index.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        pass

    def add_exp_record(self, data: Dict):
        """
        add experiment record
        Args:
            data: (Dict) the experiment data
            /
            data such as:
            {
                "record_id": "be1ea089-b4cc-4939-8bae-4b788f797de2",
                "version": "0.22.3",
                "device": "B",
                "username": "zyc_y3",
                "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）",
                "env_name": "Y3",
                "point_label": "2q_gate",
                "exp_type": 1,
                "exp_class": "SingleShot",
                "date": "2025-2-20",
                "context_meta": {},
                "execute_meta": {},
            }
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        pass


    def query_exp_record(self, experiment_id: str = None, experiment_name: str = None):
        """
        query experiment record, just get one record pre query.
        Args:
            experiment_id: (str) the experiment execute id, default is None, then query by experiment name.
            experiment_name: (str)the experiment name. default is None, then query by experiment id.
            if id and name all not None, then query by id.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data such as:
            {
                  "username": "xiao",
                  "exp_type": "single",
                  "exp_id": "63455f8293a3b120094e01b2",
                  "exp_name": "StateTomography",
                  "extra": {
                    "dirs": "E:\\LocalTest\\SimulatorResult\\fivago\\ProcessTomography\\q0q1
                    \\2022-10-11\\20.20.18\\StateTomography\\20-20-18-[['I'], ['I']]\\"
             }
        """
        pass

    def query_exp_history(self,
                          exp_name: str = None,
                          username: str = None,
                          page_num: int = 1,
                          page_size: int = 10):
        """
        query experiment execute history.
        Args:
            exp_name: the query experiment name. Default is None, then query history near execute.
            username: username. if none all user
            page_num: The number of query starting bars.
            page_size: The number of records per query.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data is a list, per experiment record is a dict, such as:
            [{
                  "username": "xiao",
                  "exp_type": "single",
                  "exp_id": "63455f8293a3b120094e01b2",
                  "exp_name": "StateTomography",
                  "extra": {
                    "dirs": "E:\\LocalTest\\SimulatorResult\\fivago\\ProcessTomography\\q0q1
                    \\2022-10-11\\20.20.18\\StateTomography\\20-20-18-[['I'], ['I']]\\"
                  },
                  "version": "0.1.6",
                  "create_time": "2022-10-11 20:20:19",
                  "id": "63455f83b3bbec53fc57e2e4"
                }]
        """
        pass

    def init_experiment_and_dag(self, exp_data: Dict, dag_data: List[Dict] = None):
        """
        init courier experiment and dag config, use after visage login.
        this api not recommended for common users.
        Args:
            exp_data: Dict, the experiment data.
            dag_data: list, the dag structure dict in list.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        pass

    def init_customer_exp(self, exp_data: List):
        """
            init courier customer experiment, only operate custom experiments, will filter others
            Args:
                exp_data: List, the experiment data list.

            Returns:
                the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            """
        pass

    def delete_customer_exp(self, exp_name: Union[str, List[str]]):
        """
            delete courier customer experiment, only operate custom experiments, will filter others
            Args:
                exp_name: Union[str, List[str]], the experiment name.

            Returns:
                the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            """
        pass

    # user

    def login(self, username: str, password: str):
        """
        login in pyqcat service.
        Args:
            username: you register username.
            password: you password.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            if succ:
                data = {
                    "token" : "your token."
                }
        """
        pass

    def logout(self):
        """
        logout, The token cannot continue to be used.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        pass

    def register_user(self, username: str, password: str, repeat_password: str, email: str, group: str = "normal"):
        """

        Args:
            username:prepare register username, the username user to login.
            password: any str.
            repeat_password: same with password
            email: email for yoursele.
            group: group name.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data = {
                "token" : "your token."
            }
        """
        pass

    def reset_passwd(self, user_name: str, password: str, new_password: str, email: str):
        """
        reset user password
        Args:
            user_name:you register username.
            password: old password.
            new_password: new password.
            email: your personal email with register.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        pass

    def change_passwd(self, new_password: str):
        """
        change user password
        Args:
            new_password: new password.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        pass

    def query_group(self, target_group: str = None):
        """
        Query a single group set of details.

        Super group: all groups can be queried.
        Non-super groups : query their own group.

         Args:
            target_group: the name you will find.
            Do not fill in the default query for your own group.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data ={
                "name": "super",
                "description": null,
                "users": [
                  "A_9527",
                  "A_9528"
                ],
                "leaders": [
                  "A_9527",
                  "A_9528"
                ],
                "counts": 2
              }

        """
        pass

    def create_group(self, group_name: str, description: str = ""):
        """
        create new group.
        Args:
            group_name: With the creation group name.
            description:Group description information.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        pass

    def change_user_group(self, target_user: str, target_group: str):
        """
        Modify the group that the user is in.
        Args:
            target_user: The user to be modified.
            target_group:The group that the user is going to.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        pass

    def change_group_leader(self, target_user: str, target_group: str, is_admin: bool):
        """
        Modify whether user in group becomes an administrator.
        Args:
            target_user: the user will be change.
            target_group: group name.
            is_admin: the user is admin, if true, the user will become admin.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        pass

    def change_platform_leader(self, username: str, platform_ident: str, is_admin: bool):
        """
        Modify whether user in group becomes an administrator.
        Args:
            username: the user will be change.
            platform_ident: platform_ident.
            is_admin: the user is admin, if true, the user will become admin.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        pass

    def query_group_info(self, target_group: str = None):
        """
        get group user.

        Args:
            target_group: the group name need query. if note group name query the group which self in.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data is a list, usually such as:
            data = [
                    {
                      "username": [
                        "A_9527",
                        "A_9528"
                      ],
                      "groups": "super",
                      "is_super": true,
                      "is_admin": true
                    }
                  ]
        """
        pass

    def query_perms_user_list(self, perm_type=None, platform_ident="", perms_ident="", group_name: str = ""):
        """
        get group user.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data is a list, usually such as:
            data = [
                    {
                      "username": [
                        "A_9527",
                        "A_9528"
                      ],
                      "groups": "super",
                      "is_super": true,
                      "is_admin": true
                    }
                  ]
        """
        pass

    def query_user_info(self, target_user: str = None):
        """
        query user details info.
        Args:
            target_user: the userame need query, if None  query self.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data is a dict, show user detail msg:
            data = {
                    "username": "A_9527",
                    "groups": "super",
                    "email": "<EMAIL>",
                    "phone_num": null,
                    "is_super": true,
                    "is_admin": true,
                    "status": 0,
                    "create_time": "2022-09-21 17:13:46",
                    "last_login_time": "2022-10-11 10:35:03",
                    "id": "632ad5caa90f6134fe0e9504"
                  }
        """
        pass

    def query_all_groups(self, perm_type=None, platform_ident="", perms_ident=""):
        """
        get system all groups.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data is a list, show all groups and user. such as:
            data = [
            {
              "name": "super",
              "description": null,
              "leaders": ["A_9527","A_9528"]
            },
            {
              "name": "normal",
              "description": null,
              "leaders": [  "A_9527","A_9528"]
            }
          ]
        """
        pass

    def query_group_names(self, all_group=True):
        """
        get system all group names.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data is a list, show all group`s name. such as:
            data = ["normal", "super"]
        """

    def query_user_config(self):
        """
        query user config info.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data is a dict, show user config info. such as:
            data = {
                "username": "A_9527",
                "system": {
                  "chip_label": "",
                  "platform": "",
                  "root": "",
                  "filepath": "",
                  "use_s3": false,
                  "qaio_type": "8bit",
                  "work_point": "freestyle"
                },
                "service": {
                  "qaio_ip": "",
                  "qaio_port": "",
                  "data_service_ip": "",
                  "data_service_port": ""
                },
                "minio": {
                  "address": "1",
                  "access_key": "",
                  "secret_key": ""
                }
              }
        """
        pass

    def change_user_config(self, system: Dict = None, service: Dict = None, minio: Dict = None):
        """
        modify user config info.
        Args:
            system: the user system config, if None system is null dict.
            service: the user service config, if None service is null dict.
            minio: the user s3 service config, if None s3 service is null dict.
            now s3 service is minio.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        pass

    # chip
    def query_chip_line(self):
        """
        get chip info that search by sample. sample set with INVOKER env.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            if os, return chip info, include sample, env_name, qubit and couple wiring information.
            such as:
            {
                "sample": "fivago",
                "env_name": "D5_env_0824",
                "QubitCount": 2,
                "CouplerCount": 1,
                "QubitParams": {
                  "q0": {
                    "xy_channel": 5,
                    "z_dc_channel": 1,
                    "z_flux_channel": 1,
                    "readout_channel": 1,
                    "probe_freq": 6546.2,
                    "sample_delay": 500,
                    "sample_width": 500
                  },
                  "q1": {
                    "xy_channel": 6,
                    "z_dc_channel": 2,
                    "z_flux_channel": 2,
                    "readout_channel": 1,
                    "probe_freq": 6401.6,
                    "sample_delay": 500,
                    "sample_width": 500
                  }
                },
                "CouplerParams": {
                  "c0": {
                    "drive_bit": 0,
                    "probe_bit": 1,
                    "z_dc_channel": 5,
                    "z_flux_channel": 5,
                    "probe_pi_width": 180,
                    "probe_pi_offset": 10
                  }
                }
              }

        """
        pass

    def create_chip_line(self, chip_line_dict: Dict):
        """
        create chip by chip_line_dict.
        Args:
            chip_line_dict:{
                "sample": str,
                "env_name": str,
                "QubitCount": str,
                "CouplerCount": str,
                "QubitParams": Dict,
                "CouplerParams": Dict
            }

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        pass

    def sync_chip_line(self):
        """
        sync chip line channel.
        Args:

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """

    def sync_chip_line_new(self):
        """
        sync chip line channel.
        Args:

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """

    def query_data_names(self):
        """
        Query user base data names, normal four data type.
        `Qubit`, `Coupler`, `QubitPair`, `Config`
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data:{
                "Qubit": ["q0", "q1", "q2", "q3" ],
                "Coupler": ["c0",  "c1",  "c2"  ],
                "QubitPair": ["q0q1"],
                "Config": ["distortion_q0.dat","q0.bin","crosstalk.json","character.json","instrument.json"]
              }

        """
        pass

    def query_config(self, config_file_name: Union[List[str], str]):
        """
        get config details file,
        Args:
            config_file_name: the config file name, if need file > 1, input filename list.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        pass

    def init_base_qubit_data(self, params: Dict,
                             bit_data: Dict = None,
                             bit_names: List = None,
                             delete: bool = False):
        """
        Initial user base data, by chip_line data.
        Args:
            params: the chip qubit freq and other params, such as
                    {"xy_baseband_freq": 1050, "m_baseband_freq": 1200}
            bit_data: init base qubit data {"qubit_fmt": {}, "coupler_fmt": {}}
            bit_names: init qubit names, if None: all bits
            delete: whether is init after delete

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        pass

    def init_config_data(self, params: Dict,
                         instrument_data: Dict,
                         base_qubit_names: list = None,
                         delete: bool = False):
        """
         Initial user base data, by chip_line data.
         Args:
             params: the chip qubit freq and other params, such as {"xy_baseband_freq": 1050, "m_baseband_freq": 1200}
             instrument_data: Chip measurement all-in-one machine data.
             base_qubit_names: list of qubits name, default None: all bits.
             delete: whether delete config and create new.
         Returns:
             the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

         """
        pass

    def update_single_config(self, file_name: str,
                             file_data: Union[bytes, Dict],
                             bin_abbr: Union[bytes, Dict] = None):
        """
        update config file, just could update or put one file pre time.
        Args:
            file_name: the config file name.
            file_data: the config file data, usually is bytes.
            bin_abbr: file data abbreviation

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        pass

    def update_many_config(self, conf_data: list):
        """
        update config file, just could update or put one file pre time.
        Args:
            conf_data: the config list [{filename: "", "file_data": ...}].

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        pass

    def query_other_user_data(self, username: str,
                              type_name: str = None,
                              sample: str = None,
                              env_name: str = None,
                              point_label: str = None):
        """
        Query other base data, by type_name.
        Args:
            username: prepare query the username.
            type_name: the config type, must in ["BaseQubit", "Config"], if None: all type
            sample (str):
            env_name (str):
            point_label (str):
        """
        pass

    def copy_other_user_data(self, from_user: str,
                             from_sample: str,
                             from_env_name: str,
                             from_point_label: str,
                             local: bool = True,
                             to_user: str = None,
                             to_sample: str = None,
                             to_env_name: str = None,
                             to_point_label: str = None,
                             element_names: List[str] = None,
                             element_configs: List[str] = None,
                             copy_qubit: bool = True):
        """
        copy another user config to self database.
        Args:
            from_user(str): from user`s username.
            from_sample(str): from sample name.
            from_env_name(str): from env_name name.
            from_point_label(str): from point_label name.
            local(bool): if True, copy data to local env and to_user、to_sample、
                        to_env_name、to_point_label default to myself local env data.
            to_user(str): copy to user`s username, if local is True, default=None: local user.
            to_sample(str): copy to sample, if local is True, default=None: local sample.
            to_env_name(str): copy to env_name, if local is True, default=None: local env_name.
            to_point_label(str): copy to point_label, if local is True, default=None: local point_label.
            element_names: the config params with type if None: all bits.
            element_configs: the config params with type if None: not copy config everything.
                             option from [character,crosstalk,distortion,bin,instrument,union_readout].
                             The bin option is very special, and when selected, copies all bits of bin data.
            copy_qubit(bool): whether copy qubits
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        pass

    def query_qcomponent(self, name: str = None, bit_id: str = None):
        """
        get the user bit params that search with point_label, sample and user.
        Args:
            name: the bit number. such as q1, c1
            bit_id: the bit id. such as "63f32eb80a8544e30801aa71"

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            if ok, return qubit dict, such as {q1}
            data = {
                      "name": "q1",
                      "sample": "fivago",
                      "username": "A_9527",
                      "point_label": "freestyle",
                      "bit_type": "Qubit",
                      "parameters": {
                        "xy_channel": 6,
                        "z_dc_channel": 2,
                        "z_flux_channel": 2,
                        "readout_channel": 1,
                        "probe_freq": 6401.6,
                        "sample_delay": 500,
                        "sample_width": 500,
                        "XYwave": {
                          "drive_IF": 566.667
                        }
                      },
                      "create_time": "2022-09-26 20:58:25",
                      "id": "6331a1f146391b743245daec"
                    }
        """
        pass

    def query_qcomponent_list(self, name_list: List[str], chip_type: int = 6):
        """
                get the user bit params that search with point_label, sample and user.
                Args:
                    name_list: the bit number list. such as [q1, c1, q2]
                    chip_type: user to query qubits info when the name_list is None or [], default 6 bit chip.
                Returns:
                    the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
                    if ok, return qubit info list, such as [{q1}, {c1}, {q2}]
                    data = [{
                              "name": "q1",
                              "sample": "fivago",
                              "username": "A_9527",
                              "point_label": "freestyle",
                              "bit_type": "Qubit",
                              "parameters": {
                                "xy_channel": 6,
                                "z_dc_channel": 2,
                                "z_flux_channel": 2,
                                "readout_channel": 1,
                                "probe_freq": 6401.6,
                                "sample_delay": 500,
                                "sample_width": 500,
                                "XYwave": {
                                  "drive_IF": 566.667
                                }
                              },
                              "create_time": "2022-09-26 20:58:25",
                              "id": "6331a1f146391b743245daec"
                            }
                            ]
                """
        pass

    def update_qcomponent(self, name: str, params: Dict, update_list: list = None,
                          record_id: str = "",
                          source: Dict = None):
        """
        update single qubit parameters, pre time just put one qubit.
        Args:
            name: bit name, such as "q1", "c1".
            params: qubits params.
            update_list: need update params to database, such as ["XY_Wave.Xpi", "probe_power"]
            record_id(str): experiment record id
            source(Dict): change source data

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        pass

    def update_qcomponent_list(self, bits: list, update_list: List = None,
                               record_id: str = "",
                               source: Dict = None):
        """
        update many bits parameters, pre time just put one qubit.
        Args:
            bits(list[dict]): [{
                                "name": “”,
                                "parameters":{}
                                }]
            update_list(List(str)): update parameter`s name
            record_id(str): experiment record id
            source(Dict): change source data

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """
        pass

    def query_qcomponent_history(
            self,
            name: str,
            username: str = None,
            sample: str = None,
            env_name: str = None,
            point_label: str = None,
            page_num: int = 1,
            page_size: int = 10
    ):
        """
         query bit history.
        Args:
            name: the query bit name.
            username: str username, default None, find self config.
            sample: str sample, default None, find self config.
            env_name: str env_name, default None, find self config.
            point_label: str point_label, default None, find self config.
            page_num: The number of query starting bars.
            page_size: The number of records per query.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data is a list, per bit record is a dict, such as:
            [
                {
                  "name": "q0",
                  "sample": "fivago",
                  "username": "xiao",
                  "point_label": "freestyle",
                  "bit_type": "Qubit",
                  "parameters": {
                    "bit": 0,
                    "name": "q0",
                    "sample": "fivago",
                    "point_label": "freestyle",
                    "tunable": true,
                    "goodness": false,
                    "drive_freq": 5550.128,
                    "drive_power": -21.3,
                    "probe_freq": 6545.208,
                    "probe_power": -20,
                    "tls_freq": 6000,
                    "anharmonicity": 250,
                    "dc": 0.744732,
                    "dc_max": 0.740594,
                    "dc_min": -0.221014,
                    "ac": 0,
                    "T1": 11778.8,
                    "T2": 17954.03319439,
                    "z_flux_channel": 1,
                    "z_dc_channel": 1,
                    "update_time": null,
                    "idle_point": 0,
                    "_row": null,
                    "_col": null,
                    "xy_channel": 5,
                    "readout_channel": 1,
                    "sample_delay": 500,
                    "sample_width": 2850,
                    "XYwave": {
                      "Xpi": 0.5,
                      "Xpi2": 0.33165,
                      "Ypi": 1,
                      "Ypi2": 0.5,
                      "Zpi": 1,
                      "drive_IF": 566.667,
                      "delta": -240,
                      "detune_pi": -7,
                      "detune_pi2": -7,
                      "alpha": 1,
                      "offset": 5,
                      "time": 20
                    },
                    "Zwave": {
                      "width": 1000,
                      "amp": 1
                    },
                    "Mwave": {
                      "width": 4000,
                      "amp": 0.7,
                      "IF": 600
                    },
                    "union_readout": {
                      "width": 4000,
                      "amp": 0.7,
                      "index": [],
                      "probe_IF": 578.65
                    },
                    "readout_point": {
                      "amp": 0,
                      "sigma": 1.25,
                      "buffer": 5
                    }
                  },
                  "create_time": "2022-10-11 10:29:39",
                  "id": "6344d5134ccd5279f4112b50"
                }]
        """
        pass

    def query_bit_attr_history(self, name: Union[str, List],
                               attr: Union[str, List],
                               time_start: Union[str, datetime] = None,
                               time_end: Union[str, datetime] = None,
                               value_min: Union[int, float] = None,
                               value_max: Union[int, float] = None,
                               step: Union[int, str] = None,
                               origin: bool = None):
        """

        Args:
            name:   qubit/pair name, support many bits, such as "q0,q1" or ["q0", "q1"]
            attr:   qubit/pair only one attribute. Note if it is an attribute under the secondary menu,
                    such as: "dc_min" "dc_max" "XYwave.Xpi" "f12_options.delta" "Zwave.width"
            time_start:  start time, "2023-09-05 00:00:00"
            time_end:    end time, "2023-09-05 00:00:00"
            value_min:   value minimum
            value_max:   value maximum
            step:  Filter step size, unit: minute
            origin: whether return origin data, if True: Filters such as value_min, value_max, and step become invalid.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data is a dict, per attr record is a dict, such as:
            {
                "probe_freq": {
                    "q41": {
                        "x_data": [...],
                        "y_data": [...],
                        "x_label": "Datetime"，
                        "y_label": "probe_freq"
                    },
                    "q59": {
                        "x_data": [...],
                        "y_data": [...],
                        "x_label": "Datetime"，
                        "y_label": "probe_freq"
                    }
                },
                "dc_max": {
                    "q41": {
                        "x_data": [...],
                        "y_data": [...],
                        "x_label": "Datetime"，
                        "y_label": "probe_freq"
                    },
                    "q59": {
                        "x_data": [...],
                        "y_data": [...],
                        "x_label": "Datetime"，
                        "y_label": "probe_freq"
                    }
            }
        """

    def query_chip_all(
            self,
            qid: str = None,
            name: Union[str, list] = None,
            username: str = None,
            sample: str = None,
            env_name: str = None,
            point_label: str = None
    ):
        """
        query chip all config and all bit.
        This interface is used to query other people's chip configuration information and parameters.
        Args:
            qid: base qubit id
            name: str config name, such as q1, crosstalk,default None, find all.
            username: str username, default None, find self config.
            sample: str sample name, default None, find all.
            point_label: str point_label, default None, find all.
            env_name: str env name, default None, find all.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            return a dict, the key is configfile name, such as :
            {
            "q0": ...,
            "distortion_q0.dat": ...,
            }
        """
        pass

    def query_chip_all_bit(self, name: Union[str, list] = None, username: str = None, sample: str = None,
                           env_name: str = None, point_label: str = None, qid: str = None):
        """
        query chip all bit.
        This interface is used to query other people's chip configuration information and parameters.
        Args:
            name: str config name, such as q1, crosstalk,default None, find all.
            username: str username, default None, find self config.
            sample: str sample name, default None, find all.
            point_label: str point_label, default None, find all.
            env_name: str env name, default None, find all.
            qid: qubit id or coupler id
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            return a dict, the key is configfile name, such as :
            {
            "q0": ...,
            "distortion_q0.dat": ...,
            }
        """

    def query_chip_all_config(self, name: Union[str, list] = None, username: str = None, sample: str = None,
                              env_name: str = None, point_label: str = None, qid: str = None):
        """
        query chip all config.
        This interface is used to query other people's chip configuration information and parameters.
        Args:
            name: str config name, such as q1, crosstalk,default None, find all.
            username: str username, default None, find self config.
            sample: str sample name, default None, find all.
            point_label: str point_label, default None, find all.
            env_name: str env name, default None, find all.
            qid: qubit id or coupler id
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            return a dict, the key is configfile name, such as :
            {
            "q0": ...,
            "distortion_q0.dat": ...,
            }
        """

    def query_chip_all_simple(self, name: Union[str, list] = None, username: str = None, sample: str = None,
                              env_name: str = None, point_label: str = None, qid: str = None):
        """
        query chip all config and bits simple info, such as [id,name,create_time/update_time].
        This interface is used to query other people's chip configuration information and parameters.
        Args:
            name: str config name, such as q1, crosstalk,default None, find all.
            username: str username, default None, find self config.
            sample: str sample name, default None, find all.
            point_label: str point_label, default None, find all.
            env_name: str env name, default None, find all.
            qid: qubit id or coupler id
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            return a dict, the key is configfile name, such as :
            {
            "q0": ...,
            "distortion_q0.dat": ...,
            }
        """

    # dag
    def query_dag_list(self):
        """
        get user dag list.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            if ok, the return data is list. such as:
             [
                {
                  "username": "A_9527",
                  "dag_name": "single qubit dag 0",
                  "execute_params": {
                    "is_traceback": false,
                    "is_report": false,
                    "ajust_params": {}
                  },
                  "official": true,
                  "create_time": "2022-09-10 10:07:44",
                  "id": "6327cef05fda5c2f52568bf4"
                }
              ]

        """
        pass

    def query_dag_details(self, name: str):
        """
        query single dag details by dag name.
        Args:
            name: dag name.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data: {
                    "username": "A_9527",
                    "dag_name": "single qubit dag 0",
                    "node_edges": [],
                    "execute_params": {
                      "is_traceback": true,
                      "is_report": false,
                      "ajust_params": {}
                    },
                    "official": true,
                    "node_params": {},
                    "create_time": "2022-09-10 10:07:44",
                    "id": "6327cef05fda5c2f52568bf4"
                  }

        """
        pass

    def create_dag(self, name: str, node_edges: Dict, execute_params: Dict, node_params: Dict):
        """
        create new dag.
        Args:
            name: dag name.
            node_edges: node e-v dict.
            execute_params: execute params{"is_traceback": bool, "is_report": bool, "ajust_params":dict}
            node_params: node params
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        pass

    def delete_dag(self, name: str):
        """
        delete normal dag.
        Args:
            name: dag name.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    # def update_dag(self, name: str, node_edges: Dict, execute_params: Dict, node_params: Dict):
    #     """
    #     update dag.
    #     Args:
    #         name: dag name.
    #         node_edges: node e-v dict.
    #         execute_params: execute params{"is_traceback": bool, "is_report": bool, "ajust_params":dict}
    #         node_params: node params
    #     Returns:
    #         the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
    #     """
    #     pass

    def execute_dag(self, name: str, node_edges: Dict, execute_params: Dict, node_params: Dict, is_save: bool,
                    conf_work: list = None):
        """
        update dag.
        Args:
            name: dag name.
            node_edges: node e-v dict.
            execute_params: execute params{"is_traceback": bool, "is_report": bool, "ajust_params":dict}
            node_params: node params
            is_save: bool
            conf_work: list, the dag env working volt, such as [["q0","DC",4.22],["q1","DC",4.12]]
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        pass

    def put_node_result(self, id: str, node_id: str, result: Dict, loop_flag: bool = False):
        """

        Args:
            id:dag history id.
            node_id: the node id.
            result: the node result.
            loop_flag: the dag run end flag, if true, the dag execute end. default is False.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """

        pass

    def query_dag_execute_history(self, dag_name: str, page_num: int = 1, page_size: int = 20):
        """
        Query DAG execution records.
        By default, the interface displays the latest 20 pieces of data,
        which can be used to query the previous records using page num and page size.

        Args:
            dag_name: DAG name.
            page_num: The number of query starting bars.
            page_size: The number of records per query.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data is a list, Per record is a dict such as :
            [
                {
                  "execute_params": {},
                  "official": false,
                  "dag_id": "6327cef05fda5c2f52568bf5",
                  "username": "xiao",
                  "dag_name": "single qubit dag",
                  "node_edges": {},
                  "node_params": {},
                  "node_result": {},
                  "dag_report": "",
                  "execute_node": ["A","B"],
                  "traceback_note": [ 0, 0],
                  "version": "0.0.1",
                  "create_time": "2022-09-19 10:07:54",
                  "id": "6327d0f573d591592b670dd5"
                }
              ]
        """
        pass

    def query_dag_record(self, dag_name: str = None, dag_id: str = None):
        """
        Query DAG execution records.
        By default, the interface displays the latest 20 pieces of data,
        which can be used to query the previous records using page num and page size.

        Args:
            dag_name: DAG name.
            dag_id: DAG id.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            data is a list, Per record is a dict such as :
                {
                  "execute_params": {},
                  "official": false,
                  "dag_id": "6327cef05fda5c2f52568bf5",
                  "username": "xiao",
                  "dag_name": "single qubit dag",
                  "node_edges": {},
                  "node_params": {},
                  "node_result": {},
                  "dag_report": "",
                  "execute_node": ["A","B"],
                  "traceback_note": [ 0, 0],
                  "version": "0.0.1",
                  "create_time": "2022-09-19 10:07:54",
                  "id": "6327d0f573d591592b670dd5"
                }
        """
        pass

    def reset_user_standard_dag(self, dag_name: str = None, dag_id: str = None):
        """
        reset user standard dag struct and params to default standard.
        Args:
            dag_name: DAG name.
            dag_id: DAG id.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        pass

    def set_dag_history_conf(self, dag_id: str, conf_pre: Dict = None, conf_suf: Dict = None, conf_work: Dict = None):
        """
        set dag history config. use to report and dag execute.
        Args:
            dag_id: Dag history id.FD
            conf_pre: qubit info set before node execute.
            conf_suf: qubit info set after node execute.
            conf_work: wording dc set before node execute.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def query_usernames(self, groups: str = None):
        """
        query usernames list for users
        if groups: query usernames for groups
        else: all usernames
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def query_sample_list(self, username: str):
        """
        query all samples for one user
        Args:
            username: the user username.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def query_env_name_list(self, username: str, sample: str):
        """
        query all env_names for user and sample
        Args:
            username: the user username.
            sample: str sample name
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def query_point_label_list(self, username: str, sample: str, env_name: str):
        """
        query all env_names for user and sample and env_name
        Args:
            username: the user username.
            sample: str sample name
            env_name: str env name
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def query_conf_type_list(self):
        """
        Query the type of the config file.
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
            data: ["character", "crosstalk", "distortion", "bin"]
        """

    def courier_version(self):
        """
        query server version (courier version)
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def get_user_test(self, start_date: Union[datetime, str],
                      end_date: Union[datetime, str] = None,
                      username: str = None, aggregate: bool = False):
        """
        Args:
            start_date: start date.
            end_date:   end date, default is None, means same to start date.
            username:  default is None, means all user.
            aggregate: whether to aggregate date query.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        pass

    def query_version(self):
        pass

    def get_simulator_data(self, exp_name: str, index: int = 0,
                           simulator_remote_path: str = None):
        """
        get simulator data to use simulator
        Args:
            exp_name: experiment name
            index: default is 0, when CompositeExperiment must be input index
            simulator_remote_path: simulator path

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def query_cache_data(self):
        """
        query user`s cache data
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def update_cache_data(self, parameters: Dict):
        """
        update user`s cache data
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def test_connect(self):
        pass

    def query_chip(self, groups: str = None,
                   sample: str = None,
                   env_name: str = None,
                   show_all: bool = False):
        """
        Query the chip
        Args:
            groups: the user group.
            sample: str sample name
            env_name: str sample name
            show_all: show all chip
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """

    def create_chip(self,
                    sample: str,
                    env_name: str,
                    inst_ip: str,
                    inst_port: Union[str, int] = 27017,
                    groups: str = None,
                    core_num: Union[str, int] = 1,
                    debug: Union[str, int] = 0,
                    window_size: Union[str, int] = 10,
                    alert_dis: Union[str, int] = 1,
                    secure_dis: Union[str, int] = 2, **kwargs):
        """
        create the chip
        Args:
            groups: the user group.
            sample: str sample name
            env_name: str sample name
            inst_ip: str qstream db ip
            inst_port: str qstream db port
            core_num: thread core num
            debug: whether it use debug
            window_size:
            alert_dis:
            secure_dis:
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """

    def update_chip(self,
                    sample: str,
                    env_name: str,
                    inst_ip: str,
                    inst_port: int = 27017,
                    groups: str = None,
                    core_num: int = 1,
                    debug: int = 0,
                    window_size: int = 10,
                    alert_dis: int = 1,
                    secure_dis: int = 2, **kwargs):
        """
        update the chip
        Args:
            groups: the user group.
            sample: str sample name
            env_name: str sample name
            inst_ip: str qstream db ip
            inst_port: str qstream db port
            core_num: thread core num
            debug: whether it use debug
            window_size:
            alert_dis:
            secure_dis:
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """

    def delete_chip(self,
                    sample: str,
                    env_name: str):
        """
        delete the chip
        Args:
            sample: str sample name
            env_name: str sample name
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """

    def query_workspace(self, username: str = None, sample: str = None, env_name: str = None):
        """
        Query user workspace
        Args:
            username: the user group.
            sample: str sample name
            env_name: str sample name
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """

    def update_workspace(self, username: str,
                         sample: str,
                         env_name: str,
                         bit_names: List,
                         conf_names: List):
        """
        create/update user workspace
        Args:
            username: the user group.
            sample: str sample name
            env_name: str sample name
            bit_names: str qubit names
            conf_names: str config names
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """

    def delete_workspace(self, username: str = None, sample: str = None, env_name: str = None):
        """
        delete user workspace
        Args:
            username: the user group.
            sample: str sample name
            env_name: str sample name
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """

    def workspace_info(self):
        """
        Query self workspace info
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """

    def workspace_set_auto(self, auto_push: int = None, auto_pull: int = None):
        """
        setting auto pull/push option
        Args:
            auto_push(int): 0 / 1 / None
            auto_pull(int): 0 / 1 / None
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """

    def workspace_pull_push(self, option: Union[bool, int, str] = None):
        """
        pull/push user workspace qubits or configs
        Args:
            option(bool, int, str): if option: pull else False(except None)
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """

    def control_qs_server(self, sample: str, env_name: str, option: str):
        """
        start/stop/restart qs server
        Args:
            sample(str):
            env_name(str):
            option(str): start/stop/restart
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """

    def sync_qs_status(self, sample: str = None, env_name: str = None):
        """
        sync qs status
        Args:
            sample(str):
            env_name(str):
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """

    def query_chip_sample_data(self):
        """
        query chip data for sample/env_name data
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """

    def query_chip_line_space(self, sample: str, env_name: str):
        """
        query qubit/config for chip_line data
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """

    def query_workspace_his(self, username: str = None,
                            sample: str = None,
                            env_name: str = None,
                            name: str = None,
                            page_num: int = 1,
                            page_size: int = 10):
        """
        query workspace history
        Returns:
            the standard response.{"code": (int)xxx, "data":[], msg:(str) ok}
        """

    def run_experiment(self, task: Dict):
        """
        run experiment
        Args:
            task: experiment data

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def delete_experiment(self, task: Union[str, List[str]], force: bool = True):
        """
        delete experiment, use to remove task if task send to chimera but not running.
        Args:
            task: experiment task id or id list.
            force: whether force to stop exp task.
        Returns:
            the standard response.
        """

    def broadcast_chip(self, state: Union[str, bytes], data: Union[str, bytes] = None):
        """
        send to courier for broadcast to chip
        Args:
            state: b"calibrate_start", b"calibrate_end", b"qs_state"
            data:

        Returns:

        """

    def broadcast_user(self, username: str, state: Union[str, bytes], data: Union[str, bytes] = None):
        """
        send to courier for broadcast to user
        Args:
            username:
            state:
            data:

        Returns:

        """

    def query_storm_list(self, sample: str, env_name: str):
        """
        query storm info list
        Args:
            sample:
            env_name:

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """

    def update_storm(self, sample: str, env_name: str):
        """
        add/update storm
        Args:
            sample:
            env_name:

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """

    def delete_storm(self, sample: str, env_name: str):
        """
        delete storm
        Args:
            sample:
            env_name:

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """

    def control_storm(self, sample: str, env_name: str, option: str):
        """
        control storm
        Args:
            sample:
            env_name:
            option: start/stop

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """

    def query_storm_sample_data(self):
        """
        query storm sample/env_name data
        Args:

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """

    def query_storm_conf(self, sample: str, env_name: str):
        """
        query storm sample/env_name data
        Args:
            sample:
            env_name:

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """

    def update_storm_conf(self, sample: str, env_name: str, online_conf: Dict):
        """
        query storm sample/env_name data
        Args:
            sample:
            env_name:
            online_conf: chip online qubit config

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """

    def save_workspace_info(self, bit_names: Union[List, str],
                            conf_names: Union[List, str],
                            point_label: Union[List, str] = None,
                            bit_attr: Union[List, str] = None):
        """
        query storm sample/env_name data
        Args:
            bit_names:  workspace qubit names
            conf_names: workspace config names
            point_label: workspace point_label names
            bit_attr: qubit attr names

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """

    def check_user_version(self, username: str, version: str):
        """check user version"""
        pass

    def query_custom_task(self, task_id: str = None,
                          task_name: str = None,
                          sub_type: str = None,
                          sub_name: str = None):
        """
        query custom task list
        Args:
            task_id: task id.
            task_name: task name.
            sub_type: task type: Dag/Exp
            sub_name: Dag name/ Exp name
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def add_custom_task(self, task_data: Dict):
        """
        add custom task
        Args:
            task_data: task data.
                    {
                        "task_name": "interval 10 min",
                        "task_desc": "calibration per 10minute",
                        "username": "",
                        "policy":{
                          "type": "schedule/repeat",
                          "options":{}#实际运行的策略信息
                        },
                        "status": "stopped",
                        "sub_type": "",
                        "sub_name":"exp_name/dag_name"
                    }

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def update_custom_task(self, task_data: Dict):
        """
        update custom task
        Args:
            task_data: task data.
                    {
                        "task_name": "interval 10 min",
                        "task_desc": "calibration per 10minute",
                        "username": "",
                        "policy":{
                          "type": "schedule/repeat",
                          "options":{}#实际运行的策略信息
                        },
                        "status": "stopped",
                        "sub_type": "",
                        "sub_name":"exp_name/dag_name"
                    }

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def delete_custom_task(self, task_name: str):
        """
        delete custom task list
        Args:
            task_name: task name.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def add_custom_task_his(self, task_data: Dict, bit_data: Dict):
        """
        add custom task note
        Args:
            task_data: task data.
                    {
                        "task_name": "interval 10 min",
                        "task_desc": "calibration per 10minute",
                        "username": "",
                        "policy":{
                          "type": "schedule/repeat",
                          "options":{}#实际运行的策略信息
                        },
                        "status": "stopped",
                        "sub_type": "",
                        "sub_name":"exp_name/dag_name"
                    }
            bit_data: {
                        "qubits":["q0", "q1", "q2", "q3"],
                        "couplers":["c0-1", "c11-12", c"2-3"],
                        "pairs": ["q0q1", "q2q3"],
                        "discriminators":["q0", "q1", "q2", "q3"],
                        "compensates":["q0", "q1", "q2", "q3"],
                        }

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            {"doc_id": "id"}
        """

    def update_custom_task_his(self, doc_id: str, task_data: Dict = None, bit_data: Dict = None):
        """
        update custom task note done
        Args:
            doc_id: task id
            task_data: task data. default to None, not update.
                    {
                        "task_name": "interval 10 min",
                        "task_desc": "calibration per 10minute",
                        "username": "",
                        "policy":{
                          "type": "schedule/repeat",
                          "options":{}#实际运行的策略信息
                        },
                        "status": "stopped",
                        "sub_type": "",
                        "sub_name":"exp_name/dag_name"
                    }
            bit_data: default to None, not update.
                    {
                        "qubits":["q0", "q1", "q2", "q3"],
                        "couplers":["c0-1", "c11-12", c"2-3"],
                        "pairs": ["q0q1", "q2q3"],
                        "discriminators":["q0", "q1", "q2", "q3"],
                        "compensates":["q0", "q1", "q2", "q3"],
                        }

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def query_custom_task_his(self, doc_id: str = None,
                              task_name: str = None,
                              sub_type: str = None,
                              sub_name: str = None,
                              sample: str = None,
                              env_name: str = None,
                              point_label: str = None,
                              ):
        """
        query custom task list for history
        Args:
            doc_id: task id.
            task_name: task name.
            sub_type: task type: Dag/Exp
            sub_name: Dag name/ Exp name
            sample:
            env_name:
            point_label:
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def copy_workspace(self,
                       from_user: str,
                       from_sample: str,
                       from_env: str,
                       to_user: str):
        """
        copy workspace range
        Args:
            from_user:  from username
            from_sample: from sample
            from_env: from env_name
            to_user: copy to username

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """

    def compare_exp_policy(self, exp_ids: Union[str, List[str]]):
        """
        Compare the calibration strategies between different experiments
        Args:
            exp_ids: experiment id or id list.
        Returns:
            the standard response.
        """

    def query_exp_policy(self,
                         exp_id: Union[str, List[str]] = None,
                         username: str = None,
                         exp_name: str = None,
                         sample: str = None,
                         env_name: str = None,
                         page_num: int = 1,
                         page_size: int = 10):
        """
        Compare the calibration strategies between different experiments
        Args:
            exp_id: experiment id or id list.
            username: username default to None：all user
            exp_name: .
            sample: .
            env_name: .
            page_num: .
            page_size: .
        Returns:
            the standard response.
        """

    def add_workspace_info(self, work_type: int,
                           bit: Union[str, List] = None,
                           bit_attr: str = None,
                           point_label: str = None):
        """
        add workspace infos, only add
        Args:
            work_type:  0,1,2
            bit: bit name(work_type=0)
            bit_attr: bit attribute(work_type=1)
            point_label: point label(work_type=2)

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """

    def delete_workspace_info(self, work_type: int,
                              bit: Union[str, List] = None,
                              bit_attr: str = None):
        """
        delete workspace infos, only delete
        Args:
            work_type:  0,1
            bit: bit name(work_type=0)
            bit_attr: bit attribute(work_type=1)

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """

    def revert_bit(self, doc_id: str):
        """
        revert BaseQubitHis bit to local
        Args:
            doc_id:  QubitHistory doc_id

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """

    def query_task_performer(self, task_ids: Union[list, str]):
        """
        Query the execution time of a task in each module.
        Args:
            task_ids: task id or id list.
        Returns:
            the standard response.
        """

    def query_revert_bits(self, time_node: str):
        """
        query revert BaseQubitHis many bits to local (for time node)
        Args:
            time_node:  datetime str (%Y-%m-%d %H:%M:%S)

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """

    def revert_more_bits(self, time_node: str):
        """
        revert BaseQubitHis many bits to local (for time node)
        Args:
            time_node:  datetime str (%Y-%m-%d %H:%M:%S)

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}

        """

    def query_white_list(self):
        """query user white list"""

    def add_white_list(self, username: str):
        """add user to white list"""

    def remove_white_list(self, username: str):
        """remove user to white list"""

    def query_black_list(self):
        """query user black list"""

    def add_black_list(self, username: str):
        """add user to black list"""

    def remove_black_list(self, username: str):
        """remove user to black list"""

    def get_exclusive_user(self):
        """get exclusive user and expire_time"""

    def add_exclusive_date(self, username: str,
                           start_time: str,
                           timeout: int,
                           sample: str = None,
                           env_name: str = None):
        """add chimera exclusive user from once scheduler(trigger: date)
        Args:
            username(str):
            start_time(str): "%Y-%m-%d %H:%M:%S" -> "2024-01-25 12:00:03"
            timeout(int): timeout seconds, Radius(10 minute, 10 hour)
            sample(str): default to None(self env)
            env_name(str): default to None(self env)
        Returns
            code
                200： success
                400： parameters validate error
                403: Cannot be created because it is already occupied

        """

    def del_exclusive_user(self,
                           sample: str = None,
                           env_name: str = None):
        """del chimera exclusive user
        Args:
            sample(str): default to None(self env)
            env_name(str): default to None(self env)

        """

    def add_exclusive_cron(self, username: str,
                           timeout: int,
                           sample: str = None,
                           env_name: str = None,
                           year: str = None,
                           month: str = None,
                           week: str = None,
                           day: str = None,
                           hour: str = None,
                           minute: str = None,
                           second: str = None,
                           ):
        """add chimera exclusive user from once scheduler(trigger: cron)
        Args:
            username(str):
            timeout(int):(seconds) Valid timeout period
            sample(str): default to None(self env)
            env_name(str): default to None(self env)
            year(str): such as "2024"  "*/2"  "2024,2025"  "*"
            month(str): (1-12) such as "1"  "*/2"  "1,2,5"  "*"
            week(str): (0-6)   such as "1"  "*/2"  "1,2,5"  "*"
            day(str): (1-31)   such as "3"  "*/2"  "1,2,5"  "*"
            hour(str): (0-23)   such as "4"  "*/2"  "1,2,5"  "*"
            minute(str): (0-59)   such as "5"  "*/2"  "1,2,5,7"  "*"
            second(str): (0-59)   such as "6"
        Returns
            code
                200： success
                400： parameters validate error
                403: Cannot be created because it is already occupied

        """

    def add_exclusive_interval(self, username: str,
                               timeout: int,
                               sample: str = None,
                               env_name: str = None,
                               weeks: int = None,
                               days: int = None,
                               hours: int = None,
                               minutes: int = None,
                               start_time: str = None,
                               end_time: str = None):
        """add chimera exclusive user from once scheduler(trigger: interval)
        Args:
            username(str):
            timeout(int):(seconds) Valid timeout period
            start_time(str): "%Y-%m-%d %H:%M:%S" -> "2024-01-25 12:00:03"
            end_time(str): "%Y-%m-%d %H:%M:%S" -> "2025-01-25 12:00:03"
            sample(str): default to None(self env)
            env_name(str): default to None(self env)
            weeks(int): The number of weeks between triggers.
            days(int): The number of days between triggers.
            hours(int): The hours of days between triggers.
            minutes(int): The minutes of days between triggers.
            # seconds(int): The seconds of days between triggers.
        Returns
            code
                200： success
                400： parameters validate error
                403: Cannot be created because it is already occupied

        """

    def test_token(self, token: str):
        pass

    def query_perms_list(self, group: str = None, username: str = None):
        """
        get permission for self
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def query_platform(
            self,
            page_num: int = 1,
            page_size: int = 20,
    ):
        """
        get platform of permission
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def add_platform(self, sample: str, env_name: str):
        """
        add platform of permission
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def del_platform(self, name: str, sample: str = None, env_name: str = None):
        """
        delete platform of permission(super)
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def query_perms_group(self, group: str = None, platform_ident: str = ""):
        """
        query group of permission(super)
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def add_perms_group(self, perms_ids: Union[str, List], group: str, platform_ident: str = ""):
        """
        add group of permission(super)
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def del_perms_group(self, perms_ids: Union[str, List], group: str, platform_ident: str = ""):
        """
        delete group of permission(super)
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def query_perms_user(self, username: str = None, platform_ident: str = ""):
        """
        query user of permission(admin/super)
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def add_perms_user(self, perms_ids: Union[str, List], username: str, platform_ident: str = ""):
        """
        add user of permission(admin/super)
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def del_perms_user(self, perms_ids: Union[str, List], username: str, platform_ident: str = ""):
        """
        delete user of permission(admin/super)
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def query_perms_note(self, username: str = None,
                         types: str = None,
                         perms_id: str = None,
                         page_num: int = 1,
                         page_size: int = 10):
        """
        query change note of permission(admin/super)
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
    def query_platform_perms(self, perms_id: str = None,
                             perms_name: str = None,
                             perms_type: str = None):
        """
        query platform of permission
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def operate_platform_perms(self, perms_id: str = None,
                               perms_name: str = None,
                               perms_type: str = None,
                               target: Union[str, List] = None):
        """
        operate platform of permission (group/username)
        target: usernames or group names
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """

    def calibration(
            self,
            item_type: str,
            cali_type: Union[str, int],
            total_num: int,
            success_num: int,
            fail_num: int,
            fault_num: int,
            result: Dict,
    ):
        """
        add auto calibration data.
        """

    def query_default_node(self):
        """query default node
        """


    def batch_start_signal(
            self,
            record_id: str,
            system_meta: Dict,
            execute_meta: Dict,
            context_meta: Dict,
    ):
        """
        add batch start record.
        """

    def batch_end_signal(
            self,
            **kwargs
    ):
        """
        update batch result.
        """

    def batch_flow_start_signal(
            self,
            batch_id: str,
            flow_id: str,
            name: str,
            exp_flows: List[str],
            start_time: str,
            physical_units: List[str],
            end_time: str = "",
            experiments: List[str] = None,
            pass_units: List[str] = None,
            fail_units: List[str] = None,
            *args,
            **kwargs
    ):
        """
        add batch flow record.
        """

    def batch_flow_end_signal(
            self,
            batch_id: str,
            flow_id: str,
            end_time: str,
            pass_units: List[str] = None,
            fail_units: List[str] = None,
    ):
        """
        update batch flow result.
        """

    def batch_exp_end_signal(
            self,
            batch_id: str,
            flow_id: str,
            record_id: str,
            name: str,
            physical_units: List[str],
            pass_units: List[str] = None,
            fail_units: List[str] = None,
            update_data: List = None,
            *args,
            **kwargs
    ):
        """
        update batch exp result.
        """

    def query_chip_env_id(self, sample: str = None,
                     env_name: str = None,
                     point_label: str = None,
                     username: str = None):

        """
        query chip env id
        """




class AsyncDataCenter(DataCenter):
    """
    async data center.
    """
    async_mode = True
