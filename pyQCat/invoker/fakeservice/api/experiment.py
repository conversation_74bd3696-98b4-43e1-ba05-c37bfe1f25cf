# from typing import Dict
from pyQCat.invoker.const import Dict
from .baseapi import BaseApi
from ..qcontrol import get_all_exp_list

class Chip(BaseApi):
    def get_exp_list(self):
        """
        get experiment list and options.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            the data such as
            {
                "exp_name": "",
                "exp_params": {},
                "official": false
              }
        """
        api_data = {}
        return get_all_exp_list()


    def save_exp_options(self, exp_name: str, exp_params: Dict):
        """
        save user experiment params and options in service.
        Args:
            exp_name: experiment name
            exp_params:the experiment

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "exp_name": exp_name,
            "exp_params": exp_params
        }
        # todo
        return {"code": 300, "data": {}, "msg": "todo"}

    def exp_execute(self, exp_id: str, exp_name: str, exp_type: str, extra: Dict):
        """

        Args:
            exp_id: experiment execute id.
            exp_name: experiment name, such as rabi.
            exp_type: single or composite.
            extra: the experiment extra info and params.

        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
        """
        api_data = {
            "exp_id": exp_id,
            "exp_name": exp_name,
            "exp_type": exp_type,
            "extra": extra
        }
        # todo
        return {"code": 300, "data": {}, "msg": "todo"}