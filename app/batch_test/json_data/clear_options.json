{"CavityFreqSpectrum_high_power": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "CavityFreqSpectrum", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"period": 50, "points": 101, "readout_power": -10, "pi_amp": null, "add_pi_pulse": false, "scope": 3, "z_amp": null, "mode": "IF", "save_result": false}, "analysis_options": {"quality_bounds": [0.98, 0.92, 0.85]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "T2SpinEcho": {"meta": {"username": "<PERSON><PERSON><PERSON><PERSON>", "visage_version": "0.23.2", "monster_version": "********", "chip": {"sample": "Reset_LRU_test", "env_name": "Y8", "point_label": "idle_point"}, "exp_class_name": "T2SpinEcho", "export_datetime": "2025-07-15 16:22:49", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q6"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"period": 100, "acq_index": "Points(0) | normal | None", "resource_filter": true, "open_half_pi": false, "union_mode": "union", "delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": "right"}, "run_mode": "sync", "async_time_out": 10, "quality_filter": false, "is_sub_merge": true, "minimize_mode": false, "delays": "Points(100) | qarange | (200, 20000, 200)", "fringe": 0.5, "z_amp": null, "rate_down": 0.3, "rate_up": 0.5, "max_loops": 5, "frequency": null, "ac_branch": "right"}, "analysis_options": {"pure_exp_mode": false, "save_s3": false, "save_exp_data": true, "child_ana_options": {"pure_exp_mode": false, "save_s3": false, "save_exp_data": true, "quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "fit_type": "osc"}, "fit_type": "osc"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XpiDetection": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "XpiDetection", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null}, "expect_value": 0.2, "scope": 0.1, "max_loops": 5, "name": "Xpi", "amps": "Points(51) | qarange | (0.0, 0.5, 0.01)", "drive_power": null}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.91]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XpiDetection_0": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "XpiDetection", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null}, "expect_value": 0.6, "scope": 0.1, "max_loops": 5, "name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": -15}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.91]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "ReadoutFreqCalibrate": {"meta": {"username": "zyc", "visage_version": "0.4.9", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "ReadoutFreqCalibrate", "export_datetime": "2024-01-31 16:36:31", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"time_perform": false, "fc_list": "Points(0) | normal | None", "points": 61, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "extend_f12": false, "scope": 3, "z_amp": null, "mode": "IF"}, "fc_list": "Points(0) | normal | None", "readout_power": null, "readout_type": "01", "save_result": false}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "save_mode": "max_distance_point", "diff_threshold": 0.1}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CavityFreqSpectrum_0": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "CavityFreqSpectrum", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"points": 61, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "scope": 3, "z_amp": null, "mode": "IF", "save_result": false}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.85]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "PhotonScanReadoutFreqV2": {"meta": {"username": "tuple_01", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "PhotonScanReadoutFreqV2", "export_datetime": "2024-04-17 14:07:36", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1,q2"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"exp_rd_pulse_params": null, "nor_rd_pulse_params": null, "exp_rd_pulse_type": "Square", "nor_rd_pulse_type": "Square", "init_state": 0, "xy_delay": 2500, "extra_delay": 50, "nor_rd_trigger": 300, "fake_pulse": false, "pre_amp": null, "pre_width": 3000, "scope": {"l": 150, "r": 20, "s": 2}, "mode": "BF"}, "sweep_list": null, "scope": {"l": 0.25, "r": 0.25, "s": 0.05}}, "analysis_options": {"quality_bounds": [0.98, 0.8, 0.75], "child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "effective_chi": null}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {"child_ana_options": {"effective_chi": {"q1": null}}}}}, "SingleShot_0": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "SingleShot", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"level_str": "01"}, "analysis_options": {"quality_bounds": [3, 0.7, 0.6, 0.03], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {}}, "PhotonNumMeasVsAmp": {"meta": {"username": "tuple_01", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "PhotonNumMeasVsAmp", "export_datetime": "2024-04-17 14:11:05", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1,q2"}, "options_for_regular_exec": {"experiment_options": {"async_time_out": 10, "run_mode": "async", "child_exp_options": {"exp_rd_pulse_params": null, "nor_rd_pulse_params": null, "exp_rd_pulse_type": "Square", "nor_rd_pulse_type": "Square", "init_state": 0, "xy_delay": 2500, "extra_delay": 50, "nor_rd_trigger": 300, "fake_pulse": false, "pre_amp": null, "pre_width": 3000, "scope": {"l": 120, "r": 20, "s": 2}, "mode": "BF"}, "amp_list": "Points(3) | qarange | (0.00, 0.2, 0.01)"}, "analysis_options": {"kappa": null, "child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "effective_chi": null}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "NMClearParamsBoth": {"meta": {"username": "tuple_01", "visage_version": "0.5.1", "monster_version": "********", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "NMClearParamsBoth", "export_datetime": "2024-04-17 17:52:17", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1,q2"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(121) | qarange | (0.0, 300.0, 5)", "fringe": 25, "t_relax": 50, "t_buffer": 100, "init_state": 0, "is_amend": true, "fake_pulse": false, "fidelity_correct_type": "ibu", "acq_pulse_params": null, "acq_pulse_type": "Clear"}, "mode": "NM", "input_data": {"amp1": {"is_opt": true, "init_v": null, "iter_step": 0.001, "bound": [-0.9, 0.0], "nonzdelt": 0.08}, "amp2": {"is_opt": true, "init_v": null, "iter_step": 0.001, "bound": [0.0, 0.8], "nonzdelt": 0.08}, "tkick": {"is_opt": false, "init_v": null, "iter_step": 10, "bound": [50, 150]}}, "nm_params": {"ftarget": 0, "maxiter": 20, "maxfev": 20, "xatol": 0.001, "fatol": 0.01, "adaptive": false}}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "kappa": null, "chi": null, "t2echo": null, "raw_data_format": "plot"}}}, "options_for_parallel_exec": {"experiment_options": {"child_exp_options": {"clear_params": {"q1": {}, "q2": {}}}}, "analysis_options": {"child_ana_options": {"quality_bounds": {"q1": [0.98, 0.93, 0.81], "q2": [0.98, 0.93, 0.81]}, "kappa": {"q1": null, "q2": null}, "chi": {"q1": null, "q2": null}, "t2echo": {"q1": null, "q2": null}}}}}}