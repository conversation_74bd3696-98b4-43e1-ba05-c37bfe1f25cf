{"SingleShot": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "SingleShot", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q4,q10"}, "options_for_regular_exec": {"experiment_options": {"level_str": "01"}, "analysis_options": {"quality_bounds": [2, 0.8, 0.7, 0.03], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {}}, "QubitFreqCalibration_0": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "QubitFreqCalibration", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(41) | qarange | (0, 200, 5)", "fringe": 25, "z_amp": null, "frequency": null, "ac_branch": null, "is_dynamic": 0}, "fringes": "Points(2) | qarange | (41, -41, -82)", "delays": "Points(71) | qarange | (50, 150, 2.5)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.7, 0.5], "factor": 3.5}, "subplots": [2, 2], "freq_gap_threshold": 2}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XpiDetection_0": {"meta": {"username": "zyc", "visage_version": "0.4.7", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "XpiDetection", "export_datetime": "2023-12-21 11:08:59", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": ""}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null, "is_dynamic": 0}, "expect_value": 0.7, "scope": 0.1, "max_loops": 5, "name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.9, 0.8, 0.7]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SingleShot_0": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "SingleShot", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q4,q10"}, "options_for_regular_exec": {"experiment_options": {"level_str": "01"}, "analysis_options": {"quality_bounds": [2, 0.8, 0.7, 0.03], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {}}, "DetuneCalibration_0": {"meta": {"username": "dpY4", "visage_version": "*******", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "DetuneCalibration", "export_datetime": "2023-12-30 12:54:48", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"detune_list": "Points(36) | qarange | (-35, 25, 1)", "rough_n_list": "Points(3) | qarange | (6, 8, 1)", "fine_n_list": "Points(2) | qarange | (11, 13, 2)", "theta_type": "Xpi", "fine_precision": 0.15, "f12_opt": false}, "analysis_options": {"child_ana_options": {"child_ana_options": {"quality_bounds": [0.95, 0.9, 0.8], "filter": {"window_length": 5, "polyorder": 3}, "fine": false, "prominence_divisor": 4.0}, "diff_threshold": 0.25}}}, "options_for_parallel_exec": {}}, "RabiScanAmp_1": {"meta": {"username": "zhaorenzeY4", "visage_version": "0.4.7", "monster_version": "0.5.6", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "test"}, "exp_class_name": "RabiScanAmp", "export_datetime": "2023-12-21 17:03:44", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q3,q29"}, "options_for_regular_exec": {"experiment_options": {"name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": -30}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.91]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "AmpComposite_1": {"meta": {"username": "dpY4", "visage_version": "*******", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "AmpComposite", "export_datetime": "2023-12-30 12:55:01", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"theta_type": "Xpi", "amp_init": null, "amp_list": "Points(0) | normal | None", "points": 61, "N": 7, "threshold_left": 0.95, "threshold_right": 1.05, "f12_opt": false}, "n_list": "Points(3) | normal | [10, 19, 30]", "theta_type": "Xpi", "f12_opt": false}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "diff_threshold": 0.05}}, "options_for_parallel_exec": {}}, "AmpComposite_0": {"meta": {"username": "dpY4", "visage_version": "*******", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "idle point"}, "exp_class_name": "AmpComposite", "export_datetime": "2023-12-30 12:55:01", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"theta_type": "Xpi/2", "amp_init": null, "amp_list": "Points(0) | normal | None", "points": 61, "N": 7, "threshold_left": 0.95, "threshold_right": 1.05, "f12_opt": false}, "n_list": "Points(3) | normal | [20, 30, 40]", "theta_type": "Xpi/2", "f12_opt": false}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "diff_threshold": 0.05}}, "options_for_parallel_exec": {}}}