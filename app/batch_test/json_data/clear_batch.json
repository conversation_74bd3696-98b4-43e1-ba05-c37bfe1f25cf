{"ReadoutFreqCalibrate": {"meta": {"username": "zyc", "visage_version": "0.4.9", "monster_version": "*******", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "batch_test"}, "exp_class_name": "ReadoutFreqCalibrate", "export_datetime": "2024-01-31 16:36:31", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"time_perform": false, "fc_list": "Points(0) | normal | None", "points": 61, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "extend_f12": false, "scope": 3, "z_amp": null, "mode": "IF"}, "fc_list": "Points(0) | normal | None", "readout_power": null, "readout_type": "01", "save_result": false}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.85]}, "save_mode": "max_distance_point", "diff_threshold": 0.1}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SingleShot_0": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "SingleShot", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"level_str": "01"}, "analysis_options": {"quality_bounds": [3, 0.7, 0.6, 0.03], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {}}, "FixedMeasureStartSingleShot": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "FixedMeasureStartSingleShot", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"level_str": "01", "measure_start": 100, "fake_pulse": false, "repeat": 10000}, "analysis_options": {"quality_bounds": [3, 0.7, 0.6, 0.03], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {}}, "PhotonScanReadoutFreqV2": {"meta": {"username": "tuple_01", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "PhotonScanReadoutFreqV2", "export_datetime": "2024-04-17 14:07:36", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1,q2"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {}, "sweep_list": null, "scope": {"l": 0.25, "r": 0.25, "s": 0.05}}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "kappa": null, "chi": null, "t2echo": null, "raw_data_format": "plot"}}}, "options_for_parallel_exec": {"experiment_options": {"sweep_list": {"q1": "Points(0) | normal | None", "q2": "Points(0) | normal | None"}}, "analysis_options": {"child_ana_options": {"quality_bounds": {"q1": [0.98, 0.93, 0.81], "q2": [0.98, 0.93, 0.81]}, "kappa": {"q1": null, "q2": null}, "chi": {"q1": null, "q2": null}, "t2echo": {"q1": null, "q2": null}}}}}, "PhotonRamsey": {"meta": {"username": "tuple_01", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "<PERSON>n<PERSON><PERSON><PERSON>", "export_datetime": "2024-04-17 14:11:05", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1,q2"}, "options_for_regular_exec": {"experiment_options": {"delays": "Points(121) | qarange | (20, 170, 2.5)", "nor_rd_trigger": 500, "exp_rd_pulse_params": null, "nor_rd_pulse_params": null, "exp_rd_pulse_type": "Square", "nor_rd_pulse_type": "Clear", "fringe": 25, "init_state": 0, "pre_width": 800, "pre_amp": 0.0, "ramsey_end": 600, "is_amend": false, "fake_pulse": false, "fidelity_correct_type": "ibu", "plot_iq": true}, "analysis_options": {"quality_bounds": [0.9, 0.8, 0.75], "raw_data_format": "plot"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "AmpToPhoton": {"meta": {"username": "tuple_01", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "AmpToPhoton", "export_datetime": "2024-04-17 14:09:55", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1,q2"}, "options_for_regular_exec": {"experiment_options": {"run_mode": "sync", "child_exp_options": {"delays": "Points(121) | qarange | (20, 170, 2.5)", "nor_rd_trigger": 500, "exp_rd_pulse_params": null, "nor_rd_pulse_params": null, "exp_rd_pulse_type": "Square", "nor_rd_pulse_type": "Square", "fringe": 25, "init_state": 0, "pre_width": 800, "pre_amp": 0.0, "ramsey_end": 600, "is_amend": false, "fake_pulse": false, "fidelity_correct_type": "ibu"}, "pre_amp_list": "Points(5) | qarange | (0.01, 0.04, 0.003)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "factor": 3.5, "raw_data_format": "plot"}, "chi_eff": null, "kappa": null}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {"child_ana_options": {}, "chi_eff": {}, "kappa": {}}}}, "PhotonNumMeas": {"meta": {"username": "tuple_01", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "PhotonNumMeas", "export_datetime": "2024-04-17 14:09:55", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1,q2"}, "options_for_regular_exec": {"experiment_options": {"exp_rd_pulse_params": null, "nor_rd_pulse_params": null, "exp_rd_pulse_type": "Square", "nor_rd_pulse_type": "Square", "init_state": 0, "xy_delay": 800, "extra_delay": 50, "nor_rd_trigger": 300, "fake_pulse": false, "plot_iq": true, "scope": {"l": 10, "r": 0, "s": 0.5}, "mode": "LO", "pre_amp": null, "pre_width": null}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "PhotonNumMeasVsTime": {"meta": {"username": "tuple_01", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "PhotonNumMeasVsTime", "export_datetime": "2024-04-17 14:11:05", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1,q2"}, "options_for_regular_exec": {"experiment_options": {"async_time_out": 10, "run_mode": "async", "child_exp_options": {"exp_rd_pulse_params": null, "nor_rd_pulse_params": null, "exp_rd_pulse_type": "Square", "nor_rd_pulse_type": "Square", "init_state": 0, "xy_delay": 800, "extra_delay": 50, "nor_rd_trigger": 300, "fake_pulse": false, "scope": {"l": 10, "r": 0, "s": 0.5}, "mode": "LO", "pre_amp": null, "pre_width": null}, "xy_delay_list": "Points(3) | qarange | (0, 1500, 50)"}, "analysis_options": {"chi_eff": null}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "PhotonNumMeasVsAmp": {"meta": {"username": "tuple_01", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "PhotonNumMeasVsAmp", "export_datetime": "2024-04-17 14:11:05", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1,q2"}, "options_for_regular_exec": {"experiment_options": {"async_time_out": 10, "run_mode": "async", "child_exp_options": {"exp_rd_pulse_params": null, "nor_rd_pulse_params": null, "exp_rd_pulse_type": "Square", "nor_rd_pulse_type": "Square", "init_state": 0, "xy_delay": 1300, "extra_delay": 50, "nor_rd_trigger": 300, "fake_pulse": false, "pre_amp": null, "pre_width": 1500, "scope": {"l": 120, "r": 20, "s": 2}, "mode": "LO"}, "amp_list": "Points(3) | qarange | (0.03, 0.12, 0.005)"}, "analysis_options": {"kappa": null, "chi_eff": null}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "SingleShot_1": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "SingleShot", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"level_str": "01"}, "analysis_options": {"quality_bounds": [3, 0.7, 0.6, 0.03], "method": "GMM", "n_multiple": 3.0, "set_proportion": false, "heat_stimulate": false}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "DePhaseRamsey": {"meta": {"username": "tuple_01", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "export_datetime": "2024-04-17 14:11:05", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1,q2"}, "options_for_regular_exec": {"experiment_options": {"delays": "Points(121) | qarange | (0.0, 300.0, 5)", "fringe": 25, "t_relax": 550, "t_buffer": 100, "init_state": 0, "is_amend": true, "fake_pulse": false, "fidelity_correct_type": "ibu", "acq_pulse_params": null, "acq_pulse_type": "Square", "plot_iq": true}, "analysis_options": {"quality_bounds": [0.9, 0.8, 0.75], "kappa": null, "chi": null, "t2echo": null, "raw_data_format": "plot"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "DePhaseRamseyComposite": {"meta": {"username": "tuple_01", "visage_version": "0.5.1", "monster_version": "*******", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "DePhaseRamseyComposite", "export_datetime": "2024-04-17 14:11:05", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1,q2"}, "options_for_regular_exec": {"experiment_options": {"async_time_out": 10, "run_mode": "async", "child_exp_options": {"delays": "Points(121) | qarange | (0.0, 300.0, 5)", "fringe": 25, "t_relax": 550, "t_buffer": 100, "init_state": 0, "is_amend": true, "fake_pulse": false, "fidelity_correct_type": "ibu", "acq_pulse_params": null, "acq_pulse_type": "Square"}, "t_relax_list": "Points(3) | qarange | (400, 800, 10)"}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "kappa": null, "chi": null, "t2echo": null, "raw_data_format": "plot"}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XMTiming": {"meta": {"username": "<PERSON><PERSON><PERSON><PERSON>", "visage_version": "0.10.1", "monster_version": "0.10.1", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "8bit_idle_point"}, "exp_class_name": "XMTiming", "export_datetime": "2024-08-13 11:30:45", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"delays": "Points(3) | qarange | (0, 1400, 10)", "one_m_offset": 300, "scope": {"l": 150, "r": 150, "s": 2}, "fake_pulse": false, "plot_iq": false}, "analysis_options": {"pure_exp_mode": false, "extract_mode": "fit_params"}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "NMClearParamsBoth": {"meta": {"username": "tuple_01", "visage_version": "0.5.1", "monster_version": "********", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "NMClearParamsBoth", "export_datetime": "2024-04-17 17:52:17", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1,q2"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"delays": "Points(121) | qarange | (0.0, 300.0, 5)", "fringe": 25, "t_relax": 50, "t_buffer": 100, "init_state": 0, "is_amend": true, "fake_pulse": false, "fidelity_correct_type": "ibu", "acq_pulse_params": null, "acq_pulse_type": "Clear"}, "mode": "NM", "input_data": {"amp1": {"is_opt": true, "init_v": null, "iter_step": 0.001, "bound": [-0.9, 0.0], "nonzdelt": 0.08}, "amp2": {"is_opt": true, "init_v": null, "iter_step": 0.001, "bound": [0.0, 0.8], "nonzdelt": 0.08}, "tkick": {"is_opt": false, "init_v": null, "iter_step": 10, "bound": [50, 150]}}, "nm_params": {"ftarget": 0, "maxiter": 20, "maxfev": 20, "xatol": 0.001, "fatol": 0.01, "adaptive": false}}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "kappa": null, "chi": null, "t2echo": null, "raw_data_format": "plot"}}}, "options_for_parallel_exec": {"experiment_options": {"child_exp_options": {"clear_params": {"q1": {}, "q2": {}}}}, "analysis_options": {"child_ana_options": {"quality_bounds": {"q1": [0.98, 0.93, 0.81], "q2": [0.98, 0.93, 0.81]}, "kappa": {"q1": null, "q2": null}, "chi": {"q1": null, "q2": null}, "t2echo": {"q1": null, "q2": null}}}}}, "NMPhaseParams": {"meta": {"username": "tuple_01", "visage_version": "0.5.1", "monster_version": "********", "chip": {"sample": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "env_name": "Y4-231011-设计验证-72bit_300pin_V9.2.3_Base-3#", "point_label": "sweetpoint"}, "exp_class_name": "NMPhaseParams", "export_datetime": "2024-04-17 17:52:17", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1,q2"}, "options_for_regular_exec": {"experiment_options": {"time_perform": false, "child_exp_options": {"delays": "Points(121) | qarange | (0.0, 300.0, 5)", "fringe": 25, "t_relax": 50, "t_buffer": 100, "init_state": 0, "is_amend": true, "fake_pulse": false, "fidelity_correct_type": "ibu", "acq_pulse_params": null, "acq_pulse_type": "Clear"}, "mode": "NM", "input_data": {"amp": {"is_opt": true, "init_v": null, "iter_step": 0.001, "bound": [0.0, 0.6], "nonzdelt": 0.08}, "phase": {"is_opt": true, "init_v": null, "iter_step": 0.001, "bound": [0.0, 6.283185307179586], "nonzdelt": 0.1}, "tkick": {"is_opt": false, "init_v": null, "iter_step": 10, "bound": [50, 150]}}, "nm_params": {"ftarget": 0, "maxiter": 20, "maxfev": 20, "xatol": 0.001, "fatol": 0.01, "adaptive": false}}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.93, 0.81], "kappa": null, "chi": null, "t2echo": null, "raw_data_format": "plot"}}}, "options_for_parallel_exec": {"experiment_options": {"child_exp_options": {"phase_params": {"q1": {}, "q2": {}}}}, "analysis_options": {"child_ana_options": {"quality_bounds": {"q1": [0.98, 0.93, 0.81], "q2": [0.98, 0.93, 0.81]}, "kappa": {"q1": null, "q2": null}, "chi": {"q1": null, "q2": null}, "t2echo": {"q1": null, "q2": null}}}}}, "CavityFreqSpectrum_high_power": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "CavityFreqSpectrum", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"points": 61, "readout_power": -10, "pi_amp": 0.9, "add_pi_pulse": false, "scope": 3, "z_amp": null, "mode": "IF", "save_result": false}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.85]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "CavityFreqSpectrum_0": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "CavityFreqSpectrum", "export_datetime": "2023-11-29 19:57:28", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"points": 61, "readout_power": null, "pi_amp": null, "add_pi_pulse": false, "scope": 3, "z_amp": null, "mode": "IF", "save_result": false}, "analysis_options": {"quality_bounds": [0.98, 0.95, 0.85]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XpiDetection": {"meta": {"username": "zyc", "visage_version": "*******", "monster_version": "0.5.4", "chip": {"sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2", "point_label": "P1"}, "exp_class_name": "XpiDetection", "export_datetime": "2023-12-01 16:47:35", "description": null}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"child_exp_options": {"name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null}, "expect_value": 0.7, "scope": 0.1, "max_loops": 5, "name": "Xpi", "amps": "Points(51) | qarange | (0.0, 1.0, 0.02)", "drive_power": null}, "analysis_options": {"child_ana_options": {"quality_bounds": [0.98, 0.95, 0.91]}}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}}