# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/04/02
# __author:       <PERSON><PERSON><PERSON>


if __name__ == "__main__":
    from app.config import init_backend
    from pyQCat.experiments.batch import BatchClearCalibration

    _backend = init_backend()
    batch = BatchClearCalibration(_backend)
    batch.set_experiment_options(
        param_path=r"app/batch_test/json_data/clear_options.json",
        exp_retry=0,
        use_simulator=False,
        record_batch=True,
        refresh_context=False,
        physical_units=["q75"],
        use_config_unit=False,
        simulator_pass_rate=1,
        save_db=False,
        wr_flow=["CavityFreqSpectrum_high_power"],
        clear_flow=[
            "T2SpinEcho",
            "CavityFreqSpectrum_0",
            "ReadoutFreqCalibrate",
            "XpiDetection",
            "PhotonScanReadoutFreqV2",
            "SingleShot_0",
            "PhotonNumMeasVsAmp",
            "NMClearParamsBoth",
        ],
    )
    batch.run()
