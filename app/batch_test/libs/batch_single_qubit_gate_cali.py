# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/20
# __author:       <PERSON><PERSON><PERSON>


if __name__ == "__main__":
    from app.config import init_backend
    from pyQCat.experiments.batch import BatchSingleQubitCalibration

    physical_units = [f"q{i}" for i in range(1, 73)]

    backend = init_backend()

    batch = BatchSingleQubitCalibration(backend)
    batch.set_experiment_options(
        param_path=r"../json_data/sq_cali.json",
        physical_units=physical_units,
        use_simulator=True,
        simulator_pass_rate=0.8,
        save_db=False,
        generate_ppt=True,
    )
    batch.run()
