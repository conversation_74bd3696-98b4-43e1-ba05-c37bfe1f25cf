# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/12/20
# __author:       <PERSON><PERSON><PERSON>


if __name__ == "__main__":
    from app.config import init_backend, JSON_PATH
    from pyQCat.experiments.batch import BatchRunner

    import sys

    print("接收到的参数：", sys.argv)
    backend = init_backend()
    batch = BatchRunner(backend)
    batch.set_experiment_options(
        param_path=str(JSON_PATH / "simulator.json"),
        exp_retry=0,
        record_batch=True,
        quality_filter=False,
        use_config_unit=True,
        unified_dir=False,
        refresh_context=False,
        flows=["SingleShot"],
        use_simulator=True,
        # physical_units=["q1", "q2", "q3"]
    )
    batch.run()
