# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/11
# __author:       <PERSON><PERSON><PERSON>

import asyncio

from loguru import logger

from app.config import SYSTEM_PARA, config_manager
from app.server.conf import ROLE
from app.server.log import Logger
from app.server.manager import MonsterServer
from pyQCat.config import PyqcatConfig


def _set_up_logger(process_name):
    """Set up logger."""

    config = PyqcatConfig(filename=SYSTEM_PARA.backend.config_file, init_log=False)

    transfer_logger = Logger(
        process_name=process_name, log_path=config.log_path, process_safe=True
    )

    transfer_logger.set_log_level()
    transfer_logger.save_log()

    logger.warning(
        f"\n\n\n\n MS-{process_name} logger successfully. "
        f"Monster server log will save at: {transfer_logger.log_path}",
    )


def main():
    config_manager()
    _set_up_logger("MS")
    server = MonsterServer(ROLE)
    asyncio.run(server.run())


if __name__ == "__main__":
    main()
