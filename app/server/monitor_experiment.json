{"SingleShot": {"meta": {"exp_class_name": "SingleShot"}, "context_options": {"name": "qubit_calibration", "readout_type": "", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {}, "analysis_options": {"quality_bounds": [2, 0.85, 0.7, 0.011]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "Ramsey": {"meta": {"exp_class_name": "<PERSON>"}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"delays": "Points(49) | qarange | (20, 140, 2.5)", "fringe": 25}, "analysis_options": {"quality_bounds": [0.98, 0.93, 0.81]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "T1": {"meta": {"exp_class_name": "T1"}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q1"}, "options_for_regular_exec": {"experiment_options": {"delays": "Points(100) | qarange | (200, 29900, 300)", "is_amend": true}, "analysis_options": {"quality_bounds": [0.9, 0.85, 0.77]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "PopulationLossOnce": {"meta": {"exp_class_name": "PopulationLossOnce"}, "context_options": {"name": "qubit_calibration", "readout_type": "01", "physical_unit": "q61"}, "options_for_regular_exec": {"experiment_options": {"delay": 1000, "z_amp_list": "Points(21) | qarange | (-0.1, 0.1, 0.01)", "freq_range_mode": "normal", "point": 20}, "analysis_options": {}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}, "XEBMultiple": {"meta": {"exp_class_name": "XEBMultiple"}, "context_options": {"name": "cz_gate_calibration", "readout_type": "union-01-01", "physical_unit": "q11q17"}, "options_for_regular_exec": {"experiment_options": {"depth1": "Points(5) | qarange | (2, 10, 2)", "depth2": "Points(10) | qarange | (15, 60, 5)", "depth3": "Points(0) | normal | None", "depth4": "Points(0) | normal | None", "times": 30, "goal_gate": "CZ", "is_amend": true, "fidelity_correct_type": "ibu"}, "analysis_options": {"fidelity_threshold": 0.93, "quality_bounds": [0.95, 0.9, 0.85]}}, "options_for_parallel_exec": {"experiment_options": {}, "analysis_options": {}}}}