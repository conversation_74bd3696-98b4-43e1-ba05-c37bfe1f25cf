# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/01/03
# __author:       <PERSON><PERSON><PERSON>

from collections import OrderedDict, defaultdict
from datetime import datetime
from pathlib import Path
from enum import Enum
from functools import cmp_to_key
from typing import List, Optional

import matplotlib.dates as mdates
import matplotlib.pyplot as plt
import numpy as np
from bson import ObjectId
from loguru import logger
from pymongo import MongoClient

from app.data_extraction.exp_record_analysis.ppt_tool import create_ppt_from_dict
from app.tool.utils import read_json_file, write_json_file
from pyQCat.processor.utilities import sort_bit
from pyQCat.tools.s3storage import S3Storage


class CollectionEnum(str, Enum):
    ExpRecord = "ExpRecord"
    BatchRecord = "BatchRecord"
    BatchFlowRecord = "BatchFlowRecord"
    BatchExpRecord = "BatchExpRecord"
    ExpExecute = "ExpExecute"


class RecordTool:
    def __init__(self, host: str = "***********", port: int = 27017):
        client = MongoClient(f"mongodb://bylz:fjsaoJOIjiojj28hjj@{host}:{port}")
        db = client["UserData"]
        # db = client["UserData"]
        self.exp_record = db[CollectionEnum.ExpRecord]
        self.batch_record = db[CollectionEnum.BatchRecord]
        self.batch_flow_record = db[CollectionEnum.BatchFlowRecord]
        self.batch_exp_record = db[CollectionEnum.BatchExpRecord]
        self.exp_execute = db[CollectionEnum.ExpExecute]
        self.user_env_execute = db["UserEnv"]
        self.db = db
        self.client = client
        self.s3 = S3Storage()

    def query_bucket_size(self, bucket_name: str = "data"):
        total_size = 0
        try:
            objects = self.s3.list_objects(bucket_name, recursive=True)
            for obj in objects:
                total_size += obj.size
            size_in_mb = round(total_size / (1024 * 1024), 2)
            size_in_gb = round(total_size / (1024 * 1024 * 1024), 3)
            logger.info(
                f"Bucket `{bucket_name}` Size is: {size_in_mb} MB, {size_in_gb} GB"
            )
        except Exception as e:
            logger.error(f"Error occurred: {e}")
            return 0

    def close(self):
        self.client.close()
        logger.info("关闭 Mongo 链接")

    def query_exp_record_collection_size(self):
        self.query_collection_size(CollectionEnum.ExpRecord)

    def query_batch_record_collection_size(self):
        self.query_collection_size(CollectionEnum.BatchRecord)

    def query_batch_flow_record_collection_size(self):
        self.query_collection_size(CollectionEnum.BatchFlowRecord)

    def query_batch_exp_record_collection_size(self):
        self.query_collection_size(CollectionEnum.BatchExpRecord)

    def query_all_collection_size(self):
        data_size, storage_size, index_size = 0, 0, 0
        for name, member in CollectionEnum.__members__.items():
            a, b, c = self.query_collection_size(member)
            data_size += a
            storage_size += b
            index_size += c
        logger.info(
            f"\nAll Collection:\nData Size: {data_size} MB\nStorage Size: {storage_size} MB\nIndex Size: {index_size} MB"
        )

    def query_collection_size(self, collection: CollectionEnum):
        collection_stats = self.db.command("collStats", collection)

        data_size = round(
            collection_stats["size"] / (1024 * 1024), 2
        )  # MB, 表示集合中所有文档的实际数据大小（即未压缩的数据大小)
        storage_size = round(
            collection_stats["storageSize"] / (1024 * 1024), 2
        )  # MB, 表示集合在磁盘上占用的存储空间大小 (压缩后)
        index_size = round(
            collection_stats["totalIndexSize"] / (1024 * 1024), 2
        )  # MB, 表示集合中所有索引占用的磁盘空间大小

        logger.info(
            f"\nCollection: {collection.value}\nData Size: {data_size} MB\nStorage Size: {storage_size} MB\nIndex Size: {index_size} MB"
        )
        return data_size, storage_size, index_size

    def query_exp_record_collection_count(self):
        total_count = self.exp_record.count_documents({})
        top_count = self.exp_record.count_documents({"exp_type": "TOP"})
        parallel_count = self.exp_record.count_documents({"exp_type": "PARALLEL"})
        comp_count = self.exp_record.count_documents({"exp_type": "COMP"})
        batch_count = self.batch_record.count_documents({})

        logger.info(
            f"\n实验总数: {total_count}\n基础实验总数: {top_count}\n复合实验总数: {comp_count}\n并行实验总数: {parallel_count}\nBatch总数: {batch_count}"
        )

    def query_his_exp_execute(self):
        from collections import defaultdict

        documents = self.exp_execute.find(
            {
                "env_name": "Y3",
                "sample": "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）",
                "is_simulate": False,
            }
        )
        data_map = {}
        index = 0
        for doc in documents:
            date = str(doc["date"])

            if doc.get("is_parallel") is False:
                n = 1
            else:
                n = len(doc.get("extra", {}).get("options", {}))
            print(f"{doc['username']} | {doc['exp_name']} | n={n}")
            if date not in data_map:
                data_map[date] = {"count": 0, "exp_type": defaultdict(int)}
            data_map[date]["count"] += n
            data_map[date]["exp_type"][doc["exp_type"]] += n
            if index == 10000:
                print(data_map)
                index = 0
            else:
                index += 1
        write_json_file("exp_execute_v3.json", data_map)

    def plot_history(self):
        data = read_json_file("exp_execute.json")
        # 提取数据
        dates = []
        counts = []
        singles = []
        composites = []
        rates = []

        for date_str, values in data.items():
            if (
                "single" in values["exp_type"] and "composite" in values["exp_type"]
                # and values["count"] < 50000
            ):
                dates.append(
                    datetime.strptime(date_str.split(" ")[0], "%Y-%m-%d")
                )  # 转换为 datetime 对象
                counts.append(values["count"])
                singles.append(values["exp_type"]["single"])
                composites.append(values["exp_type"]["composite"])
                rates.append(
                    values["exp_type"]["single"] / values["exp_type"]["composite"]
                )

        # 创建图表
        plt.figure(figsize=(12, 10))
        fig, axes = plt.subplots(2, 1, sharex=True)

        # 绘制 count
        axes[0].plot(dates, counts, label="Count", color="blue")

        # 绘制 single
        axes[0].plot(dates, singles, label="Single", color="green")

        # 绘制 composite
        axes[0].plot(
            dates,
            composites,
            label="Composite",
            color="red",
        )
        axes[0].set_xlabel("Date", fontsize=12)
        axes[0].set_ylabel("Value", fontsize=12)

        # 设置标题和标签
        axes[0].set_title(
            f"Count, Single, and Composite Over Time, Mean ({int(np.mean(counts))})",
            fontsize=10,
        )
        axes[0].legend()
        axes[0].grid(True, linestyle="--", alpha=0.6)
        axes[1].plot(
            dates,
            rates,
            label="Single/Composite",
            color="red",
        )
        axes[1].legend()
        axes[1].grid(True, linestyle="--", alpha=0.6)

        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d"))
        plt.gca().xaxis.set_major_locator(mdates.AutoDateLocator())  # 按天显示刻度
        plt.gcf().autofmt_xdate()  # 自动旋转日期标签
        plt.tight_layout()
        plt.savefig("history.png")

    def plot_y3_history(self):
        data = read_json_file("exp_execute_v3.json")
        dates = []
        counts = []
        for date_str, values in data.items():
            dates.append(datetime.strptime(date_str.split(" ")[0], "%Y-%m-%d"))
            counts.append(values["count"])

        max_count, min_count = np.max(counts), np.min(counts)
        max_date = dates[counts.index(max_count)]
        min_date = dates[counts.index(min_count)]

        plt.figure(figsize=(12, 6))

        plt.plot(dates, counts, label="Count", marker="o", linestyle="-", color="blue")

        plt.title(
            f"Y3 Count, Experiment Over Time, Mean ({int(np.mean(counts))})",
            fontsize=16,
        )
        plt.xlabel("Date", fontsize=12)
        plt.ylabel("Value", fontsize=12)
        plt.annotate(
            f"Max: {max_count}\n({max_date})",
            xy=(max_date, max_count),
            xytext=(10, -20),
            textcoords="offset points",
            color="blue",
            arrowprops=dict(arrowstyle="->", color="blue"),
        )
        plt.annotate(
            f"Min: {min_count}\n({min_date})",
            xy=(min_date, min_count),
            xytext=(10, -20),
            textcoords="offset points",
            color="blue",
            arrowprops=dict(arrowstyle="->", color="blue"),
        )

        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter("%Y-%m-%d"))
        plt.gca().xaxis.set_major_locator(mdates.AutoDateLocator())
        plt.gcf().autofmt_xdate()

        plt.legend()

        plt.grid(True, linestyle="--", alpha=0.6)

        plt.tight_layout()
        plt.savefig("Y3_history.png")

    def query_batch_record(self, batch_id: str):
        result = OrderedDict()

        index = 0
        batch_record = self.batch_record.find_one({"_id": ObjectId(batch_id)})
        for flow_id in batch_record["execute_meta"]["flows"]:
            flow_record = self.batch_flow_record.find_one({"_id": ObjectId(flow_id)})
            for exp_id in flow_record["experiments"]:
                batch_exp_record = self.batch_exp_record.find_one(
                    {"_id": ObjectId(exp_id)}
                )
                exp_record = self.exp_record.find_one({"_id": ObjectId(exp_id)})
                analysis_data = {}
                if exp_record:
                    if exp_record["exp_type"] == "PARALLEL":
                        for cid in exp_record["execute_meta"]["child_record_ids"]:
                            child_exp_record = self.exp_record.find_one(
                                {"_id": ObjectId(cid)}
                            )
                            if (
                                "FixedPointCalibration" in batch_exp_record["name"]
                                or "FixedSwapFreqCaliCoupler"
                                in batch_exp_record["name"]
                            ):
                                physical_units = child_exp_record["context_meta"][
                                    "unit_map"
                                ]["qubit_pair"]
                            else:
                                physical_units = child_exp_record["execute_meta"][
                                    "physical_units"
                                ]
                            analysis_data[physical_units] = dict(
                                result=child_exp_record["execute_meta"][
                                    "analysis_result"
                                ],
                                quality=child_exp_record["execute_meta"]["quality"],
                                exp_id=cid,
                            )
                    else:
                        if (
                            "FixedPointCalibration" in batch_exp_record["name"]
                            or "FixedSwapFreqCaliCoupler" in batch_exp_record["name"]
                        ):
                            physical_units = exp_record["context_meta"]["unit_map"][
                                "qubit_pair"
                            ]
                        else:
                            physical_units = exp_record["execute_meta"][
                                "physical_units"
                            ]
                        analysis_data[physical_units] = dict(
                            result=exp_record["execute_meta"]["analysis_result"],
                            quality=exp_record["execute_meta"]["quality"],
                            exp_id=exp_id,
                        )
                result[f"{index} {batch_exp_record['name']}"] = dict(
                    flow_name=flow_record["name"],
                    pass_units=batch_exp_record["pass_units"],
                    bad_units=batch_exp_record["fail_units"],
                    update_data=batch_exp_record["update_data"],
                    analysis_data=analysis_data,
                )
                index += 1

        write_json_file("zz.json", result)

    def extract_info_from_batch(
        self,
        batch_id: str,
        exp_list: Optional[List[str]] = None,
        physical_units: Optional[List[str]] = None,
        check_pass: bool = False,
        N: int = 4,
    ):
        batch_record = self.batch_record.find_one({"_id": ObjectId(batch_id)})
        picture_map = {}
        picture_list = []

        def _extract_picture(cur_exp_record, exp_name):
            cur_unit = cur_exp_record["execute_meta"]["physical_units"]

            def _check_unit():
                if physical_units:
                    for _unit in physical_units:
                        if _unit in cur_unit:
                            return True
                    return False
                return True

            if _check_unit():
                if cur_exp_record["exp_class"] == "ZZTimingComposite":
                    pngs = []
                    if cur_exp_record["execute_meta"]["quality"]["descriptor"] in [
                        "perfect",
                        "normal",
                    ]:
                        for z_once_id in cur_exp_record["execute_meta"][
                            "child_record_ids"
                        ][-3:]:
                            z_once_record = self.exp_record.find_one(
                                {"_id": ObjectId(z_once_id)}
                            )
                            pngs.append(
                                z_once_record["execute_meta"]["origin_png_results"][-1]
                            )
                        picture_map[cur_unit + exp_name] = dict(
                            unit=cur_unit,
                            exp=exp_name,
                            png=pngs,
                        )
                    return

                elif cur_exp_record["exp_class"] == "XYZTimingComposite":
                    pngs = []
                    # if cur_exp_record["execute_meta"]["quality"]["descriptor"] in ["perfect", "normal"]:
                    for z_once_id in cur_exp_record["execute_meta"]["child_record_ids"][
                        -2:
                    ]:
                        z_once_record = self.exp_record.find_one(
                            {"_id": ObjectId(z_once_id)}
                        )
                        pngs.append(
                            z_once_record["execute_meta"]["origin_png_results"][-1]
                        )
                    picture_map[cur_unit + exp_name] = dict(
                        unit=cur_unit,
                        exp=exp_name,
                        png=pngs,
                    )

                elif cur_exp_record["execute_meta"]["origin_png_results"]:
                    picture_map[cur_unit + exp_name] = dict(
                        unit=cur_unit,
                        exp=exp_name,
                        png=cur_exp_record["execute_meta"]["origin_png_results"],
                    )

        def _check_flows(cur_flow_record):
            for _exp in cur_flow_record["exp_flows"]:
                if _exp in exp_list:
                    return True
            return False

        def _check_exp(curt_batch_exp):
            if curt_batch_exp["name"] in exp_list:
                if physical_units:
                    for unit in curt_batch_exp["physical_units"]:
                        if unit in physical_units:
                            return True
                else:
                    return True
            return False

        if batch_record:
            _ = batch_record.pop("context_meta")
            execute_meta = batch_record.pop("execute_meta")
            batch_record["_id"] = batch_id
            batch_record["start_time"] = execute_meta["start_time"]
            batch_record["end_time"] = execute_meta["end_time"]
            batch_record["state"] = execute_meta["state"]
            batch_record["physical_units"] = execute_meta["result"]["physical_units"]
            batch_record["pass_units"] = execute_meta["result"]["pass_units"]
            batch_record["fail_units"] = execute_meta["result"]["fail_units"]

            if check_pass and not physical_units:
                physical_units = batch_record["pass_units"]

            if exp_list:
                for flow_id in execute_meta["flows"]:
                    flow_record = self.batch_flow_record.find_one(
                        {"_id": ObjectId(flow_id)}
                    )
                    if _check_flows(flow_record):
                        for exp_id in flow_record["experiments"]:
                            batch_exp_record = self.batch_exp_record.find_one(
                                {"_id": ObjectId(exp_id)}
                            )
                            if _check_exp(batch_exp_record):
                                exp_record = self.exp_record.find_one(
                                    {"_id": ObjectId(exp_id)}
                                )
                                if not exp_record:
                                    continue
                                if exp_record["exp_type"] == "PARALLEL":
                                    for child_exp_id in exp_record["execute_meta"][
                                        "child_record_ids"
                                    ]:
                                        child_exp_record = self.exp_record.find_one(
                                            {"_id": ObjectId(child_exp_id)}
                                        )
                                        _extract_picture(
                                            child_exp_record, batch_exp_record["name"]
                                        )
                                else:
                                    _extract_picture(
                                        exp_record, batch_exp_record["name"]
                                    )

            for _, v in picture_map.items():
                for png in v["png"]:
                    picture_list.append(dict(unit=v["unit"], exp=v["exp"], png=png))
            picture_list.sort(key=lambda x: x["unit"])

            create_ppt_from_dict(
                batch_record,
                picture_list,
                ppt_title=batch_record["exp_label"],
                output_filename=rf"Z:\Y3\Monitor\Data\data\Batch节点\{batch_record['exp_label']}-{batch_id}.pptx",
                N=N,
            )
        else:
            logger.warning("No find batch data!")

    def extract_info_from_batch_v2(self, batch_id: str, save_path: str):
        exp_data = defaultdict(list)
        state_map = {1: "初始化", 2: "运行中", 3: "完成", 4: "异常结束"}
        batch_record = self.batch_record.find_one({"_id": ObjectId(batch_id)})
        env = self.user_env_execute.find_one({"_id": ObjectId(batch_record["env_id"])})
        pass_rate = 0
        pass_count = len(batch_record["execute_meta"]["result"]["pass_units"] or [])
        fail_count = len(batch_record["execute_meta"]["result"]["fail_units"] or [])
        if pass_count or fail_count:
            pass_rate = round(pass_count / (pass_count + fail_count) * 100, 2)

        data = {
            "metadata": {
                "exp_label": batch_record["exp_label"],
                "exp_class": batch_record["exp_class"],
                "id": batch_id,
                "version": batch_record["version"],
                "device": batch_record["device"],
                "username": batch_record["username"],
                "sample": env["sample"],
                "env_name": batch_record["env_name"],
                "start_time": batch_record["execute_meta"]["start_time"],
                "end_time": batch_record["execute_meta"]["end_time"],
                "save_path": batch_record["execute_meta"]["save_path"],
                "state": state_map.get(
                    batch_record["execute_meta"]["state"], "未知状态"
                ),
                "pass_count": len(
                    batch_record["execute_meta"]["result"]["pass_units"] or []
                ),
                "fail_count": len(
                    batch_record["execute_meta"]["result"]["fail_units"] or []
                ),
                "pass_rate": f"{pass_rate}%",
            },
            "exp_data": exp_data,
        }

        for flow_id in batch_record["execute_meta"]["flows"]:
            flow_record = self.batch_flow_record.find_one({"_id": ObjectId(flow_id)})
            for exp_id in flow_record["experiments"]:
                batch_exp_record = self.batch_exp_record.find_one(
                    {"_id": ObjectId(exp_id)}
                )
                exp_record = self.exp_record.find_one({"_id": ObjectId(exp_id)})
                if exp_record["exp_type"] == "PARALLEL":
                    for child_exp_id in exp_record["execute_meta"]["child_record_ids"]:
                        child_exp_record = self.exp_record.find_one(
                            {"_id": ObjectId(child_exp_id)}
                        )
                        unit = child_exp_record["execute_meta"]["physical_units"]
                        exp_data[unit].append(
                            dict(
                                unit=unit,
                                exp_name=batch_exp_record["name"],
                                flow_name=flow_record["name"],
                                flow_id=flow_id,
                                png=child_exp_record["execute_meta"][
                                    "origin_png_results"
                                ],
                            )
                        )
                else:
                    unit = exp_record["execute_meta"]["physical_units"]
                    exp_data[unit].append(
                        dict(
                            unit=unit,
                            exp_name=batch_exp_record["name"],
                            flow_name=flow_record["name"],
                            flow_id=flow_id,
                            png=exp_record["execute_meta"]["origin_png_results"],
                        )
                    )

        batch_class_name = batch_record["exp_class"]
        flag = True
        batch_pic_count = 0
        batch_shape = (1, 1)
        filter_repeat = False

        if batch_class_name == "BatchBusS21":
            batch_pic_count = 1
            batch_shape = (1, 1)
            shape = (2, 3)
            split_by_unit = False
            exps = ["BusS21Collector"]
        elif batch_class_name == "BatchCavityQ":
            shape = (2, 4)
            split_by_unit = True
            exps = ["FindBusCavityFreq_rough", "FindBusCavityFreq_segma"]
        elif batch_class_name == "BatchIMPAControl":
            shape = (2, 3)
            split_by_unit = False
            exps = ["ImpaGain"]
        elif batch_class_name == "BatchCavityPowerScan":
            shape = (2, 3)
            split_by_unit = False
            exps = ["CavityPowerScan"]
        elif batch_class_name == "BatchCouplerTunable":
            shape = (3, 4)
            split_by_unit = False
            exps = ["CavityTunable_for_coupler"]
        elif batch_class_name == "BatchQubitTunable":
            shape = (2, 3)
            split_by_unit = False
            exps = ["CavityTunable_for_qubit"]
        elif batch_class_name == "BatchQubitSpectrumZAmp":
            shape = (2, 3)
            split_by_unit = True
            exps = [
                "CavityFreqSpectrum_BSZ",
                "QubitSpectrumScanPower",
                "QubitSpectrumVMax",
                "QubitSpectrumVMin",
                "QubitSpectrumZAmp",
            ]
        elif batch_class_name == "BatchQubitSpectrumZAmp":
            shape = (2, 3)
            split_by_unit = True
            exps = [
                "CavityFreqSpectrum_BSZ",
                "QubitSpectrumScanPower",
                "QubitSpectrumVMax",
                "QubitSpectrumVMin",
                "QubitSpectrumZAmp",
            ]
        elif batch_class_name == "BatchACT1Spectrum":
            shape = (2, 3)
            split_by_unit = False
            exps = ["ACSpectrum"]
        elif batch_class_name == "BatchRBSpectrum":
            shape = (3, 3)
            split_by_unit = True
            exps = ["RBSingle"]
        elif batch_class_name == "BatchSearchIdlePoint":
            shape = (2, 3)
            split_by_unit = True
            exps = [
                "QubitSpectrum_BSI",
                "RabiScanWidth",
                "QubitFreqCalibration",
                "XpiDetection",
            ]
            filter_repeat = True
        elif batch_class_name == "BatchSearchF12":
            shape = (2, 2)
            split_by_unit = True
            exps = ["QubitSpectrumF12", "RabiScanWidthF12", "F12Calibration"]
        else:
            flag = False

        page_data = []

        if flag is True:
            one_page_total = shape[0] * shape[1]
            ppt_page = 0
            index = 0
            page_data = []
            for unit, records in data["exp_data"].items():
                for exp_data in records:
                    if exp_data["exp_name"] in exps:
                        for p in exp_data["png"]:
                            page_data.append(
                                dict(
                                    unit=unit,
                                    exp_name=exp_data["exp_name"],
                                    flow_name=exp_data["flow_name"],
                                    flow_id=exp_data["flow_id"],
                                    png=p,
                                    ppt_page=ppt_page,
                                    index=index,
                                    shape=shape,
                                )
                            )
                            index += 1
                            if index >= one_page_total:
                                ppt_page += 1
                                index = 0

                if split_by_unit is True and index:
                    index = 0
                    ppt_page += 1

        data["ppt_data"] = page_data

        from pyQCat.experiments.batch_experiment import (
            generate_ppt_from_batch_report_data,
        )

        generate_ppt_from_batch_report_data(
            report_data=data,
            batch_pic_count=batch_pic_count,
            batch_shape=batch_shape,
            shape=shape,
            split_by_unit=split_by_unit,
            filter_repeat=filter_repeat,
            exps=exps,
            save_path=save_path,
        )


if __name__ == "__main__":
    tools = RecordTool()
    # tools.query_all_collection_size()
    # tools.query_bucket_size()
    # tools.query_exp_record_collection_count()

    # tools.query_batch_record("6850e869a314699639a7f081")
    # tools.extract_info_from_batch(
    #     batch_id="6854dec5adf195a804d87fed", exp_list=["ZZTimingComposite"], N=6
    # )

    save_path = "app/data_extraction/exp_record_analysis/data"
    std_batch_ids = [
        # "686b16cf9113379ff6f51cac",
        # "686b17b762c9e0882665fb9f",
        # "6869e1ab8717fde70315d9ad",
        # "686b1a2362c9e0882665fc03",
        # "686b64d9ae43c167a1449a74",
        # "686b94c2ae43c167a144cc77",
        # "686ba0e2ae43c167a144e6c3",
        # "686ccaa98d044195c699391c",
        # "686cd00d8d044195c6993f93",
        # "686e4a4af71a5210bbaf9fab",
        # "686c7f1a8b233520602d8e90",
        "68624b3512e70c050107516c"
    ]

    for bid in std_batch_ids:
        tools.extract_info_from_batch_v2(bid, save_path)

    tools.close()
    # tools.query_his_exp_execute()
    # tools.plot_history()
