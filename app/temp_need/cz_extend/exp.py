# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/02/14
# __author:       <PERSON><PERSON><PERSON>

import json
import math
from copy import deepcopy

import numpy as np
from loguru import logger
from scipy.signal import savgol_filter

from pyQCat.analysis.algorithms import change_phase, get_peak_point, phase_tomograph
from pyQCat.analysis.curve_fit_analysis import CurveFitAnalysis
from pyQCat.analysis.library.conditional_phase_analysis import phase_format
from pyQCat.analysis.quality import BaseQuality
from pyQCat.analysis.standard_curve_analysis import StandardCurveAnalysis
from pyQCat.errors import ExperimentFieldError, ExperimentOptionsError
from pyQCat.experiments.composite_experiment import CompositeExperiment
from pyQCat.experiments.single.two_qubit_gate.swap_once import (
    validate_data_key,
    validate_qubit_pair_cz_std,
    validate_two_qubit_exp_read_options,
)
from pyQCat.experiments.top_experiment_v1 import TopExperimentV1 as TopExperiment
from pyQCat.gate.notable_gate import Rphi_gate
from pyQCat.parameters import analysis_options_wrapper, options_wrapper
from pyQCat.pulse.pulse_function import (
    half_pi_pulse,
    pi_pulse,
    stimulate_state_pulse,
    zero_pulse,
)
from pyQCat.pulse.pulse_lib import Constant
from pyQCat.pulse_adjust import params_to_pulse
from pyQCat.structures import MetaData, Options, QDict, ExperimentData
from pyQCat.tools.utilities import cz_flow_options_adapter
from pyQCat.types import ExperimentRunMode, QualityDescribe, StandardContext
from pyQCat.tools.serialization import from_pick_binary_data


@analysis_options_wrapper(x_label="Freq [MHz]")
class LeakageAnalysis(StandardCurveAnalysis):
    pass


class LeakageNumAnalysisV2(StandardCurveAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()

        options.x_label = "CZ Num"
        options.y_label = ["QC Amp (v)"]
        options.filter = {"window_length": 5, "polyorder": 3}
        options.result_parameters = ["min_diff", "mean"]
        options.diff_bound = 0.005
        options.use_key = "P10"
        options.distance = 2

        return options

    def _create_analysis_data(self):
        analysis_data = {}
        index = 0
        use_key = self.options.use_key
        for child_exp in self.experiment_data._child_data.values():
            key = str(self.experiment_data.x_data[index])
            index += 1
            analysis_data[key] = dict(x=child_exp.x_data, y=child_exp.y_data[use_key])
            for ck in list(child_exp.y_data.keys()):
                if ck != use_key:
                    child_exp.y_data.pop(ck, None)
        return QDict(**analysis_data)

    def _data_processing(self):
        distance = self.options.distance
        ns = []
        peaks = []
        final_ns = []
        origin_peaks = []
        for k, data in self._analysis_data_dict.items():
            n = int(k)
            final_ns.append(n)
            x = data.x
            y = 1 - savgol_filter(data.y, **self.options.filter)
            height = (np.max(y) - np.min(y)) / 2
            prominence = (np.max(y) - np.min(y)) / 4.0
            points = [
                p for p in get_peak_point(x, y, distance, height, prominence=prominence)
            ]
            cur_peaks = [p.x for p in points]
            peaks.append(cur_peaks)
            origin_peaks.extend(cur_peaks)
            ns.extend([int(n) for _ in range(len(points))])

        # try:
        #     final_peaks, offset = LeakageNumAnalysisV3.find_closest_elements(peaks)
        #     self.results.min_diff.value = round(offset, 6)
        #     self.results.mean.value = round(np.mean(final_peaks), 6)
        #     self._analysis_data_dict["nearest_peaks"] = final_peaks
        #     if self.results.min_diff.value > self.options.diff_bound:
        #         self._quality = BaseQuality.instantiate(QualityDescribe.bad)
        #     else:
        #         self._quality = BaseQuality.instantiate(QualityDescribe.normal)
        # except Exception as e:
        #     logger.error(f"find peak error {e}")
        #     self._quality = BaseQuality.instantiate(QualityDescribe.bad)

    def _visualization(self):
        super()._visualization()
        use_key = self.options.use_key
        x_data = self.experiment_data.x_data
        y_data = self._analysis_data_dict.get("nearest_peaks")
        if y_data is not None:
            self.drawer.draw_raw_data(
                x_data=x_data,
                y_data=y_data,
                ax_index=0,
                label=f"{use_key} nearest peaks",
                c="blue",
                linewidth=3,
            )
            self.drawer.draw_raw_data(
                x_data=x_data,
                y_data=np.ones_like(x_data) * self.results.mean.value,
                ax_index=0,
                label=f"{use_key} mean peaks",
                linewidth=3,
            )


class LeakageNumAnalysisV3(CurveFitAnalysis):
    @classmethod
    def _default_options(cls) -> Options:
        """Create the Analysis options, and set some fields."""
        options = super()._default_options()

        # fit_model_name: skewed_lorentzian or gauss_lorentzian
        options.x_label = "QC Amp (v)"
        options.y_label = "P10"

        options.filter = {"window_length": 5, "polyorder": 3}
        options.result_parameters = ["min_diff", "mean"]
        options.diff_bound = 0.005
        options.use_key = "P10"
        options.distance = 3

        return options

    def _create_analysis_data(self):
        x_data = None
        y_data = {}
        index = 0
        use_key = self.options.use_key
        for child_exp in self.experiment_data._child_data.values():
            key = str(self.experiment_data.x_data[index])
            index += 1
            x_data = child_exp.x_data
            y_data[key] = child_exp.y_data[use_key]
            for ck in list(child_exp.y_data.keys()):
                if ck != use_key:
                    child_exp.y_data.pop(ck, None)

        self.experiment_data._x_data = x_data
        self.experiment_data._y_data = y_data
        self.options.x_label = ["QC Amp (v)" for _ in range(len(y_data))]
        self.options.y_label = ["P10" for _ in range(len(y_data))]

        return super()._create_analysis_data()

    @staticmethod
    def find_closest_elements(arrays):
        """Finds the closest elements by selecting one element from each of the given sorted lists.

        Given multiple sorted lists, this function selects one element from each list such that the
        maximum difference between the selected elements is minimized.

        Args:
            arrays (List[List[float]]): A list of sorted lists, where each inner list contains floats.

        Returns:
            Tuple[List[float], float]: A tuple containing:
                - The combination of elements with the smallest maximum difference.
                - The maximum difference of the selected combination.

        Example:
            >>> arrays = [
            ...     [1.0, 4.0, 7.0],
            ...     [2.0, 5.0],
            ...     [3.0, 6.0, 9.0]
            ... ]
            >>> result, diff = find_closest_elements(arrays)
            >>> print(result)
            [4.0, 5.0, 6.0]
            >>> print(diff)
            2.0
        """
        # Initialize pointers for each list
        pointers = [0] * len(arrays)
        min_diff = math.inf
        best_combination = None

        while True:
            # Get the current elements pointed to by the pointers
            current_elements = [
                arrays[i][pointers[i]]
                for i in range(len(arrays))
                if pointers[i] < len(arrays[i])
            ]
            # If any pointer is out of bounds, skip this list
            if len(current_elements) < len(arrays):
                break
            # Calculate the maximum difference in the current combination
            current_diff = max(current_elements) - min(current_elements)
            # Update the best combination if the current difference is smaller
            if current_diff < min_diff:
                min_diff = current_diff
                best_combination = current_elements
            # Move the pointer of the list containing the smallest element
            min_index = current_elements.index(min(current_elements))
            pointers[min_index] += 1
            # If the pointer goes out of bounds, break the loop
            if pointers[min_index] >= len(arrays[min_index]):
                break

        return best_combination, min_diff

    def _data_processing(self):
        super()._data_processing()

        distance = self.options.distance
        ns = []
        peaks = []
        final_ns = []
        origin_peaks = []
        for n, data in self._analysis_data_dict.items():
            final_ns.append(int(n))
            x = data.x
            y = 1 - data.y
            height = (np.max(y) - np.min(y)) / 4
            prominence = (np.max(y) - np.min(y)) / 5
            points = [
                p for p in get_peak_point(x, y, distance, height, prominence=prominence)
            ]
            cur_peaks = [p.x for p in points]
            peaks.append(cur_peaks)
            origin_peaks.extend(cur_peaks)
            ns.extend([int(n) for _ in range(len(points))])
            pos_list = [(round(point.x, 6), round(1 - point.y, 3)) for point in points]
            rp_list = [f"X\n{pos}" for pos in pos_list]
            self.options.text_pos[n] = (pos_list, rp_list)
        try:
            final_peaks, offset = self.find_closest_elements(peaks)
        except Exception as e:
            logger.error(f"find peak error {e}")
            self._quality = BaseQuality.instantiate(QualityDescribe.bad)
        else:
            if final_peaks is not None:
                self.experiment_data.y_data["all peaks"] = origin_peaks
                self.experiment_data.y_data["nearest peaks"] = final_peaks
                self.experiment_data.replace_x_data = {
                    "all peaks": ns,
                    "nearest peaks": final_ns,
                }
                self.results.min_diff.value = round(offset, 6)
                self.results.mean.value = round(np.mean(final_peaks), 6)
                if self.results.min_diff.value > self.options.diff_bound:
                    self._quality = BaseQuality.instantiate(QualityDescribe.bad)
                else:
                    self._quality = BaseQuality.instantiate(QualityDescribe.normal)
                self.options.x_label.extend(["CZ Num" for _ in range(2)])
                self.options.y_label.extend(["QC Amp (v)" for _ in range(2)])


class CPhaseTMSENumAnalysisV2(StandardCurveAnalysis):
    def _data_processing(self):
        for child_exp in self.experiment_data._child_data.values():
            key = "delta_phase"
            delta_phase = np.array(child_exp.y_data.get(key))
            child_exp.y_data[key] = np.abs(change_phase(delta_phase - np.pi, 0))


@analysis_options_wrapper(x_label="Freq [MHz]")
class CZPhaseAnalysisV2(StandardCurveAnalysis):
    def _data_processing(self):
        x_data = self.experiment_data.metadata.process_meta.get("actual_x_data")

        if len(x_data) == len(self._experiment_data.x_data):
            return

        metadata = self.experiment_data.metadata
        adapter_options = metadata.process_meta.get("adapter_options")

        k = adapter_options.k
        mode = adapter_options.mode
        data_acq = adapter_options.data_acq

        if data_acq:
            p0 = self.experiment_data.y_data.get(data_acq[0][0])
            p1 = self.experiment_data.y_data.get(data_acq[0][1])
            p2 = self.experiment_data.y_data.get(data_acq[1][0])
            p3 = self.experiment_data.y_data.get(data_acq[1][1])
            p0 = (np.array(p0) + np.array(p1)).tolist()
            p1 = (np.array(p2) + np.array(p3)).tolist()
        else:
            p0 = self.experiment_data.y_data.get("P0")
            p1 = self.experiment_data.y_data.get("P1")

        all_phase_data = []
        phase_list = []
        if mode == "TM":
            for j in range(len(p0)):
                if j % 4 == 0:
                    *_, phase_i = phase_tomograph(p0[j], p1[j], p0[j + 1], p1[j + 1])
                    *_, phase_x = phase_tomograph(
                        p0[j + 2], p1[j + 2], p0[j + 3], p1[j + 3]
                    )
                    phase_list.append(change_phase(phase_x - phase_i))
        else:
            for j in range(len(p0)):
                if j % 2 == 0:
                    *_, phase = phase_tomograph(p0[j], p1[j], p0[j + 1], p1[j + 1])
                    phase_list.append(change_phase(phase))

        sl = len(x_data)
        mean_phase = []
        for i, _ in enumerate(x_data):
            tp = []
            for j in range(k):
                tp.append(phase_list[i + j * sl])
            tp = phase_format(np.array(tp))
            all_phase_data.append(tp)
            mean_phase.append(np.mean(tp))
        phase_list = np.array(mean_phase)
        x_data = np.array(x_data)

        self.experiment_data._x_data = x_data.tolist()
        self.experiment_data._y_data = {"delta_phase": phase_list.tolist()}


@options_wrapper
class LeakageOnceV2(TopExperiment):
    """LeakageOnce scan default name z amp list."""

    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("swap_state", ["10", "11", "01"])
        options.set_validator("scan_name", str)
        options.set_validator("z_amp_list", list)
        options.set_validator("cz_num", int)
        options.set_validator("scope", dict)
        options.set_validator("label", ["zz", "cz"])

        options.scope = {"l": 30, "r": 30, "p": 31}
        options.swap_state = "11"
        options.readout_type = "ql-01"
        options.scan_name = "qc"
        options.z_amp_list = None
        options.cz_num = 1
        options.label = "cz"

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.ql = None
        options.qh = None
        options.qc = None
        options.gate_params = {}
        options.width = None
        options.env_bits = []
        options.offset_width = None
        options.support_context = [StandardContext.CGC]
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        return metadata

    def _check_options(self):
        """Check Options."""
        super()._check_options()

        cz_flow_options_adapter(self)
        validate_qubit_pair_cz_std(self)
        validate_two_qubit_exp_read_options(self)
        validate_data_key(self)

        # check offset width
        cz_num = self.experiment_options.cz_num
        offset_width = self.run_options.width * cz_num
        self.set_run_options(offset_width=offset_width)

        pair = self.qubit_pair
        eop = self.experiment_options
        params = pair.metadata.std.cz.params

        # check scope
        if (
            (eop.freq_list is None or len(eop.freq_list) == 0)
            and eop.z_amp_list is None
            and not self.run_options.scan_map
        ):
            left = eop.scope.get("l")
            right = eop.scope.get("r")
            point = eop.scope.get("p")

            if left + right < 1:
                v = params[eop.scan_name]["amp"]
                sweep_list = np.linspace(v - left, v + right, point).tolist()
                self.experiment_options.z_amp_list = sweep_list
                self.run_options.max_iter_count = point
                self.run_options.x_data = sweep_list
            else:
                v = params[eop.scan_name]["freq"]
                if v is None:
                    raise ExperimentFieldError(
                        self.label, f"{eop.scan_name} default freq is None!"
                    )
                sweep_list = np.round(np.linspace(v - left, v + right, point), 3)
                self.experiment_options.freq_list = sweep_list.tolist()
                self._check_options()

        # for async mode.
        self.set_run_options(analysis_class=LeakageAnalysis)
        readout_type = self.experiment_options.readout_type

        if readout_type in ["ql-01", "ql-02", "ql-012"]:
            self.run_options.measure_qubits = [self.run_options.ql]
        elif readout_type in ["qh-01", "qh-02", "qh-012"]:
            self.run_options.measure_qubits = [self.run_options.qh]
        else:
            self.run_options.measure_qubits = [self.run_options.qh, self.run_options.ql]

    @staticmethod
    def set_xy_pulses(builder):
        """Set LeakageOnce experiment XY pulses."""
        eop = builder.experiment_options
        rop = builder.run_options

        for qubit in rop.env_bits:
            if qubit.name == rop.qh.name:
                state = eop.swap_state[0]
            elif qubit.name == rop.ql.name:
                state = eop.swap_state[1]
            elif qubit.name.startswith("q"):
                state = "0"
            else:
                continue

            state_pulse = stimulate_state_pulse(state, qubit)
            offset_pulse = Constant(rop.offset_width, 0, name="XY")

            xy_pulse = state_pulse() + offset_pulse()
            builder.play_pulse(
                "XY", qubit, [deepcopy(xy_pulse) for _ in range(rop.max_iter_count)]
            )

    @staticmethod
    def set_z_pulses(builder):
        """Set LeakageOnce experiment Z pulses."""
        eop = builder.experiment_options
        rop = builder.run_options
        scan_map = builder.run_options.scan_map
        max_iter_count = builder.run_options.max_iter_count
        gate_params = rop.gate_params
        drag_time = pi_pulse(rop.qh).width

        for qubit in rop.env_bits:
            z_pulse_list = []
            q_assign_pulse = Constant(drag_time, 0)
            s_gate_params = gate_params.get(qubit.name)

            collects = scan_map.get(qubit.name, {})
            z_amp_list = collects.get("amp") or [
                s_gate_params.get("amp") for _ in range(max_iter_count)
            ]

            for amp in z_amp_list:
                new_q_assign_pulse = deepcopy(q_assign_pulse)
                s_gate_params.update({"amp": amp, "time": rop.width})
                target_pulse = params_to_pulse(**s_gate_params)
                all_cz_pulse = target_pulse() * eop.cz_num
                once_pulse = new_q_assign_pulse() + all_cz_pulse
                z_pulse_list.append(once_pulse)

            builder.play_pulse("Z", qubit, z_pulse_list)

    def _alone_save_result(self):
        if self.run_options.extra:
            scan_map = self.run_options.extra.get("scan_map_data")

        if scan_map:
            self.file.save_text(
                json.dumps(scan_map, indent=4, ensure_ascii=False),
                name=f"{self}(scan_data)",
                prefix=".json",
            )


class LeakageNumV2(CompositeExperiment):
    _sub_experiment_class = LeakageOnceV2

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()
        options.set_validator("cz_num_list", list)
        options.cz_num_list = None
        options.run_mode = ExperimentRunMode.async_mode
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.plot_2d = True
        return options

    def _check_options(self):
        super()._check_options()
        self.set_run_options(
            x_data=self.experiment_options.cz_num_list,
            analysis_class=LeakageNumAnalysisV2,
        )

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        return metadata

    def _setup_child_experiment(
        self, leak_once_exp: LeakageOnceV2, index: int, cz_num: int
    ):
        leak_once_exp.run_options.index = index
        total = len(self.run_options.x_data)
        c_eop = leak_once_exp.experiment_options
        tail_name = f"cz_num={cz_num}-scan {c_eop.scan_name}"
        leak_once_exp.set_parent_file(self, tail_name, index, total)
        leak_once_exp.set_experiment_options(cz_num=cz_num)
        self._check_simulator_data(leak_once_exp, index)


@options_wrapper
class CPhaseTMSEV2(TopExperiment):
    @classmethod
    def _default_experiment_options(cls) -> Options:
        options = super()._default_experiment_options()

        options.set_validator("mode", ["TM", "SE-TM"])
        options.set_validator("ramsey_bit", str)
        options.set_validator("phase_mode", ["control", "single"])
        options.set_validator("scan_name", str)
        options.set_validator("z_amp_list", list)
        options.set_validator("tc_list", list)
        options.set_validator("k", int)
        options.set_validator("cz_num", int)
        options.set_validator("scope", dict)
        options.set_validator("swap_epd", str)

        options.mode = "SE-TM"
        options.scope = {"l": 30, "r": 30, "p": 31}
        options.phase_mode = "control"
        options.ramsey_bit = None
        options.scan_name = None
        options.z_amp_list = None
        options.tc_list = None
        options.k = 1
        options.cz_num = 1
        options.readout_type = None
        options.swap_epd = ""

        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.x_label = "Amp (V)"
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()

        options.ramsey_qubit = None
        options.drag_qubit = None
        options.data_acq = None

        options.ql = None
        options.qh = None
        options.qc = None
        options.gate_params = {}
        options.width = None
        options.env_bits = []
        options.step = 0

        options.use_detune = False
        options.detune_point = False
        options.support_context = [StandardContext.CGC]
        options.injection_func = [
            "_set_tm_xy_pulse",
            "_set_se_tm_xy_pulse",
            "_set_tm_z_pulse",
            "_set_se_tm_z_pulse",
        ]
        options.need_check_options = True
        options.actual_x_data = None

        return options

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "mode": self.experiment_options.mode,
            "phase_mode": self.experiment_options.phase_mode,
            "k": self.experiment_options.k,
            "cz_num": self.experiment_options.cz_num,
            "scan_name": self.experiment_options.scan_name,
        }
        metadata.process_meta = {
            "actual_x_data": self.run_options.actual_x_data,
            "adapter_options": QDict(
                k=self.experiment_options.k,
                mode=self.experiment_options.mode,
                data_acq=self.run_options.data_acq,
            ),
        }
        return metadata

    def _check_options(self):
        # base context check: check experiment context, once call
        super()._check_options()

        eop = self.experiment_options
        rop = self.run_options

        if eop.swap_epd:
            zc_list, tc_list = [], []
            experiment_data: ExperimentData = from_pick_binary_data(eop.swap_epd)
            for idx, zc in enumerate(experiment_data.x_data):
                child_exp = experiment_data.child_data(idx)
                for peak in child_exp.metadata.process_meta.get("peaks", []):
                    zc_list.append(zc)
                    tc_list.append(peak)
            eop.z_amp_list = zc_list
            eop.tc_list = tc_list
            logger.info(f"zcs:{zc_list}\ntcs:{tc_list}")
            eop.swap_epd = None

        if rop.step == 0:
            cz_flow_options_adapter(self)
            validate_qubit_pair_cz_std(self)
            validate_two_qubit_exp_read_options(self)

            if eop.ramsey_bit == rop.qh.name:
                ramsey_qubit = rop.qh
                drag_qubit = rop.ql
            elif eop.ramsey_bit == rop.ql.name:
                ramsey_qubit = rop.ql
                drag_qubit = rop.qh
            else:
                raise ExperimentOptionsError(
                    self.label,
                    key="ramsey_bit",
                    value=eop.ramsey_bit,
                    msg=f"No find ramsey bit {eop.ramsey_bit}",
                )

            data_acq = None
            if self.experiment_options.readout_type.startswith("union"):
                if ramsey_qubit == rop.qh:
                    data_acq = [["P00", "P01"], ["P10", "P11"]]
                elif ramsey_qubit == rop.ql:
                    data_acq = [["P00", "P10"], ["P01", "P11"]]
            self.set_run_options(
                ramsey_qubit=ramsey_qubit,
                drag_qubit=drag_qubit,
                data_acq=data_acq,
                analysis_class=CZPhaseAnalysisV2,
            )
            if not eop.z_amp_list and not eop.freq_list:
                left = eop.scope.get("l")
                right = eop.scope.get("r")
                point = eop.scope.get("p")
                if left + right < 1:
                    v = rop.gate_params[eop.scan_name]["amp"]
                    sweep_list = np.linspace(v - left, v + right, point).tolist()
                    eop.z_amp_list = sweep_list
                else:
                    v = rop.gate_params[eop.scan_name]["freq"]
                    if v is None:
                        raise ExperimentFieldError(
                            self.label, f"{eop.scan_name} default freq is None!"
                        )
                    sweep_list = np.round(
                        np.linspace(v - left, v + right, point), 3
                    ).tolist()
                    eop.freq_list = sweep_list
            rop.step = 1
        else:
            self._set_analysis_params()

    @staticmethod
    def set_xy_pulses(self):
        if self.experiment_options.mode == "TM":
            pulse_map = self._set_tm_xy_pulse()
        else:
            pulse_map = self._set_se_tm_xy_pulse()

        for unit, pulses in pulse_map.items():
            self.play_pulse("XY", unit, pulses)

    @staticmethod
    def set_z_pulses(self):
        if self.experiment_options.mode == "TM":
            pulse_map = self._set_tm_z_pulse()
        else:
            pulse_map = self._set_se_tm_z_pulse()

        for unit, pulses in pulse_map.items():
            self.play_pulse("Z", unit, pulses)

    def _set_tm_xy_pulse(self):
        pulse_map = {}
        eop = self.experiment_options
        rop = self.run_options
        zero_cz = Constant(rop.width, 0, name="XY")() * eop.cz_num

        open_half_pi = eop.open_half_pi
        delay = half_pi_pulse(self.qh).width if open_half_pi else 0
        compensate = Constant(delay, 0, "XY")()

        for qubit in self.qubits:
            if qubit.name.startswith("c"):
                continue

            if qubit == rop.ramsey_qubit:
                x2 = half_pi_pulse(qubit)()
                y2 = Rphi_gate(phase=np.pi / 2).to_pulse(qubit)()
                xy_pulse_x2 = deepcopy(x2) + compensate + zero_cz + x2 + compensate
                xy_pulse_y2 = deepcopy(x2) + compensate + zero_cz + y2 + compensate
                xy_pulse_list = [
                    deepcopy(xy_pulse_x2),
                    deepcopy(xy_pulse_y2),
                    deepcopy(xy_pulse_x2),
                    deepcopy(xy_pulse_y2),
                ]
            elif qubit == rop.drag_qubit:
                x = pi_pulse(qubit)()
                zero = zero_pulse(qubit)()
                xy_pulse1 = deepcopy(zero) + zero_cz + zero
                xy_pulse2 = x + zero_cz + zero
                xy_pulse_list = [
                    deepcopy(xy_pulse1),
                    deepcopy(xy_pulse1),
                    deepcopy(xy_pulse2),
                    deepcopy(xy_pulse2),
                ]
            else:
                zero = zero_pulse(qubit)()
                xy_pulse = deepcopy(zero) + zero_cz + zero
                xy_pulse_list = [deepcopy(xy_pulse) for _ in range(4)]

            xy_pulses = []
            if self.experiment_options.phase_mode == "control":
                for _ in rop.actual_x_data:
                    xy_pulses.extend(deepcopy(xy_pulse_list))
            else:
                if qubit == rop.drag_qubit:
                    x1 = xy_pulse_list[0]
                    xy_pulse_list = [deepcopy(x1) for _ in range(4)]
                xy_pulses = deepcopy(xy_pulse_list)

            pulse_set = []
            for _ in range(eop.k):
                pulse_set.extend(deepcopy(xy_pulses))

            pulse_map[qubit] = pulse_set

        return pulse_map

    def _set_tm_z_pulse(self):
        pulse_map = {}
        eop = self.experiment_options
        rop = self.run_options

        zero_cz = Constant(rop.width, 0)() * eop.cz_num
        zero_x = zero_pulse(rop.ramsey_qubit, name="Z")()

        for qubit in rop.env_bis:
            gate_param = rop.gate_params.get(qubit.name)
            if eop.phase_mode == "single":
                s_ps = gate_param
                s_ps["time"] = rop.width
                cz_pulse = params_to_pulse(**s_ps)() * eop.cz_num
                z_pulse1 = deepcopy(zero_x) + zero_cz + zero_x
                z_pulse2 = deepcopy(zero_x) + cz_pulse + zero_x
                z_pulses = [
                    deepcopy(z_pulse1),
                    deepcopy(z_pulse1),
                    deepcopy(z_pulse2),
                    deepcopy(z_pulse2),
                ]
            else:
                z_pulses = []
                for i in range(len(self.run_options.actual_x_data)):
                    if qubit.name in rop.scan_map:
                        amp = rop.scan_map.get(qubit.name).get("amp")[i]
                        gate_param["amp"] = amp
                        gate_param["freq"] = 0
                    cz_pulse = params_to_pulse(**gate_param)() * eop.cz_num
                    z_pulse = deepcopy(zero_x) + cz_pulse + zero_x
                    for _ in range(4):
                        z_pulses.append(deepcopy(z_pulse))

            pulse_set = []
            for _ in range(eop.k):
                pulse_set.extend(deepcopy(z_pulses))

            pulse_map[qubit] = pulse_set

        return pulse_map

    def _set_se_tm_xy_pulse(self):
        pulse_map = {}
        eop = self.experiment_options
        rop = self.run_options

        zero_cz = Constant(rop.width, 0, name="XY")() * eop.cz_num

        open_half_pi = eop.open_half_pi
        delay = half_pi_pulse(self.qh).width if open_half_pi else 0
        compensate = Constant(delay, 0, "XY")()

        n = (
            len(rop.actual_x_data)
            if self.experiment_options.phase_mode == "control"
            else 1
        )

        for qubit in self.qubits:
            if qubit.name.startswith("c"):
                continue

            if qubit == rop.ramsey_qubit:
                x2 = half_pi_pulse(qubit)()
                x = pi_pulse(qubit)()
                y2 = Rphi_gate(phase=np.pi / 2).to_pulse(qubit)()
                xy_pulse1 = (
                    deepcopy(x2) + compensate + zero_cz + x + zero_cz + x2 + compensate
                )
                xy_pulse2 = (
                    deepcopy(x2) + compensate + zero_cz + x + zero_cz + y2 + compensate
                )
                xy_pulse_once = [xy_pulse1, xy_pulse2]
            elif qubit == rop.drag_qubit:
                x = pi_pulse(qubit)()
                zero = zero_pulse(qubit)()
                if eop.phase_mode == "control":
                    xy_pulse = deepcopy(zero) + zero_cz + x + zero_cz + zero
                else:
                    xy_pulse = deepcopy(zero) + zero_cz + zero + zero_cz + zero
                xy_pulse_once = [deepcopy(xy_pulse), deepcopy(xy_pulse)]
            else:
                zero = zero_pulse(qubit)()
                xy_pulse = deepcopy(zero) + zero_cz + zero + zero_cz + zero
                xy_pulse_once = [deepcopy(xy_pulse), deepcopy(xy_pulse)]

            xy_pulses = []
            for _ in range(n):
                xy_pulses.extend(deepcopy(xy_pulse_once))

            pulse_set = []
            for _ in range(eop.k):
                pulse_set.extend(deepcopy(xy_pulses))

            pulse_map[qubit] = pulse_set
        return pulse_map

    def _set_se_tm_z_pulse(self):
        pulse_map = {}
        eop = self.experiment_options
        rop = self.run_options

        zero_cz = Constant(rop.width, 0)() * eop.cz_num
        k = self.experiment_options.k
        bit_list = []
        bit_list.extend(self.qubits)
        bit_list.extend(self.couplers)
        zero_x = zero_pulse(self.run_options.ramsey_qubit, name="Z")()

        for qubit in bit_list:
            gate_param = rop.gate_params.get(qubit.name)
            gate_param["time"] = rop.width
            if eop.phase_mode == "single":
                cz_pulse = params_to_pulse(**gate_param)() * eop.cz_num
                z_pulse = deepcopy(zero_x) + zero_cz + zero_x + cz_pulse + zero_x
                z_pulses = [deepcopy(z_pulse), deepcopy(z_pulse)]
            else:
                z_pulses = []
                for i in range(len(self.run_options.actual_x_data)):
                    if qubit.name in rop.scan_map:
                        amp = rop.scan_map.get(qubit.name).get("amp")[i]
                        gate_param["amp"] = amp
                        gate_param["freq"] = 0
                        if eop.tc_list:
                            tc = eop.tc_list[i]
                            gate_param["buffer"] = (rop.width - tc) / 2
                    cz_pulse = params_to_pulse(**gate_param)() * eop.cz_num
                    z_pulse = deepcopy(zero_x) + cz_pulse + zero_x + cz_pulse + zero_x
                    z_pulses.extend([deepcopy(z_pulse), deepcopy(z_pulse)])

            pulse_set = []
            for _ in range(k):
                pulse_set.extend(deepcopy(z_pulses))

            pulse_map[qubit] = pulse_set
        return pulse_map

    def _set_analysis_params(self):
        x_data = self.run_options.x_data
        new_x_data = []
        mode = self.experiment_options.mode
        phase_mode = self.experiment_options.phase_mode
        k = self.experiment_options.k

        if phase_mode == "control":
            if mode == "TM":
                for x in x_data:
                    new_x_data.extend([x for _ in range(4)])
            else:
                for x in x_data:
                    new_x_data.extend([x for _ in range(2)])
        else:
            x_data = [1]
            if mode == "TM":
                new_x_data = [1, 1, 2, 2]
            else:
                new_x_data = [1, 1]

        if k > 1:
            cur_x_data = new_x_data
            new_x_data = []
            for _ in range(k):
                new_x_data.extend(deepcopy(cur_x_data))

        self.set_run_options(actual_x_data=x_data, x_data=new_x_data)

    def _alone_save_result(self):
        z_amp_list = self.experiment_options.z_amp_list
        tc_list = self.experiment_options.tc_list
        delta_phase = self.experiment_data.y_data.get("delta_phase")
        if tc_list:
            self.file.save_data(z_amp_list, tc_list, delta_phase, name="zc-tc-phase")

class CPhaseTMSENumV2(CompositeExperiment):
    _sub_experiment_class = CPhaseTMSEV2

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default Experiment Options."""
        options = super()._default_experiment_options()
        options.set_validator("cz_num_list", list)
        options.cz_num_list = None
        options.run_mode = ExperimentRunMode.async_mode
        return options

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.plot_2d = True
        options.x_label = "CZ Num"
        options.y_label = ["Amp"]
        return options

    def _check_options(self):
        super()._check_options()
        self.set_run_options(
            x_data=self.experiment_options.cz_num_list,
            analysis_class=CZPhaseAnalysisV2,
        )

    def _metadata(self) -> MetaData:
        """Set metadata."""
        metadata = super()._metadata()
        metadata.draw_meta = {
            "mode": self.experiment_options.child_exp_options.mode,
            "k": self.experiment_options.child_exp_options.k,
            "scan_name": self.experiment_options.child_exp_options.scan_name,
        }
        return metadata

    def _setup_child_experiment(self, once_exp: CPhaseTMSEV2, index: int, cz_num: int):
        once_exp.run_options.index = index
        total = len(self.run_options.x_data)
        c_eop = once_exp.experiment_options
        tail_name = f"cz_num={cz_num}-scan {c_eop.scan_name}"
        once_exp.set_parent_file(self, tail_name, index, total)
        once_exp.set_experiment_options(cz_num=cz_num)
        self._check_simulator_data(once_exp, index)


if __name__ == "__main__":
    from app.tool.template_option_tools import generate_options_json

    generate_options_json(
        [CPhaseTMSENumV2],
        file_name="/home/<USER>/code/pyqcat-apps/app/temp_need/cz_extend/o2.json",
    )
