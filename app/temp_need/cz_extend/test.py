# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/03
# __author:       <PERSON><PERSON><PERSON>


if __name__ == "__main__":
    from app.batch_test.dynamic_injection import injection_experiment_libs
    from app.config import init_backend
    from app.temp_need.cz_extend.exp import (
        CPhaseTMSENumV2,
        CPhaseTMSEV2,
        LeakageNumV2,
        LeakageOnceV2,
    )
    from pyQCat.experiments.batch import BatchRunner
    injection_experiment_libs(LeakageOnceV2, LeakageNumV2, CPhaseTMSEV2, CPhaseTMSENumV2)

    backend = init_backend()
    batch = BatchRunner(backend)
    batch.set_experiment_options(
        param_path=r"app/temp_need/cz_extend/options.json",
        exp_retry=0,
        record_batch=True,
        quality_filter=False,
        use_config_unit=True,
        unified_dir=False,
        refresh_context=False,
        flows=["CPhaseTMSEV2"],
        physical_units="q50q56",
    )
    batch.run()
