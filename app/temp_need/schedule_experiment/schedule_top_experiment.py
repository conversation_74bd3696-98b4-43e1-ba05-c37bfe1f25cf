# -*- coding: utf-8 -*-

# This code is part of pyqcat-monster.
#
# Copyright (c) 2021-2025 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/03/19
# __author:       <PERSON><PERSON><PERSON>

import asyncio
from collections import defaultdict
from copy import deepcopy
from typing import Type

from pyQCat import Options
from pyQCat.analysis import CurveAnalysis, TopAnalysis
from pyQCat.errors import ExperimentOptionsError, CompensateError
from pyQCat.experiments.single.swap_once import (
    validate_two_qubit_exp_read_options,
    validate_qubit_pair_cz_std,
    set_measure_pulses,
)
from pyQCat.experiments.top_experiment import TopExperiment, Qubit, extend_f12_pulse
from pyQCat.log import pyqlog
from pyQCat.pulse.pulse_function import pi_pulse, zero_pulse
from pyQCat.pulse.pulse_lib import (
    Pulse<PERSON>omponent,
    FlatTopGaussian,
    Constant,
    FlatTopGaussianSide,
    FlatTopGaussianAsymmetric,
    AcquireSine,
)
from pyQCat.pulse.utilities import compile_pulse
from pyQCat.structures import QDict
from pyQCat.tools import cz_flow_options_adapter, format_results
from pyQCat.types import ExperimentDocStatus
from pyQCat.qaio_property import QAIO
from structure import *


class ExperimentAdapterMode(Enum):

    normal = 0
    adapter_coupler = 1
    adapter_qubit_pair = 2


class ScheduleTopExperiment(TopExperiment):

    mode_adapter = ExperimentAdapterMode.normal

    @classmethod
    def _default_analysis_options(cls) -> Options:
        options = super()._default_analysis_options()
        options.plot_raw_data = True
        options.raw_data_format = "plot"
        options.x_label = ""
        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        options = super()._default_run_options()
        options.scan_parameters = None
        options.const_parameters = None
        options.unit_name2obj = {}
        options.xy_pulse_map = defaultdict(list)
        options.z_pulse_map = defaultdict(list)
        options.parameter_pulse_map = None
        options.same_level_parameter_map = None
        options.coupler_cali_options = None
        options.pair_options = None
        options.total_loop = 0
        options.x_data = None
        options.run_mode = ExperimentRunMode.sync_mode
        return options

    def _register(self):
        # expand the qubit experiment into a coupler experiment
        self._coupler_exp_adapter()

        # tackle const parameters before analyze scan parameters
        self._tackle_const_parameters()

        # tackle scan parameters to generate xy/z pulse map
        self._tackle_scan_parameters()

        # super register
        self._validate_fake_pulse()

        # Top experiment register
        super()._register()

        # standard analysis
        if self.run_options.run_mode == ExperimentRunMode.sync_mode:
            self._special_run_analysis()

    def _check_options(self):
        super()._check_options()

        # check data type
        if self.discriminator is not None:
            data_type = "I_Q"
        else:
            data_type = "amp_phase"
        self.set_experiment_options(data_type=data_type)

        # must set fake before register
        PulseComponent._fake = True

        # check scan/const parameters qubit
        self._check_parameter_bit()

        # check coupler adapter
        if self.mode_adapter == ExperimentAdapterMode.adapter_coupler and self.couplers:
            qc = self.coupler
            probe_q, drive_q = f"q{qc.probe_bit}", f"q{qc.drive_bit}"
            if (
                probe_q in self.run_options.unit_name2obj
                and drive_q in self.run_options.unit_name2obj
            ):
                qd: Qubit = self.run_options.unit_name2obj[drive_q]
                qp: Qubit = self.run_options.unit_name2obj[probe_q]
                self.run_options.coupler_cali_options = QDict(qd=qd, qp=qp, qc=qc)
                self.qubit = qd
                pyqlog.log(
                    "EXP", f"Coupler calibration adapter: qb({qp.name}) | qd({qd.name})"
                )

        # check pair adapter
        if (
            self.mode_adapter == ExperimentAdapterMode.adapter_qubit_pair
            and self.qubit_pair
        ):
            cz_flow_options_adapter(self)
            pair_options = validate_qubit_pair_cz_std(self)
            readout_type = validate_two_qubit_exp_read_options(self)
            pair_options.readout_type = readout_type
            self.run_options.pair_options = pair_options
            pyqlog.log("EXP", f"Qubit Pair Calibration adapter: {self.qubit_pair}")

    def _check_parameter_bit(self):
        parameters = []

        if self.run_options.scan_parameters:
            parameters.extend(self.run_options.scan_parameters)

        if self.run_options.const_parameters:
            parameters.extend(self.run_options.const_parameters)

        for parameter in parameters:
            if not parameter.qubit:
                parameter.qubit = self.qubits[0]
            elif isinstance(parameter.qubit, str):
                parameter.qubit = self.run_options.unit_name2obj.get(parameter.qubit)

    def _tackle_scan_parameters(self):
        inst_parameters = []
        parameter_pulse_map = {}
        for parameter in self.run_options.scan_parameters:
            unit_name = parameter.qubit or self.qubit.name
            if not isinstance(unit_name, str):
                unit_name = unit_name.name
            unit_obj = self.run_options.unit_name2obj[unit_name]
            parameter.qubit = unit_obj

            if isinstance(parameter, PulseParameter):
                if parameter.mode == "xy":
                    pulse = self.xy_pulses.get(unit_obj)[0]
                elif parameter.mode == "z":
                    pulse = self.z_pulses.get(unit_obj)[0]
                else:
                    raise ExperimentOptionsError(
                        self, msg=f"Only support xy or z mode!"
                    )

                parameter_pulse_map[parameter] = pulse
            else:
                inst_parameters.append(parameter)

        self.run_options.parameter_pulse_map = parameter_pulse_map

        self._divide_scan_parameter(self.run_options.scan_parameters)

    def _divide_scan_parameter(self, parameters: List[PulseParameter], idx: int = 0):
        scan_length = len(parameters[0])
        self.run_options.total_loop = scan_length
        pulse_set = set()
        for i in range(scan_length):
            for parameter in parameters:
                if isinstance(parameter, PulseParameter):
                    pulse = self._get_pulse_from_parameters(
                        parameter, i + idx * scan_length
                    )
                    self._set_pulse_from_parameters(
                        pulse, parameter.key, parameter.index, parameter.value[i]
                    )
                    pulse_set.add(parameter.goal_name)
                elif i == 0:
                    self._set_inst_parameter(parameter)

                if parameter.is_plot is True:
                    self.run_options.x_data = parameter.value

    @staticmethod
    def _set_pulse_from_parameters(
        pulse: PulseComponent, key: str, index: Union[str, int, List[str]], value: float
    ):
        if index == "all":
            index = list(range(len(pulse._fake_pulse)))
        elif isinstance(index, int):
            index = [index]

        for idx in index:
            base_pulse = pulse._fake_pulse[idx]
            if key == "bias":
                base_pulse.amp += value
            else:
                setattr(base_pulse, key, value)

        if len(pulse._fake_pulse) == 1:
            if key == "bias":
                pulse.amp += value
            else:
                setattr(pulse, key, value)

        new_width = 0
        for cp in pulse._fake_pulse:
            new_width += cp.width
        pulse.width = new_width

    def _get_pulse_from_parameters(self, parameter: PulseParameter, index: int = 0):
        pulse_map = getattr(self.run_options, f"{parameter.mode}_pulse_map")

        if parameter.qubit in pulse_map and index < len(pulse_map[parameter.qubit]):
            return pulse_map[parameter.qubit][index]
        else:
            pulse = self.run_options.parameter_pulse_map.get(parameter)
            new_pulse = deepcopy(pulse)
            pulse_map[parameter.qubit].append(new_pulse)
            return new_pulse

    def _validate_fake_pulse(self):
        def auto_set_sweep_delay_list():
            width_list = [pulse.width for pulse in temp_pulse_list]
            if len(set(width_list)) != 1:
                self.sweep_readout_trigger_delay(qubit.readout_channel, width_list)

        def auto_check_fake_pulse():
            xy_pulse_map = self.xy_pulses
            z_pulse_map = self.z_pulses
            pulse_length_list = [len(pl) for pl in xy_pulse_map.values()]
            pulse_length_list.extend([len(pl) for pl in z_pulse_map.values()])
            max_length = max(pulse_length_list)

            for bit in list(xy_pulse_map.keys()):
                pulses = xy_pulse_map[bit]
                if not pulses and z_pulse_map[bit]:
                    xy_pulse_map[bit] = [
                        Constant(p.width, 0, "XY")() for p in z_pulse_map[bit]
                    ]
                elif len(pulses) == 1 and max_length > 1:
                    xy_pulse_map[bit] = [deepcopy(pulses[0]) for _ in range(max_length)]

            for bit in list(z_pulse_map.keys()):
                pulses = z_pulse_map[bit]
                if not pulses and xy_pulse_map[bit]:
                    z_pulse_map[bit] = [
                        Constant(p.width, 0, "Z")() for p in xy_pulse_map[bit]
                    ]
                if len(pulses) == 1 and max_length > 1:
                    z_pulse_map[bit] = [deepcopy(pulses[0]) for _ in range(max_length)]

            if self.experiment_options.fake_pulse is False:
                for bit in list(xy_pulse_map.keys()):
                    pulses = xy_pulse_map[bit]
                    new_pulse = [compile_pulse(p) for p in pulses]
                    xy_pulse_map[bit] = new_pulse

                for bit in list(z_pulse_map.keys()):
                    pulses = z_pulse_map[bit]
                    new_pulse = [compile_pulse(p) for p in pulses]
                    z_pulse_map[bit] = new_pulse

                PulseComponent._fake = self._experiment_options.fake_pulse

        for qubit, fake_pulse_list in self.run_options.xy_pulse_map.items():
            temp_pulse_list = []
            for fake_pulse in fake_pulse_list:
                tp = self._format_fake_pulse(fake_pulse)
                tp.bit = qubit.name
                temp_pulse_list.append(tp)
            self.xy_pulses[qubit] = temp_pulse_list

            if (
                qubit not in self.run_options.z_pulse_map
                and qubit in self.readout_qubits
            ):
                auto_set_sweep_delay_list()

        for qubit, fake_pulse_list in self.run_options.z_pulse_map.items():
            temp_pulse_list = []
            for fake_pulse in fake_pulse_list:
                tp = self._format_fake_pulse(fake_pulse)
                tp.bit = qubit.name
                temp_pulse_list.append(tp)
            self.z_pulses[qubit] = temp_pulse_list

            if qubit in self.readout_qubits:
                auto_set_sweep_delay_list()

        auto_check_fake_pulse()

    @staticmethod
    def _format_fake_pulse(pulse):
        new_pulse = pulse._fake_pulse[0]()
        for pulse in pulse._fake_pulse[1:]:
            new_pulse += pulse()
        # if self.experiment_options.fake_pulse is False:
        #     new_pulse.envelope2sequence()
        return new_pulse

    def _refresh(self, is_reload: bool = True):
        keys = ["_experiment", "inst", "xy_pulses", "z_pulses"]

        if is_reload is True:
            for k, v in self.run_options.cache.items():
                setattr(self, k, deepcopy(v))
        else:
            self.run_options.cache = {}
            for key in keys:
                self.run_options.cache[key] = getattr(self, key)

    def _special_run_analysis(self):
        """Experiment special analysis run logic."""
        self._run_analysis(
            x_data=self.run_options.x_data or list(range(self.run_options.total_loop)),
            analysis_class=CurveAnalysis,
        )

    def _tackle_const_parameters(self):
        for parameter in self.run_options.const_parameters:
            unit_obj = parameter.qubit
            if isinstance(unit_obj, str):
                unit_obj = self.run_options.unit_name2obj.get(unit_obj)

            if isinstance(parameter, PulseParameter):
                if parameter.mode == "xy":
                    pulse = self.xy_pulses.get(unit_obj)[0]
                elif parameter.mode == "z":
                    pulse = self.z_pulses.get(unit_obj)[0]
                else:
                    raise ExperimentOptionsError(
                        self, msg=f"Only support xy or z mode!"
                    )
                self._set_pulse_from_parameters(
                    pulse, parameter.key, parameter.index, parameter.value
                )
            elif isinstance(parameter, InstrumentParameter):
                self._set_inst_parameter(parameter)

    def _coupler_exp_adapter(self):
        if self.run_options.coupler_cali_options:
            qd = self.run_options.coupler_cali_options.qd
            qp = self.run_options.coupler_cali_options.qp
            qc = self.run_options.coupler_cali_options.qc

            probe_pi_pulse = pi_pulse(qp)()
            drive_pulse = self.xy_pulses.get(qd)[0]

            # load qc z pulse
            qc_z_pulse = self.z_pulses.get(qc)
            if qc_z_pulse:
                qc_z_pulse = qc_z_pulse[0]
            else:
                qc_z_pulse = Constant(drive_pulse.width, 0)()

            # adapter probe qubit and drive qubit xy pulse
            drive_pulse += zero_pulse(qp)()
            probe_pulse = Constant(drive_pulse.width, 0, "XY")() + probe_pi_pulse

            # adapter coupler z pulse
            if (
                self.coupler.readout_point.amp == 0.0
                or self.coupler.pi_pulse_point.amp == 0.0
                or self.coupler.readout_point.amp * self.coupler.pi_pulse_point.amp < 0
            ):
                self.run_options.coupler_cali_options.readout_point_mode = 0
                z_pi_pulse = FlatTopGaussian(
                    time=probe_pi_pulse.width, **self.coupler.pi_pulse_point
                )
            elif (
                0 > self.coupler.pi_pulse_point.amp >= self.coupler.readout_point.amp
                or 0 < self.coupler.pi_pulse_point.amp <= self.coupler.readout_point.amp
            ):
                if self.coupler.pi_pulse_point.amp == self.coupler.readout_point.amp:
                    self.run_options.coupler_cali_options.readout_point_mode = 1
                else:
                    self.run_options.coupler_cali_options.readout_point_mode = 2
                z_pi_pulse = FlatTopGaussianSide(
                    time=probe_pi_pulse.width, **self.coupler.pi_pulse_point
                )
            elif self.coupler.pi_pulse_point.amp < 0:
                z_pi_pulse = FlatTopGaussianAsymmetric(
                    time=probe_pi_pulse.width,
                    **self.coupler.pi_pulse_point,
                    amp2=self.coupler.pi_pulse_point.amp
                    - self.coupler.readout_point.amp,
                    side="right",
                )
                self.run_options.coupler_cali_options.readout_point_mode = 1
            else:
                z_pi_pulse = FlatTopGaussianAsymmetric(
                    time=probe_pi_pulse.width,
                    **self.coupler.pi_pulse_point,
                    amp2=self.coupler.pi_pulse_point.amp
                    - self.coupler.readout_point.amp,
                    side="right",
                )
                self.run_options.coupler_cali_options.readout_point_mode = 1
            qc_z_pulse += z_pi_pulse()

            # repeat play pulse
            self.play_pulse("XY", qd, drive_pulse)
            self.play_pulse("XY", qp, probe_pulse)
            self.play_pulse("Z", qc, qc_z_pulse)

    def _fill_readout_work_point_pulse(
        self, qubit: QubitType, readout_pulse_width: float
    ):
        coupler_cali_options = self.run_options.coupler_cali_options

        if (
            isinstance(qubit, Qubit)
            or coupler_cali_options is None
            or coupler_cali_options.readout_point_mode == 0
        ):
            return super()._fill_readout_work_point_pulse(qubit, readout_pulse_width)
        else:
            if self.run_options.coupler_cali_options.readout_point_mode == 1:
                pulse = FlatTopGaussianSide(
                    time=readout_pulse_width, side="right", **qubit.readout_point
                )
            elif self.run_options.coupler_cali_options.readout_point_mode == 2:
                pulse = FlatTopGaussianAsymmetric(
                    time=readout_pulse_width,
                    amp2=self.coupler.readout_point.amp
                    - self.coupler.pi_pulse_point.amp,
                    **qubit.check_readout_point(),
                )
            else:
                raise CompensateError(f"Coupler Readout Point Error!")

            pulse.type = "Z"
            pulse.bit = qubit.name
            pulse()
            return pulse

    def _set_inst_parameter(self, parameter):
        pyqlog.log("EXP", f"Set {str(parameter)}")

        qubit = parameter.qubit
        if parameter.mode == "xy":
            module_name = "XY_control"
            channel = qubit.xy_channel
        elif parameter.mode == "m":
            module_name = "Readout_control"
            channel = qubit.readout_channel
        else:
            raise ExperimentOptionsError(self, msg=f"Unknown mode: {parameter.mode}")

        if isinstance(parameter.value, List):
            func_name = f"sweep_{parameter.key}"
            func = getattr(self.inst, func_name, None)
            if func:
                func(
                    module_name,
                    channel,
                    points=parameter.value,
                    repeat=self.experiment_options.repeat,
                )
        else:
            func_name = f"set_{parameter.key}"
            func = getattr(self.inst, func_name, None)
            if func:
                func(module_name, channel, parameter.value)

    def set_analysis_options(self, **fields):
        self._analysis_options.update(**fields)

    def set_experiment_options(self, **fields):
        self._experiment_options.update(**fields)

    def set_run_options(self, **fields):
        self._run_options.update(**fields)

    def get_bit_obj(self, unit: str):
        return self.run_options.unit_name2obj.get(unit)

    def _get_readout_channels(self) -> List[int]:
        """Get readout or sample channels."""
        if self.run_options.online_bits:
            # feature online ctx use all qubit readout channel
            readout_channels = [
                bit.readout_channel
                for bit in self.run_options.online_bits
                if isinstance(bit, Qubit)
            ]
        elif self.experiment_options.multi_readout_channels:
            readout_channels = self.experiment_options.multi_readout_channels
        elif self.run_options.coupler_cali_options:
            readout_channels = [
                self.run_options.coupler_cali_options.qp.readout_channel
            ]
        else:
            readout_channels = [self.qubits[0].readout_channel]
        return readout_channels

    def _bind_qubit_probe(self):
        if self.experiment_options.bind_probe:
            if self.run_options.coupler_cali_options:
                self._bind_probe_inst(self.run_options.coupler_cali_options.qp)
            elif self.qubit:
                self._bind_probe_inst(self.qubit)

    def _set_measure_pulses(self):
        if self.run_options.pair_options:
            set_measure_pulses(self)
        else:
            self._set_single_readout_pulse()

    @extend_f12_pulse
    def _set_single_readout_pulse(self, qubit=None):
        """Set single bit readout pulse."""
        if qubit is None:
            qubit = self.qubit

        pyqlog.debug("single qubit set readout parameters")
        time = qubit.Mwave.width
        amp = qubit.Mwave.amp
        baseband_freq = qubit.Mwave.baseband_freq
        channel = qubit.readout_channel

        self.set_multiple_baseband_freq(*[baseband_freq], channel=channel)
        self.set_multiple_index(*[[]], channel=channel)

        acq_pulse = AcquireSine(
            time=time, amp_list=[amp], baseband_freq_list=[baseband_freq]
        )()
        if self.experiment_options.fake_pulse is False:
            acq_pulse = compile_pulse(acq_pulse)
        self.readout_pulses.append(acq_pulse)
        self.readout_qubits.append(qubit)
        self.inst.set_custom_waveform("Readout_control", channel, [acq_pulse.pulse])

        return qubit

    def _get_union_readout_pulse_utility(
        self,
        readout_channel: int,
        width: float,
        baseband_freq_list: List[float],
        index_list: List[int],
        amp_list: List[float],
    ):
        """Set readout pulse with parameters."""
        self.set_multiple_baseband_freq(*baseband_freq_list, channel=readout_channel)
        self.set_multiple_index(*index_list, channel=readout_channel)
        union_readout_pulse = AcquireSine(width, amp_list, baseband_freq_list)()
        if self.experiment_options.fake_pulse is False:
            union_readout_pulse = compile_pulse(union_readout_pulse)
        self.readout_pulses.append(union_readout_pulse)
        self.inst.set_custom_waveform(
            "Readout_control", readout_channel, [union_readout_pulse.pulse]
        )

    def _retry_experiment(self, x_data: Union[List, np.ndarray], count: int, info: str):
        pyqlog.warning(f"{info}, Retry index-{count + 1}")
        self._experiment.status = ExperimentDocStatus.trans
        self._experiment.status = self._match_doc_status()
        self._experiment.measure_data = {}
        self._experiment.save()
        self.send_to_chimera()
        self._data_collection(x_data, count=count + 1)

    def _prepare_analysis_data(
        self,
        x_data: Union[List, np.ndarray],
    ):
        # create experiment data
        self._create_experiment_data(
            data_type=self.experiment_options.data_type,
            x_data=x_data,
            loop_num=self.experiment_options.loop_num,
        )

        # save source data
        self._save_source_data(
            label=None,
            iq_flag=self.experiment_options.iq_flag,
        )

        if hasattr(self, "correct_data") and self.correct_data:
            self.experiment_data.y_data = self.correct_data

    def _run_analysis_flow(self, acq_status: int, analysis_class: Type[TopAnalysis]):
        if acq_status == ExperimentDocStatus.finished:
            # create analysis object.
            self.analysis = analysis_class(self.experiment_data)

            # update options.
            self.analysis.set_options(**self._analysis_options)

            # run analysis.
            self.analysis.run_analysis()
            self._save_curve_analysis_plot()

            self.analysis.update_result()

            self.update_execute_exp()
        elif acq_status in [
            ExperimentDocStatus.failed,
            ExperimentDocStatus.chimera_validate_error,
            ExperimentDocStatus.aio_error,
        ]:
            r_name = self.analysis_options.result_name
            status_m = ExperimentDocStatus.status_map.get(acq_status)
            pyqlog.error(
                f"{self.label}({r_name}) id: {self.id}, status: {acq_status}, {status_m}"
            )
            self.update_execute_exp(2)

    def _initialize_data_acquisition(self, x_data: Union[List, np.ndarray], count: int):
        # feature: use online dcm when check online context
        if self.run_options.online_dcms:
            discriminator = self.run_options.online_dcms
        else:
            discriminator = self.discriminator
        # acquisition data
        acq_class = self.acquisition_class_map.get(self.run_options.acquisition_key)
        sample_channels = self._get_readout_channels()
        acq_obj = acq_class(
            id_=self.id,
            data_type=self.experiment_options.data_type,
            discriminator=discriminator,
            is_dynamic=self.experiment_options.is_dynamic,
            is_amend=self.experiment_options.is_amend,
            fidelity_correct_type=self.experiment_options.fidelity_correct_type,
            post_select_type=self.experiment_options.post_select_type,
            measure_qubits=self.run_options.measure_qubits,
            simulator_data_path=self.run_options.simulator_data_path,
            save_path=self.file.dirs,
            sample_channels=sample_channels,
            is_parallel=self.run_options.parallel,
            simulator_name=self.run_options.simulator_name,
            use_simulator=self.run_options.use_simulator,
            simulator_index=self.run_options.simulator_index,
            config=self.config,
            plot_iq=self.experiment_options.plot_iq,
            is_retry=bool(count),
        )
        if x_data is not None:
            acq_obj.x_list = x_data
        return acq_obj

    async def _async_query_acq_status(self):
        r_name = self.analysis_options.result_name
        acq_status = self.data_acquisition.get_experiment("status")
        self.data_acquisition._status = acq_status
        status_m = ExperimentDocStatus.status_map.get(acq_status)
        info = f"{self.label}({r_name}) id: {self.id}, status: {acq_status}, {status_m}"
        return info, acq_status

    async def _async_retry_experiment(
        self, x_data: Union[List, np.ndarray], count: int, info: str
    ):
        self._retry_experiment(x_data, count, info)
        status = await self._async_data_collection(x_data, count=count + 1)
        return status

    async def _async_data_collection(
        self, x_data: Union[List, np.ndarray] = None, count: int = 0
    ):
        """Asynchronous Data Collector Interface.

        Args:
            x_data (Union[List, np.ndarray]): scan parameter range
            count (int): Which times collection data.
        """
        parallel = self.run_options.parallel
        # acquisition data
        self.data_acquisition = self._initialize_data_acquisition(x_data, count)
        while 1:
            if self.experiment_options.use_simulator is False:
                info, acq_status = await self._async_query_acq_status()
                if (
                    acq_status
                    in [
                        ExperimentDocStatus.failed,
                        ExperimentDocStatus.chimera_validate_error,
                        ExperimentDocStatus.aio_error,
                    ]
                    and parallel is False
                ):
                    if count < 4:
                        return await self._async_retry_experiment(x_data, count, info)
                    else:
                        return acq_status
                elif acq_status == ExperimentDocStatus.finished:
                    self.data_acquisition.execute_loop_parallel()
                    self._prepare_analysis_data(x_data)
                    # print("finished.")
                    return acq_status
                else:
                    # pyqlog.warning(f"{info}")
                    if parallel is True:
                        raise ExperimentOptionsError(
                            self, msg=f"No support parallel mode"
                        )
            else:
                self.data_acquisition.execute_loop()
                self._prepare_analysis_data(x_data)
                return ExperimentDocStatus.finished

    async def _async_run_analysis_flow(self, analysis_class):
        acq_status = self.data_acquisition.status
        self._run_analysis_flow(acq_status, analysis_class)

    async def async_run(self):
        # run flow
        self._check_options()
        self._validate_options()
        self._initialize_experiment()
        self._set_xy_pulses()
        self._set_z_pulses()
        self._set_measure_pulses()
        self._update_instrument()
        self._register()

        x_data = self.run_options.x_data

        # data collection
        await asyncio.create_task(self._async_data_collection(x_data))

        # Analysis must be happened after data all collected.
        await asyncio.create_task(self._async_run_analysis_flow(CurveAnalysis))

    def _auto_set_readout_delay(self):
        """Auto set readout delay according to XY and Z pulse length."""
        width_list = [p.width for p in list(self.xy_pulses.values())[0]]
        delay_step = QAIO.get_range("trigger_delay")[-1]
        width_list = QAIO.delay_ceil(width_list, delay_step)
        self.readout_delay = max(width_list)

        readout_control_list = self._experiment.measure_aio.Read_out_control
        for readout_control, readout_pulse in zip(
            readout_control_list, self.readout_pulses
        ):
            raw_delay = self.inst.get_trigger_delay(
                "Readout_control", readout_control.channel
            )
            self._m_delay_list = np.array(self._pulse_time_list) + raw_delay[0]
            delay = raw_delay[0] + self.readout_delay
            delay += self.experiment_options.ac_prepare_time
            readout_pulse.delay = delay
            if QAIO.type != QAIO.qaio_72:
                self.inst.set_trigger_delay(
                    "Readout_control", readout_control.channel, delay
                )
            else:
                # todo, locate use 72bit aio schedule bug, 2023-01-16
                #  adjust trigger delay different to modify pulse,
                #  and XY Line pulse splicing no calling envelope2sequence(),
                #  maybe this method will to optimize.
                sweep_control = self.inst.get_target_sweep(
                    "Readout_control", readout_control.channel, "trigger_delay"
                )
                if not sweep_control:
                    delay_after = QAIO.delay_ceil(delay)
                    delay_com = delay_after - delay
                    self._m_delay_list = QAIO.delay_ceil(self._m_delay_list + delay_com)
                    self.inst.set_trigger_delay(
                        "Readout_control", readout_control.channel, delay_after
                    )
                    # if delay_com:
                    #     self._delay_compensate(delay_com)
                else:
                    # self._sweep_compensate(sweep_control)
                    readout_sweep_delay = np.asarray(sweep_control.points)
                    delay_after = QAIO.delay_ceil(readout_sweep_delay)
                    sweep_control.points = delay_after.tolist()
