# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/06/12
# __author:       <PERSON><PERSON><PERSON>

from pathlib import Path

from app.automation.executor import AutomationExecutor, QDict
from app.config import init_backend


def main():
    root = Path(__file__).absolute().parent / "conf"
    executor = AutomationExecutor(init_backend())
    executor.set_experiment_options(
        node_config=str(root / "auto.json"),
        experiment_config=str(root / "experiment.json"),
        # nodes=[
        #     "CloseIMPA"
        #     "BatchSaturationPower",
        #     "BatchBusS21",
        #     "BatchCavityQ",
        #     "BatchImpaCalibrate",
        #     "OpenIMPA",
        #     "BatchCavityPowerScan_V0",
        #     "BatchCouplerTunable",
        #     "BatchQubitTunable",
        #     "BatchCavityPowerScan_VMax",
        #     "BatchQubitSpectrumZAmp",
        #     "BatchSearchIdlePoint",
        #     "BatchReadout",
        #     "BatchSearchF12",
        #     "BatchXYCrossRabiWidth",
        #     "BatchXZTiming",
        #     "BatchACT1Spectrum",
        #     "BatchRBSpectrum",
        # ],
        nodes=[
            "BatchBusImpaClose",
            "BatchSaturationPower",
            "BatchBusS21",
            "BatchBusCavityQ",
            "BatchBusCavityCheck",
            "BatchBusImpaCalibrate",
            "BatchBusImpaOpen",
            "BatchCavityPowerScan_V0",
            "BatchCouplerTunable",
            "BatchQubitTunable",
            "BatchQubitCavityCheck",
            "BatchQubitTunable",
            "BatchCavityPowerScan_VMax",
            "BatchQubitSpectrumZAmp",
            "BatchSearchIdlePoint",
            "BatchReadout",
            "BatchSingleQubitCalibration",
            "BatchSearchF12",
            "BatchXYCrossRabiWidth",
            "BatchXZTiming",
            "BatchACT1Spectrum",
            "BatchRBSpectrum",
        ],
        # physical_unit_map=QDict(
        #     qubit=[],
        #     coupler=[],
        #     qubit_pair=[],
        #     bus=[],
        # ),
    )

    executor.run()


if __name__ == "__main__":
    main()
