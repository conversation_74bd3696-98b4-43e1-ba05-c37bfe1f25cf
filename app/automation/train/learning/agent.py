# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/11
# __author:       <PERSON><PERSON><PERSON>, <PERSON><PERSON>

from copy import deepcopy
from typing import Dict

import numpy as np
from loguru import logger

from app.automation.train.learning.base_strategy import BaseStrategy
from app.automation.train.structure import ActionTypeEnum, Input

np.set_printoptions(
    precision=3,  # 保留3位小数
    suppress=True,  # 禁止科学计数法
    linewidth=150,  # 每行最大宽度
)


class QLearningAgent:
    def __init__(self, strategy: BaseStrategy):
        """_summary_

        Args:
            strategy (BaseStrategy): _description_

        Fields:
            q_table (np.ndarray): 学习结果
            alpha (float): 学习率
            gamma (float): 折扣因子
            epsilon (float): 探索率
        """
        self.strategy = strategy
        self.q_table = np.zeros((strategy.n_states, strategy.n_actions))
        self.q_table[:, -1] = -np.inf  # 不可以直接通过学习结果从状态跳转至成功结果
        self.q_table[:, -2] = -np.inf  # 不可以直接通过学习结果从状态跳转至失败结果
        self.alpha = 0.1  # 学习率
        self.gamma = 0.9  # 折扣因子
        self.epsilon = 0.95  # 探索率
        self.count_limit = 50
        self._count = 0

    def choose_action(self, states: Dict[str, int]):
        self._count += 1
        logger.info(f"Study count {self._count} start ...")
        actions = {}
        for unit, state in states.items():
            action_type = None
            pre_action = self.strategy.cur_actions.get(unit)
            if self._count > self.count_limit:
                action_id = len(self.strategy.actions) - 1
                logger.warning(
                    f"Study count {self._count} maximum number of iterations has been reached ..."
                )
            elif np.random.random() < self.epsilon or state == 0:  # 探索
                logger.info(f"{unit} 进入探索过程...")
                action_id = np.random.choice(self.strategy.state_to_action[state])
            else:  # 利用
                logger.info(f"{unit} 进入利用过程...")
                action_id = np.argmax(self.q_table[state])
                action_type = ActionTypeEnum.SEARCH
            action = deepcopy(self.strategy.actions[action_id])
            action.unit = unit
            action.id = action_id
            if action_type:
                action.type = action_type
            if action.type == ActionTypeEnum.NEXT and pre_action:
                action.key = pre_action.experiment
            actions[unit] = action
            logger.info(f"{unit} receive state {state}, next action {action}")
        self.strategy.cur_actions = actions

    def learn(self, unit: str):
        pre_state = self.strategy.pre_state.get(unit)
        action = self.strategy.cur_actions.get(unit).id
        cur_state = self.strategy.cur_state.get(unit)
        reward = self.strategy.cur_rewards.get(unit)
        done = self.strategy.cur_dones.get(unit)

        current_q = self.q_table[pre_state, action]  # 当前 Q 值
        max_next_q = np.max(self.q_table[cur_state])  # 下一状态的最大 Q 值
        target_q = reward + self.gamma * max_next_q * int(
            ~done
        )  # 如果 done=True，则忽略下一状态
        self.q_table[pre_state, action] += self.alpha * (target_q - current_q)

    def ask_actions(self, input: Input):
        if not input.data:
            self.strategy.reset(input.physical_units)
            pre_state = self.strategy.pre_state
            self.choose_action(pre_state)
        else:
            self.strategy.cur_rewards.clear()
            self.strategy.cur_dones.clear()
            cur_state = {}
            for result in input.data:
                cur_state[result.unit] = self.strategy.state_from_experiment_result(
                    result
                )
            self.strategy.step(cur_state)
            for unit in cur_state:
                self.learn(unit)
            self.choose_action(cur_state)
            self.strategy.pre_state = cur_state
            self.strategy.cur_state = None
            logger.info(f"Study count {self._count}, table: \n{self.q_table}")
        return self.strategy.cur_actions
