# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/18
# __author:       <PERSON><PERSON><PERSON>


import re
from typing import Callable, List, Optional

from loguru import logger

from app.automation.train.structure import ExperimentResult


class StateRuleParser:
    """Parses state rules into executable condition functions.

    Attributes:
        parsed_rules: List of parsed rules in format (state_id, experiment_name, conditions)
        init_state: State ID for initialization state
        other_state: State ID for unmatched cases
    """

    def __init__(self, state_rules: List[str]):
        """Initializes the parser with state rules.

        Args:
            state_rules: List of state definition strings
        """
        self.init_state = 0  # Default initialization state
        self.other_state = 0  # Default state for unmatched cases
        self.parsed_rules = self._parse_rules(state_rules)

    def _parse_rules(self, state_rules: List[str]) -> List:
        """Parses state rules into structured format.

        Rules are parsed into tuples containing:
        - state_id: Integer state identifier
        - experiment_name: Name of experiment the rule applies to
        - conditions: List of callable condition functions

        Special cases:
        - "Init" sets the initialization state
        - "Other" sets the fallback state

        Args:
            state_rules: List of state definition strings

        Returns:
            List of parsed rules in format (state_id, experiment_name, conditions)
        """
        parsed_rules = []
        for rule in state_rules:
            # Split state ID from rule content
            parts = rule.split("(", 1)
            state_id = int(parts[0].strip())

            if len(parts) < 2 or not parts[1].endswith(")"):
                parsed_rules.append((state_id, "Other", []))
                continue

            content = parts[1].rstrip(")").strip()
            segments = [s.strip() for s in content.split(",")]
            exp_name = segments[0]

            # Handle special states
            if exp_name == "Init":
                self.init_state = state_id
                continue

            if exp_name == "Other":
                self.other_state = state_id
                continue

            # Parse conditions for regular rules
            conditions = []
            for cond_str in segments[1:]:
                if cond_str:
                    cond_func = self._parse_condition(cond_str)
                    if cond_func:
                        conditions.append(cond_func)

            parsed_rules.append((state_id, exp_name, conditions))

        return parsed_rules

    def _parse_condition(self, cond_str: str) -> Optional[Callable]:
        """Parses a condition string into an executable function.

        Supports three condition formats:
        1. Compound conditions (e.g., "0.7<r2<0.9")
        2. Simple conditions (e.g., "r2>=0.9")
        3. Reverse conditions (e.g., "0.6>=xpi")

        Args:
            cond_str: Condition definition string

        Returns:
            Callable condition function or None if parsing fails
        """
        # Handle compound conditions (e.g., 0.7<r2<0.9)
        compound_match = re.match(
            r"(\d*\.?\d+)\s*([<>]=?)\s*(\w+)\s*([<>]=?)\s*(\d*\.?\d+)", cond_str
        )
        if compound_match:
            left_val = float(compound_match.group(1))
            op1 = compound_match.group(2)
            var = compound_match.group(3)
            op2 = compound_match.group(4)
            right_val = float(compound_match.group(5))

            def compound_cond(result):
                """Evaluates compound condition against experiment result."""
                val = getattr(result, var, None)
                if val is None:
                    return False
                try:
                    num_val = float(val)
                except (TypeError, ValueError):
                    return False

                # Evaluate based on operator direction
                if op1.startswith("<"):
                    cond1 = num_val > left_val
                else:  # Handles '>' or '>='
                    cond1 = num_val < left_val

                if op2.startswith("<"):
                    cond2 = num_val < right_val
                else:  # Handles '>' or '>='
                    cond2 = num_val > right_val

                return cond1 and cond2

            return compound_cond

        # Handle simple conditions (e.g., r2>=0.9)
        simple_match = re.match(r"(\w+)\s*([<>]=?)\s*(\d*\.?\d+)", cond_str)
        if simple_match:
            var = simple_match.group(1)
            op = simple_match.group(2)
            threshold = float(simple_match.group(3))

            def simple_cond(result):
                """Evaluates simple condition against experiment result."""
                val = getattr(result, var, None)
                if val is None:
                    return False
                try:
                    num_val = float(val)
                except (TypeError, ValueError):
                    return False

                if op == ">":
                    return num_val > threshold
                elif op == ">=":
                    return num_val >= threshold
                elif op == "<":
                    return num_val < threshold
                elif op == "<=":
                    return num_val <= threshold

                return False

            return simple_cond

        # Handle reverse conditions (e.g., 0.6>=xpi)
        reverse_match = re.match(r"(\d*\.?\d+)\s*([<>]=?)\s*(\w+)", cond_str)
        if reverse_match:
            threshold = float(reverse_match.group(1))
            op = reverse_match.group(2)
            var = reverse_match.group(3)

            # Normalize operator direction
            op_map = {">": "<", ">=": "<=", "<": ">", "<=": ">="}
            normalized_op = op_map.get(op, op)

            def reverse_cond(result):
                """Evaluates reverse condition against experiment result."""
                val = getattr(result, var, None)
                if val is None:
                    return False
                try:
                    num_val = float(val)
                except (TypeError, ValueError):
                    return False

                if normalized_op == ">":
                    return num_val > threshold
                elif normalized_op == ">=":
                    return num_val >= threshold
                elif normalized_op == "<":
                    return num_val < threshold
                elif normalized_op == "<=":
                    return num_val <= threshold

                return False

            return reverse_cond

        return None


class StateMatcher:
    """Matches experiment results to defined states using parsing rules."""

    def __init__(self, state_rules: List[str]):
        """Initializes the matcher with state rules.

        Args:
            state_rules: List of state definition strings
        """
        self.parser = StateRuleParser(state_rules)
        self.rules = self.parser.parsed_rules
        print("other_state", self.parser.other_state)

    def match_state(self, result: Optional[ExperimentResult] = None) -> int:
        """Determines state ID for an experiment result or returns initialization state.
        
        The matching process follows these steps:
        1. If no result is provided, returns the initialization state
        2. For valid results, checks rules matching the experiment name
        3. Verifies all conditions are satisfied for matching rules
        4. Returns state ID of first matching rule
        5. Returns other_state if no matches found
        
        Special Cases:
        - When result is None: Returns initialization state (init_state)
        - When no rules match: Returns fallback state (other_state)
        
        Args:
            result: Optional ExperimentResult instance to evaluate. Defaults to None.
        
        Returns:
            Integer state ID:
            - init_state when result is None
            - Matching state ID when rules match
            - other_state when no rules match
        """
        # Handle initialization case
        if result is None:
            return self.parser.init_state
        
        # Check for matching experiment rules
        for state_id, exp_name, conditions in self.rules:
            # Skip rules for different experiments
            if result.experiment != exp_name:
                continue

            # Verify all conditions are satisfied
            if all(cond(result) for cond in conditions):
                return state_id

        logger.warning(f"No matching status found, return to other status `{self.parser.other_state}`")

        # Return fallback state if no matches found
        return self.parser.other_state


if __name__ == "__main__":

    # 输入状态规则
    state_rules = [
        "0(Init)",
        "1(CavityFreqSpectrum,r2>=0.9)",
        "2(CavityFreqSpectrum,0.7<r2<0.9)",
        "3(CavityFreqSpectrum,r2<=0.7)",
        "4(QubitSpectrum,snr>=4)",
        "5(QubitSpectrum,2<snr<4)",
        "6(QubitSpectrum,snr<=2)",
        "7(RabiScanWidth,r2>=0.9)",
        "8(RabiScanWidth,r2<0.9)",
        "9(XpiDetection1,r2>0.9,0.6>=xpi)",
        "10(XpiDetection1,r2>0.9,xpi>=0.8)",
        "11(XpiDetection1,r2>0.9,0.8>xpi>0.6)",
        "12(XpiDetection1,r2<0.9)",
        "13(QubitFreqCalibration,r2>0.9,gap<=5)",
        "14(QubitFreqCalibration,r2>0.9,gap>5)",
        "15(QubitFreqCalibration,r2<0.9)",
        "16(XpiDetection2,r2>0.9,0.8>xpi>0.6)",
        "17(Other)",
    ]

    # 创建状态匹配器
    matcher = StateMatcher(state_rules)

    # 创建测试用例
    test_cases = [
        # 应匹配状态 1
        # ExperimentResult(
        #     unit="Q1", experiment="CavityFreqSpectrum", quality={"r2": 0.95}
        # ),
        # # 应匹配状态 5  
        # ExperimentResult(
        #     unit="Q2", experiment="QubitSpectrum", quality={"snr": 3.5}
        # ),
        # # 应匹配状态 11
        # ExperimentResult(
        #     unit="Q3",
        #     experiment="XpiDetection1",
        #     quality={"r2": 0.95},
        #     analysis_result={"Xpi": {"value": 0.75}},
        # ),
        # # 应匹配状态 17
        # ExperimentResult(unit="Q4", experiment="UnknownExperiment"),
        # # 应匹配状态 0
        # None,
        ExperimentResult(
            unit="Q1", experiment="CavityFreqSpectrum"
        ),
    ]

    # 测试匹配结果
    for i, result in enumerate(test_cases):
        state = matcher.match_state(result)
        print(f"测试用例 {i + 1} ({result.experiment if result else None}): 匹配到状态 {state}")
