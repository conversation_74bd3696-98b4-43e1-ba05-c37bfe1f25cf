# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/11
# __author:       <PERSON><PERSON><PERSON>, <PERSON><PERSON>

from abc import ABC
from collections import defaultdict
from typing import Dict, List, Optional

from prettytable import PrettyTable

from app.automation.train.learning.state_parse import StateMatcher
from app.automation.train.structure import Action, ActionTypeEnum, ExperimentResult


class BaseStrategy(ABC):
    actions = [
        Action(
            type=ActionTypeEnum.SUC,
        ),
        Action(
            type=ActionTypeEnum.FAIL,
        ),
    ]
    states = [
        "0(Init)",
        "1(Other)",
    ]
    state_to_action = {0: [1], 1: [1]}

    def __init__(self):
        self.n_states = len(self.states)
        self.n_actions = len(self.actions)
        self.goal = len(self.states) - 1
        self.pre_state = {}
        self.cur_state = {}
        self.cur_rewards = defaultdict(int)
        self.cur_dones = {}
        self.cur_actions = {}
        self.matcher = StateMatcher(self.states)

    def reset(self, physical_units: List[str]):
        self.pre_state = {unit: 0 for unit in physical_units}

    def step(self, new_states: Dict[str, int]):
        self.cur_state = new_states

        for unit, state in new_states.items():
            if state == self.goal:
                self.cur_rewards[unit] += 10
                self.cur_dones[unit] = True
            else:
                self.cur_rewards[unit] -= 1
                self.cur_dones[unit] = False

    def state_from_experiment_result(self, result: Optional[ExperimentResult] = None):
        return self.matcher.match_state(result)

    @classmethod
    def overview(cls):
        """Generates and displays a state-action overview table.
        
        Creates a formatted table showing:
        - State IDs (SID)
        - State descriptions
        - Associated action IDs (State to Action)
        - Action IDs (AID)
        - Action descriptions
        
        Additionally saves the table to 'table.txt' in the current directory.
        
        Returns:
            PrettyTable: Formatted table object
        """
        # Create table with headers
        table = PrettyTable()
        table.field_names = ["SID", "State", "State to Action", "AID", "Action"]
        table.title = "State-Action Mapping Overview"
        
        # Get counts of states and actions
        n_state = len(cls.states)
        n_action = len(cls.actions)
        
        # Populate table rows
        for i in range(max(n_state, n_action)):
            state = cls.states[i] if i < n_state else ""
            action = cls.actions[i] if i < n_action else ""
            link = cls.state_to_action[i] if i < n_state else []
            table.add_row([i, state, link, i, action])

        return table
