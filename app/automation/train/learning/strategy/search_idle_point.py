# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/11
# __author:       <PERSON><PERSON><PERSON>, <PERSON><PERSON>

from app.automation.train.learning.base_strategy import BaseStrategy
from app.automation.train.structure import (
    Action,
    ActionTypeEnum,
    ActionUpdateTypeEnum,
)


class SearchIdlePointStrategy(BaseStrategy):
    actions = [
        Action(experiment="CavityFreqSpectrum", type=ActionTypeEnum.NEXT),
        Action(experiment="QubitSpectrum", type=ActionTypeEnum.NEXT),
        Action(
            experiment="CavityFreqSpectrum",
            type=ActionTypeEnum.OPTIONS,
            mode=ActionUpdateTypeEnum.ADD,
            key="readout_power",
            value=1,
            limit=[-40, 10]
        ),
        Action(
            experiment="CavityFreqSpectrum",
            type=ActionTypeEnum.OPTIONS,
            mode=ActionUpdateTypeEnum.ADD,
            key="scope",
            value=1,
            limit=[1, 100]
        ),
        Action(
            experiment="CavityFreqSpectrum",
            type=ActionTypeEnum.OPTIONS,
            mode=ActionUpdateTypeEnum.ADD,
            key="readout_power",
            value=-1,
            limit=[-40, 10]
        ),
        Action(
            experiment="RabiScanWidth",
            type=ActionTypeEnum.NEXT,
        ),
        Action(
            experiment="QubitSpectrum",
            type=ActionTypeEnum.OPTIONS,
            mode=ActionUpdateTypeEnum.ADD,
            key="drive_power",
            value=-1,
            limit=[-40, 10]
        ),
        Action(
            experiment="QubitSpectrum",
            type=ActionTypeEnum.OPTIONS,
            mode=ActionUpdateTypeEnum.ADD,
            key="drive_power",
            value=1,
            limit=[-40, 10]
        ),
        Action(
            experiment="XpiDetection1",
            type=ActionTypeEnum.NEXT,
        ),
        Action(
            experiment="QubitFreqCalibration",
            type=ActionTypeEnum.NEXT,
        ),
        Action(
            experiment="XpiDetection1",
            type=ActionTypeEnum.OPTIONS,
            mode=ActionUpdateTypeEnum.ADD,
            key="drive_power",
            value=1,
            limit=[-40, 10]
        ),
        Action(
            experiment="XpiDetection1",
            type=ActionTypeEnum.OPTIONS,
            mode=ActionUpdateTypeEnum.ADD,
            key="drive_power",
            value=-1,
            limit=[-40, 10]
        ),
        Action(
            experiment="QubitFreqCalibration",
            type=ActionTypeEnum.OPTIONS,
            mode=ActionUpdateTypeEnum.ADD,
            key="fringes",
            value=[1, -1],
            limit=[-100, 100]
        ),
        Action(
            experiment="QubitFreqCalibration",
            type=ActionTypeEnum.OPTIONS,
            mode=ActionUpdateTypeEnum.ADD,
            key="fringes",
            value=[-1, 1],
            limit=[-100, 100]
        ),
        Action(
            experiment="XpiDetection2",
            type=ActionTypeEnum.NEXT,
        ),
        Action(
            type=ActionTypeEnum.SUC,
        ),
        Action(
            type=ActionTypeEnum.FAIL,
        ),
    ]

    states = [
        "0(Init)",
        "1(CavityFreqSpectrum,r2>=0.9)",
        "2(CavityFreqSpectrum,0.7<r2<0.9)",
        "3(CavityFreqSpectrum,r2<=0.7)",
        "4(QubitSpectrum,snr>=4)",
        "5(QubitSpectrum,2<snr<4)",
        "6(QubitSpectrum,snr<=2)",
        "7(RabiScanWidth,r2>=0.9)",
        "8(RabiScanWidth,r2<0.9)",
        "9(XpiDetection1,r2>0.9,0.6>=xpi)",
        "10(XpiDetection1,r2>0.9,xpi>=0.8)",
        "11(XpiDetection1,r2>0.9,0.8>xpi>0.6)",
        "12(XpiDetection1,r2<0.9)",
        "13(QubitFreqCalibration,r2>0.9,gap<=5)",
        "14(QubitFreqCalibration,r2>0.9,gap>5)",
        "15(QubitFreqCalibration,r2<0.9)",
        "16(XpiDetection2,r2>0.9,0.8>xpi>0.6)",
        "17(Other)",
    ]

    state_to_action = {
        0: [0],
        1: [1],
        2: [2, 3, 4],
        3: [2, 3, 4],
        4: [5],
        5: [6, 7],
        6: [6, 7],
        7: [8],
        8: [16],
        9: [10],
        10: [11],
        11: [9],
        12: [16],
        13: [14],
        14: [12, 13],
        15: [12, 13],
        16: [15],
        17: [16],
    }


if __name__ == "__main__":
    print(SearchIdlePointStrategy.overview())
