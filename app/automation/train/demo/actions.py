"""反馈策略

Args:
    unit: 工作单元
    experiment: 实验名称
    type: 策略类型，详见 ActionTypeEnum
    key: 策略中需要修改的键
    value: 策略中需要修改的值
"""


# 执行默认配置的的'CavityFreqSpectrum'实验
# (CavityFreqSpectrum,routine)
class Action0:
    def __init__(self, exp_data):
        self.unit = exp_data.get('unit')
        self.experiment = 'CavityFreqSpectrum'
        self.type = "next"  # unit_map/default/success/fail/options

    def __call__(self):
        return self


# 执行默认配置的'QubitSpectrum'实验
# (QubitSpectrum,routine)
class Action1:
    def __init__(self, exp_data):
        self.unit = exp_data.get('unit')
        self.experiment = 'QubitSpectrum'
        self.type = "next"

    def __call__(self):
        return self


# 增大读取功率后执行'CavityFreqSpectrum'实验
# (CavityFreqSpectrum, probe_power +1 )
class Action2:
    def __init__(self, exp_data):
        self.unit = exp_data.get('unit')
        self.experiment = 'CavityFreqSpectrum'
        self.type = 'unit'
        self.key = 'probe_power'
        self.value = exp_data.unit_map.get(exp_data.unit).get('probe_power') + 1

    def __call__(self):
        return self


# 增大频率扫描范围后执行'CavityFreqSpectrum'实验
# (CavityFreqSpectrum, scope +1)
class Action3:
    def __init__(self, exp_data):
        self.unit = exp_data.get('unit')
        self.experiment = 'CavityFreqSpectrum'
        self.type = 'options'
        self.key = 'scope'
        self.value = exp_data.options.get('scope')+1

    def __call__(self):
        return self


# 减小读取功率后执行'CavityFreqSpectrum'实验
# (CavityFreqSpectrum, probe_power -1 )
class Action4:
    def __init__(self, exp_data):
        self.unit = exp_data.get('unit')
        self.experiment = 'CavityFreqSpectrum'
        self.type = 'unit'
        self.key = 'probe_power'
        self.value = exp_data.unit_map.get(exp_data.unit).get('probe_power') - 1

    def __call__(self):
        return self


# 执行默认配置的'RabiScanWidth'实验
# (RabiScanWidth,routine)
class Action5:
    def __init__(self, exp_data):
        self.unit = exp_data.get('unit')
        self.experiment = 'RabiScanWidth'
        self.type = 'next'

    def __call__(self):
        return self


# 减小驱动功率后执行'QubitSpectrum'实验
# (QubitSpectrum,drive_power-1)
class Action6:
    def __init__(self, exp_data):
        self.unit = exp_data.get('unit')
        self.experiment = 'QubitSpectrum'
        self.type = 'options'
        self.key = 'drive_power'
        self.value = exp_data.options.get('drive_power') - 1

    def __call__(self):
        return self


# 增大驱动功率后执行'QubitSpectrum'实验
# (QubitSpectrum,drive_power+1)
class Action7:
    def __init__(self, exp_data):
        self.unit = exp_data.get('unit')
        self.experiment = 'QubitSpectrum'
        self.type = 'options'
        self.key = 'drive_power'
        self.value = exp_data.options.get('drive_power') + 1

    def __call__(self):
        return self


# 执行默认配置的'XpiDetection'实验
# (XpiDetection1,routine)
class Action8:
    def __init__(self, exp_data):
        self.unit = exp_data.get('unit')
        self.experiment = 'XpiDetection1'
        self.type = 'next'

    def __call__(self):
        return self


# 执行默认配置的'QubitFreqCalibration'实验
# (QubitFreqCalibration,routine)
class Action9:
    def __init__(self, exp_data):
        self.unit = exp_data.get('unit')
        self.experiment = 'QubitFreqCalibration'
        self.type = 'next'

    def __call__(self):
        return self


# 增大驱动功率后执行'XpiDetection'实验
# (XpiDetection1,drive_power+1)
class Action10:
    def __init__(self, exp_data):
        self.unit = exp_data.get('unit')
        self.experiment = 'XpiDetection1'
        self.type = 'options'
        self.key = 'drive_power'
        self.value = exp_data.options.get('drive_power') + 1

    def __call__(self):
        return self


# 增大fringe的绝对值后执行'QubitFreqCalibration'实验
# (QubitFreqCalibration,fringes+)
class Action11:
    def __init__(self, exp_data):
        self.unit = exp_data.get('unit')
        self.experiment = 'QubitFreqCalibration'
        self.type = 'options'
        self.key = 'fringes'
        [a,b] = exp_data.options.get('fringes')
        self.value = [a+1,b-1]

    def __call__(self):
        return self


# 减小fringe的绝对值后执行'QubitFreqCalibration'实验
# (QubitFreqCalibration,fringes-)
class Action12:
    def __init__(self, exp_data):
        self.unit = exp_data.get('unit')
        self.experiment = 'QubitFreqCalibration'
        self.type = 'options'
        self.key = 'fringes'
        [a,b] = exp_data.options.get('fringes')
        self.value = [a-1,b+1]

    def __call__(self):
        return self

# 执行XpiDetection2
# (XpiDetection2)
class Action13:
    def __init__(self, exp_data):
        self.unit = exp_data.get('unit')
        self.experiment = 'XpiDetection2'
        self.type ='next'

    def __call__(self):
        return self

# 成功并退出
# (suc end)
class Action14:
    def __init__(self, exp_data):
        self.unit = exp_data.get('unit')
        self.experiment = None
        self.type = 'suc'

    def __call__(self):
        return self

# 失败并退出
# (fail end)
# class Action15:
#     def __init__(self, exp_data):
#         self.unit = exp_data.get('unit')
#         self.experiment = None
#         self.type = 'fail'
#
#     def __call__(self):
#         return self