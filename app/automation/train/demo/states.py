from enum import Enum
from dataclasses import dataclass, field
from typing import Any, List

from pyQCat.structures import ExperimentData
from pyQCat.experiments.base_experiment import BaseExperiment

state_dict={
'0(None)':0,
'1(CavityFreqSpectrum, R2>0.9, Sim=STATE1)':1,
'2(CavityFreqSpectrum, 0.7<R2<0.9, Sim=STATE2)':2,
'3(CavityFreqSpectrum, R2<0.7, Sim=STATE3)':3,
'5(QubitSpectrum,SNR>4)':4,
'6(QubitSpectrum,2<SNR<4)':5,
'7(QubitSpectrum,SNR<2)':6,
'8(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,R2>0.9)':7,
'9(<PERSON><PERSON><PERSON><PERSON><PERSON>,R2<0.9)':8,
'10(XpiDetection1,R2>0.9,peak<1)':9,
'11(XpiDetection1,R2>0.9,peak=1)':10,
'12(XpiDetection1,R2<0.9)':11,
'13(QubitFreqCalibration,R2>0.9,gap<5)':12,
'14(QubitFreqCalibration,R2>0.9,gap>5)':13,
'15(QubitFreqCalibration,R2<0.9)':14,
#'16(XpiDetection2,R2>0.9,peak<1)':16
}


@dataclass
class ExperimentResult:
    def __init__(self, exp_data):
        self.exp_data = exp_data
        self.unit = exp_data.get('unit')
        self.experiment = exp_data.get('experiment')
        if self.experiment == 'CavityFreqSpectrum':
            self.states_for_CavityFreqSpectrum()

        elif self.experiment == 'QubitSpectrum':
            self.states_for_QubitSpectrum()

        elif self.experiment == 'RabiScanWidth':
            self.states_for_RabiScanWidth()

        elif self.experiment == 'XpiDetection1':
            self.states_for_XpiDetection1()

        elif self.experiment == 'QubitFreqCalibration':
            self.states_for_QubitFreqCalibration()

        elif self.experiment == None or self.experiment == 'XpiDetection2':
            self.suc_end()

        # elif self.experiment =='changeidlepoint':
        #     self.states_for_changeidlepoint()



    def states_for_CavityFreqSpectrum(self):
        self.r2 = self.exp_data.quality.get('r2')
        if self.r2>0.9:
            self.label = 1
        if self.r2>=0.7 and self.r2<=0.9:
            self.label = 2
        if self.r2<=0.7:
            self.label = 3

    def states_for_QubitSpectrum(self):
        self.snr = self.exp_data.quality.get('snr')
        if self.snr>4:
            self.label = 4
        if self.snr>=2 and self.snr<=4:
            self.label = 5
        if self.snr<=2:
            self.label = 6

    def states_for_RabiScanWidth(self):
        self.r2 = self.exp_data.quality.get('r2')
        if self.r2 > 0.9:
            self.label = 7
        if self.r2 <= 0.9:
            self.label = 8
    def states_for_QubitFreqCalibration(self):
        self.gap = self.exp_data.analysis_result.get('minimum_f01_gap').get('value')
        self.r2 = min([self.exp_data.child_result[0].quality.get('r2'),self.exp_data.child_result[1].quality.get('r2')])
        # 提取形式不对
        if self.r2 >0.9 and self.gap < 5:
            self.label = 12
        if self.r2 >0.9 and self.gap >= 5:
            self.label = 13
        if self.r2 <= 0.9:
            self.label =14

    def states_for_XpiDetection1(self):
        self.r2 = self.exp_data.quality.get('r2')
        self.peak= self.exp_data.analysis_result.get('Xpi').get('value')
        if self.r2 >0.9 and self.peak<1:
            self.label = 9
        if self.r2 >0.9 and self.peak==1:
            self.label = 10
        if self.r2 <= 0.9:
            self.label =11

    # def states_for_changeidlepoint(self):
    #     self.label = 0

    def suc_end(self):
        self.label = 15

    # def fail_end(self):
    #     # 判断条件与step有关

    def __call__(self):
        return self.label
