import numpy as np
import matplotlib.pyplot as plt
import actions
import states


action_dict={
'(CavityFreqSpectrum,routine)':0,
'(QubitSpectrum,routine)':1,
'(CavityFreqSpectrum, probe_power +1)':2,
'(CavityFreqSpectrum, scope +1)':3,
'(CavityFreqSpectrum, probe_power -1)':4,
'(RabiScanWidth,routine)':5,
'(QubitSpectrum,drive_power-1)':6,
'(QubitSpectrum,drive_power+1)':7,
'(XpiDetection1,routine)':8,
'(QubitFreqCalibration,routine)':9,
'(XpiDetection1,drive_power+1)':10,
'(QubitFreqCalibration,fringes+)':11,
'(QubitFreqCalibration,fringes-)':12,
'(XpiDetection2)':13,
'(suc end)':14,
#'(fail end)':15
}


state_dict={
'0(None)':0,
'1(CavityFreqSpectrum, R2>0.9)':1,
'2(CavityFreqSpectrum, 0.7<R2<0.9)':2,
'3(CavityFreqSpectrum, R2<0.7)':3,
'4(QubitSpectrum,SNR>4)':4,
'5(QubitSpectrum,2<SNR<4)':5,
'6(QubitSpectrum,SNR<2)':6,
'7(RabiScanWidth,R2>0.9)':7,
'8(RabiScanWidth,R2<0.9)':8,
'9(XpiDetection1,R2>0.9,peak<1)':9,
'10(XpiDetection1,R2>0.9,peak=1)':10,
'11(XpiDetection1,R2<0.9)':11,
'12(QubitFreqCalibration,R2>0.9,gap<5)':12,
'13(QubitFreqCalibration,R2>0.9,gap>5)':13,
'14(QubitFreqCalibration,R2<0.9)':14,
'15(XpiDetection2,R2>0.9,peak<1)':15
}

state_to_action={
    0: [0],
    1: [1],
    2: [2,3,4],
    3: [2,3,4],
    4: [5],
    5: [6,7],
    6: [6,7],
    7: [8],
    8: [13],
    9: [9],
    10: [10],
    11: [],
    12: [13],
    13: [11,12],
    14: [11,12],
    15: [14]
}


class BatchSearchIdlePoint_Strategy:
    def __init__(self):
        self.n_states = len(state_dict.keys())
        self.n_actions =len(action_dict.keys())
        self.goal = state_dict.values()[-1]

    def reset(self,data_from_last_batch):
        self.state = np.zeros(len(data_from_last_batch))
        return self.state

    def step(self, new_states):
        """
        批量处理多个 new_states，返回 (new_states, rewards, dones)

        Args:
            new_states (np.ndarray or list): 形状为 (batch_size,) 的新状态数组

        Returns:
            new_states (np.ndarray): 更新后的状态（和输入相同）
            rewards (np.ndarray): 每个状态对应的奖励
            dones (np.ndarray): 每个状态是否终止（bool）
        """
        new_states = np.array(new_states)  # 确保输入是 numpy 数组
        batch_size = len(new_states)

        # 初始化 rewards 和 dones
        rewards = np.full(batch_size, -1, dtype=np.float32)
        dones = np.zeros(batch_size, dtype=bool)

        # 检查哪些 new_states 达到目标
        reached_goal = (new_states == self.goal)
        rewards[reached_goal] = 10
        dones[reached_goal] = True

        return  rewards, dones


class QLearningAgent:
    def __init__(self, n_states, n_actions,state_to_action):
        self.n_states = n_states
        self.n_actions = n_actions
        self.state_to_action = state_to_action
        self.q_table = np.zeros((n_states, n_actions))
        self.alpha = 0.1  # 学习率
        self.gamma = 0.9  # 折扣因子
        self.epsilon = 0.1  # 探索率

    def choose_action(self, state_list):
        alist = []
        for state in state_list:
            if np.random.random() < self.epsilon:
                alist.append(np.random.choice(self.state_to_action[state]))  # 探索
            else:
                alist.append(np.argmax(self.q_table[state]))  # 利用
        return alist

    def learn(self, states, actions, rewards, next_states, dones):
        """
        批量更新 Q-table
        Args:
            states (np.ndarray): 形状为 (batch_size,) 的状态数组
            actions (np.ndarray): 形状为 (batch_size,) 的动作数组
            rewards (np.ndarray): 形状为 (batch_size,) 的奖励数组
            next_states (np.ndarray): 形状为 (batch_size,) 的下一状态数组
            dones (np.ndarray): 形状为 (batch_size,) 的终止标志数组（bool）
        """
        batch_size = len(states)
        current_q = self.q_table[states, actions]  # 当前 Q 值 (batch_size,)

        # 计算目标 Q 值 (batch_size,)
        max_next_q = np.max(self.q_table[next_states], axis=1)  # 下一状态的最大 Q 值
        target_q = rewards + self.gamma * max_next_q * (~dones)  # 如果 done=True，则忽略下一状态

        # 批量更新 Q-table
        self.q_table[states, actions] += self.alpha * (target_q - current_q)


def ask_actions(state, data):
    choose_action = agent.choose_action(state)
    action_list = [eval(f"actions.Action{num}")(data[i]) for i, num in enumerate(choose_action)]
    return action_list,choose_action


data_from_last_batch = ...
env = BatchSearchIdlePoint_Strategy()
agent = QLearningAgent(env.n_states, env.n_actions,state_to_action)
state = env.reset(data_from_last_batch)
data = data_from_last_batch
# rewards = np.zeros(len(data))
# steps = np.zeros(len(data))
step_count = 0
if step_count <= 50:
    action_for_exp,action_for_train = ask_actions(state, data)
    next_data = receive_results(action_for_exp)
    results = [states.ExperimentResult(data) for data in next_data]
    next_state = [result.label for result in results]
    reward, done = env.step(next_state)
    agent.learn(state, action_for_train, reward, next_state, done)
    state = next_state
    data = next_data
    step_count += 1
    # rewards.append(total_reward)
    # steps.append(step_count)

# # 可视化结果
# plt.figure(figsize=(12, 5))
# plt.subplot(1, 2, 1)
# plt.plot(rewards)
# plt.title('Rewards per Episode')
# plt.xlabel('Episode')
# plt.ylabel('Total Reward')
#
# plt.subplot(1, 2, 2)
# plt.plot(steps)
# plt.title('Steps per Episode')
# plt.xlabel('Episode')
# plt.ylabel('Steps')
#
# plt.tight_layout()
# plt.show()
#
print("Learned Q-table:")
print(agent.q_table)

