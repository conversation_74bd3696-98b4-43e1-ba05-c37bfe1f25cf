# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/07
# __author:       <PERSON><PERSON><PERSON>

import json
from collections import defaultdict
from pathlib import Path
from typing import List

import numpy as np

from app.automation.train.experiment.auto_experiment import AutoExperiment
from pyQCat.executor.batch import import_idle_point
from pyQCat.log import pyqlog
from pyQCat.tools import qarange
from app.automation.train.learning.strategy.search_idle_point import SearchIdlePointStrategy


class AutoSearchIdlePoint(AutoExperiment):
    strategy = SearchIdlePointStrategy

    @classmethod
    def _default_experiment_options(cls):
        options = super()._default_experiment_options()
        options.qs_flows = [
            "CavityFreqSpectrum",
            "QubitSpectrum",
        ]
        options.idle_flows = [
            "XpiDetection",
            "QubitFreqCalibration",
            "XpiDetection",
        ]
        options.idle_step = 0.01
        options.idle_points = 30
        options.pass_to_db = False
        options.is_qs_divide = False
        return options

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.idle_point_map = {}
        options.read_point_map = {}
        options.max_idle_length = 0
        options.max_read_length = 0
        options.qs_freq_map = {}
        options.idle_state_record = {
            "pass_units": defaultdict(list),
        }
        options.pass_unit_f01_map = {}
        options.pass_unit_osc_freq_map = {}
        options.init_readout_power_map = {}
        return options

    def _divide_idle_point(self, physical_units: List[str]):
        for unit in physical_units:
            qubit_obj = self.context_manager.chip_data.cache_qubit.get(unit)

            # init drive freq and baseband freq
            qubit_obj.drive_freq = 4500
            # qubit_obj.XYwave.baseband_freq = 850
            self.run_options.init_readout_power_map[unit] = qubit_obj.probe_power

            if qubit_obj.tunable is False:
                pyqlog.log("EXP", f"{unit} tunable false, idle point set 0")
                point_list = [0]
            else:
                max_point = qubit_obj.dc_max
                min_point = qubit_obj.dc_min

                if self.experiment_options.idle_points:
                    point_list = np.linspace(
                        max_point, min_point, self.experiment_options.idle_points
                    ).tolist()
                else:
                    step = abs(self.experiment_options.idle_step) or 0.01
                    if max_point > min_point:
                        step = -step
                    point_list = qarange(max_point, min_point, step)

            self.run_options.idle_point_map[unit] = point_list
            self.run_options.max_idle_length = max(
                len(point_list), self.run_options.max_idle_length
            )

    def _change_idle_point(self, idle_index: int, units: List[str]):
        ok_units = []
        for unit in units:
            point_list = self.run_options.idle_point_map.get(unit)
            if idle_index < len(point_list):
                idle_point = point_list[idle_index]
                ok_units.append(unit)
                qubit = self.context_manager.chip_data.cache_qubit.get(unit)
                qubit.drive_freq = 4500
                qubit.probe_power = self.run_options.init_readout_power_map[unit]
                # qubit.XYwave.baseband_freq = 850
                # qubit.inst.xy_gap = round(4500 - 850, 3)
                qubit.idle_point = idle_point - qubit.dc_max
                pyqlog.info(f"Change {unit} idle point {qubit.idle_point}")

        # bugfix: Switching idle points requires clearing pass_unit_f01_map
        self.run_options.pass_unit_f01_map.clear()
        self.run_options.pass_unit_osc_freq_map.clear()

        return ok_units

    def _run_batch(self):
        all_units = self.experiment_options.physical_units

        self.context_manager.global_options.max_point_unit = all_units
        self._divide_idle_point(all_units)

        # sweep idle point
        for idle_idx in range(self.run_options.max_idle_length):
            # process_bar = f"{idle_idx + 1}/{self.run_options.max_idle_length}"
            
            # reset parameter manager
            self.params_manager = None
            self._init_params_manager()

            # set idle point
            qs_para_units = self._change_idle_point(idle_idx, all_units)
            self.run_options.agent._count = 0
            self.run_options.unit_exp_map.clear()
            
            # run qs flows: CavityFreqSpectrum | QubitSpectrum
            if qs_para_units:
                all_pass_units, _ = self._iterate(qs_para_units)
                self._record_unit_status(all_pass_units)
                for unit in all_pass_units:
                    all_units.remove(unit)

    def _record_unit_status(self, pass_units):
        for unit in pass_units:
            qubit = self.context_manager.chip_data.get_physical_unit(unit)
            self.run_options.idle_state_record["pass_units"][unit].append(
                qubit.idle_point
            )
            with open(
                str(
                    Path(
                        Path(self.run_options.record_path).parent,
                        f"{qubit.name}_{qubit.idle_point}.json",
                    )
                ),
                mode="w",
                encoding="utf-8",
            ) as fp:
                data = qubit.to_dict()
                json.dump(data, fp, indent=4, ensure_ascii=False)

    def _record_idle_state_record(self):
        with open(
            str(Path(Path(self.run_options.record_path).parent, "status.json")),
            mode="w",
            encoding="utf-8",
        ) as fp:
            json.dump(
                self.run_options.idle_state_record, fp, indent=4, ensure_ascii=False
            )

    def _batch_down(self):
        pass_units = list(self.run_options.idle_state_record.get("pass_units").keys())
        self.bind_pass_units(pass_units)
        self.run_options.idle_state_record["fail_units"] = (
            self.record_meta.execute_meta.result.fail_units
        )
        self._record_idle_state_record()

        if self.experiment_options.pass_to_db is True:
            import_idle_point(
                self.backend, str(Path(self.run_options.record_path).parent)
            )

        super()._batch_down()
