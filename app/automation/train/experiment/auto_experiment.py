# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/09
# __author:       <PERSON><PERSON><PERSON>


import asyncio
import re
from collections import defaultdict
from typing import Dict, List, Union

from app.automation.train.learning.agent import QLearningAgent
from app.automation.train.learning.base_strategy import BaseStrategy
from app.automation.train.structure import (
    Action,
    ActionTypeEnum,
    ActionUpdateTypeEnum,
    ExperimentResult,
    Input,
)
from pyQCat.experiments.batch_experiment import (
    BaseExperiment,
    BatchExperiment,
    ParallelExperiment,
    check_and_start_merge_service,
    check_process_broken_state,
)
from pyQCat.log import pyqlog
from pyQCat.qubit import NAME_PATTERN
from pyQCat.tools.utilities import unit_group_formation_table


class AutoExperiment(BatchExperiment):
    strategy = BaseStrategy

    @classmethod
    def _default_run_options(cls):
        options = super()._default_run_options()
        options.unit_exp_map = {}
        options.agent = None
        return options

    @property
    def agent(self) -> QLearningAgent:
        return self.run_options.agent

    def _check_options(self):
        super()._check_options()
        self.run_options.agent = QLearningAgent(self.strategy())
        table_info = self.run_options.agent.strategy.overview()
        pyqlog.info(
            f"Start {self.run_options.agent.strategy.__class__.__name__} Learning: \n{table_info}"
        )
        self.file.save_text(str(table_info), "state_to_action")

    def _iterate(self, physical_units: List[str]):
        actions = self.agent.ask_actions(Input(physical_units=physical_units))
        all_pass_units = []
        all_fail_units = []
        while True:
            results, pass_units, fail_units = self._run_actions(actions)
            if not results:
                break
            all_pass_units.extend(pass_units)
            all_fail_units.extend(fail_units)
            actions = self.agent.ask_actions(Input(data=results))
        return all_pass_units, all_fail_units

    def _run_actions(self, actions: Dict[str, Action]):
        self.file.save_data(self.run_options.agent.q_table.T, name="table")
        pass_units = []
        fail_units = []
        experiment_results = []
        exp_actions = []
        for action in actions.values():
            state = self._parse_action(action)
            if state == 0:
                fail_units.append(action.unit)
                continue
            elif state == 1:
                pass_units.append(action.unit)
                continue
            elif action.experiment and action.unit:
                exp_actions.append(action)

        experiment_results, cur_fail_units = self._run_exp_from_actions(exp_actions)
        fail_units.extend(cur_fail_units)

        return (
            experiment_results,
            pass_units,
            fail_units,
        )

    def _parse_action(self, action: Action):
        pyqlog.info(f"receive action {action}")
        exp = self._get_history_experiment(action.experiment, action.unit)

        if action.type == ActionTypeEnum.FAIL:
            pyqlog.warning(f"{action} fail ...")
            return 0
        elif action.type == ActionTypeEnum.SUC:
            if exp:
                self._refresh_context_from_analysis(exp.analysis)
            return 1
        elif action.type == ActionTypeEnum.UNIT:
            # todo
            # unit_obj = self.backend.chip_data.get_physical_unit(action.unit)
            pyqlog.warning("no suggest direct update unit parameters")
        elif action.type in [ActionTypeEnum.OPTIONS, ActionTypeEnum.SEARCH]:
            if action.mode == ActionUpdateTypeEnum.ADD and exp:
                pre_value = exp.experiment_options.get(action.key)
                if isinstance(pre_value, list):
                    new_value = [
                        round(pre_value[i] + action.value[i], 3) for i in range(len(pre_value))
                    ]
                    if not action.validator(new_value):
                        new_value = pre_value
                else:
                    new_value = round(pre_value + action.value, 3)
                    if not action.validator(new_value):
                        new_value = pre_value
                pyqlog.info(
                    f"Update {action.experiment} {action.unit} `{action.key}` {pre_value} to {new_value}"
                )
                self.change_parallel_exec_exp_options(
                    unit=action.unit,
                    exp_name=action.experiment,
                    options={action.key: new_value},
                )
            elif action.mode == ActionUpdateTypeEnum.SET and action.key and exp:
                pyqlog.info(
                    f"Update {action.experiment} {action.unit} `{action.key}` to {action.value}"
                )
                self.change_parallel_exec_exp_options(
                    unit=action.unit,
                    exp_name=action.experiment,
                    options={action.key: action.value},
                )
            elif action.mode == ActionUpdateTypeEnum.MUL and exp:
                pre_value = exp.experiment_options.get(action.key)
                if isinstance(pre_value, list):
                    new_value = [
                        pre_value[i] * action.value[i] for i in range(len(pre_value))
                    ]
                else:
                    new_value = pre_value * action.value
                pyqlog.info(
                    f"Update {action.experiment} {action.unit} `{action.key}` {pre_value} to {new_value}"
                )
                self.change_parallel_exec_exp_options(
                    unit=action.unit,
                    exp_name=action.experiment,
                    options={action.key: new_value},
                )
            else:
                pyqlog.warning(f"no support action mode {action.mode} | {action}")
        elif action.type == ActionTypeEnum.NEXT:
            exp = self._get_history_experiment(action.key, action.unit)
            if exp:
                self._refresh_context_from_analysis(exp.analysis)
        else:
            raise ValueError(f"No support {action} | {action.type}")
        return 2

    def _run_exp_from_actions(self, actions: List[Action]):
        results, fail_units = [], []
        action_map = defaultdict(list)
        for action in actions:
            action_map[action.experiment].append(action)

        for exp_name, exp_actions in action_map.items():
            physical_units = [action.unit for action in exp_actions]
            if exp_name == "CavityFreqSpectrum":
                group_map = self._parallel_for_cavity(
                    [action.unit for action in exp_actions]
                )
                # group_map = {index: [action.unit] for index, action in enumerate(exp_actions)}
            elif re.match(NAME_PATTERN.qubit, physical_units[0]):
                group_map = self.parallel_allocator_for_qc(physical_units, check_readout_power=True)
            elif re.match(NAME_PATTERN.coupler, physical_units[0]):
                group_map = self.parallel_allocator_for_cc(physical_units)
            else:
                group_map = self.parallel_allocator_for_cgc(physical_units)
            for cur_physical_units in group_map.values():
                cur_results, cur_fail_units = self._run_simple_exp(
                    exp_name, cur_physical_units
                )
                results.extend(cur_results)
                fail_units.extend(cur_fail_units)

        return results, fail_units

    def _run_simple_exp(
        self,
        exp_name: str,
        physical_units: Union[List[str], str],
    ):
        if exp_name not in self.run_options.unit_exp_map:
            self.run_options.unit_exp_map[exp_name] = {}

        results = []
        fail_units = []

        if isinstance(physical_units, str):
            physical_units = [physical_units]

        exp, parallel_units, _ = self.params_manager.get(exp_name).to_exp(
            self.context_manager,
            physical_unit=physical_units,
            use_simulator=self.experiment_options.use_simulator,
        )

        if isinstance(exp, ParallelExperiment):
            exp.topology = self.backend.chip_data.topology
            exp.allocation_options = self.backend.system.parallel_divide
            check_and_start_merge_service(self.backend.context_manager.config)
            for index, child_exp in enumerate(exp.experiments):
                unit = parallel_units[index]
                self._config_file_path(unit, child_exp)
        elif isinstance(exp, BaseExperiment):
            unit = (
                parallel_units[0]
                if isinstance(parallel_units, list)
                else parallel_units
            )
            self._config_file_path(unit, exp)
        else:
            error, exp = exp, None
            pyqlog.error(f"{exp_name} error: {error}")
            fail_units.extend(physical_units)

        if exp:
            exp.record_meta.execute_meta.batch_id = self.record_id
            asyncio.run(exp.run_experiment())

            # Asynchronous process pool broken detection, and re pull the process pool
            if check_process_broken_state() is True:
                return self._run_simple_exp(exp_name, physical_units)

            if isinstance(exp, ParallelExperiment):
                for ce in exp.experiments:
                    unit = ce.record_meta.execute_meta.physical_units
                    if ce.status.is_done():
                        self.run_options.unit_exp_map[exp_name][unit] = ce
                        results.append(ExperimentResult.from_experiment(ce))
                    else:
                        fail_units.append(unit)
            elif exp.status.is_done():
                unit = exp.record_meta.execute_meta.physical_units
                self.run_options.unit_exp_map[exp_name][unit] = exp
                results.append(ExperimentResult.from_experiment(exp))
            else:
                fail_units.append(exp.record_meta.execute_meta.physical_units)

        return results, fail_units

    def _get_history_experiment(self, experiment: str, unit: str):
        if experiment and unit:
            if experiment in self.run_options.unit_exp_map:
                unit_exp_map = self.run_options.unit_exp_map[experiment]
                if unit in unit_exp_map:
                    return unit_exp_map[unit]

    def _parallel_for_cavity(self, physical_units: List[str]):
        readout_power_unit_map = defaultdict(list)
        readout_power_bus_map = defaultdict(set)
        for unit in physical_units:
            qubit = self.backend.chip_data.cache_qubit[unit]
            exp_options, _ = self.params_manager.get(
                "CavityFreqSpectrum"
            )._extract_unit_options(unit)
            readout_power = exp_options.get("readout_power")
            scope = exp_options.get("scope")
            if not readout_power:
                readout_power = qubit.probe_power
            if readout_power:
                key = (float(readout_power), scope)
                readout_power_unit_map[key].append(unit)
                readout_power_bus_map[key].add(qubit.inst.bus)

        group_map = {}
        index = 1
        for key, units in readout_power_unit_map.items():
            _, scope = key
            bus_set = readout_power_bus_map[key]
            if not group_map:
                group_map[index] = (units, bus_set, scope)
            else:
                for gn in list(group_map.keys()):
                    if (
                        group_map[gn][1].isdisjoint(bus_set)
                        and group_map[gn][2] == scope
                    ):
                        group_map[gn][0].extend(units)
                        group_map[gn][1].update(bus_set)
                        break
                else:
                    index += 1
                    group_map[index] = (units, bus_set, scope)

        result_str = unit_group_formation_table(group_map)
        pyqlog.info(f"CavityFreqSpectrum allocation overview:\n {result_str}")

        new_group_map = {}
        index = 1
        for v in group_map.values():
            for g in self.parallel_allocator_for_qc(v[0], mode="empty").values():
                new_group_map[index] = g
                index += 1

        return new_group_map
