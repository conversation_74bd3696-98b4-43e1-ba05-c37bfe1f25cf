# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/07/09
# __author:       <PERSON><PERSON><PERSON>

from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional

from pyQCat.experiments.base_experiment import BaseExperiment


class ActionTypeEnum(str, Enum):
    """Enumeration of action types for experiment feedback strategies.
    
    Defines possible actions that can be taken based on experiment results:
    
    Attributes:
        OPTIONS: Requires modifying experiment options and re-running
        UNIT: Requires modifying qubit parameters and re-running
        FAIL: Indicates qubit iteration failed with no valid strategy
        SUC: Indicates qubit iteration succeeded successfully
        NEXT: Proceeds to next experiment in workflow
        SEARCH: Initiates a parameter search operation
    """
    OPTIONS = "options"
    UNIT = "unit"
    FAIL = "fail"
    SUC = "suc"
    NEXT = "next"
    SEARCH = "search"


class ActionUpdateTypeEnum(str, Enum):
    """Enumeration of update types for action value modifications.
    
    Specifies how values should be updated when applying actions:
    
    Attributes:
        ADD: Additive update (value = current + modification)
        MUL: Multiplicative update (value = current * modification)
        SET: Direct assignment (value = modification)
    """
    ADD = "add"
    MUL = "multiple"
    SET = "set"


@dataclass
class ExperimentResult:
    """Represents the result of a quantum experiment execution.

    Attributes:
        unit: Physical unit identifier (e.g., qubit name)
        experiment: Name of the executed experiment
        quality: Dictionary of quality metrics from analysis
        analysis_result: Dictionary of detailed analysis results
        child_result: List of child experiment results (if any)
    
    Properties provide convenient access to common metrics:
        r2: Quality metric for curve fitting (R-squared)
        snr: Signal-to-noise ratio measurement
        gap: Frequency gap measurement (QubitFreqCalibration only)
        xpi: X-pulse amplitude (XpiDetection experiments only)
    """
    unit: str
    experiment: str
    quality: Dict[str, Any] = field(default_factory=dict)
    analysis_result: Dict[str, Any] = field(default_factory=dict)
    child_result: List['ExperimentResult'] = field(default_factory=list)

    @property
    def r2(self) -> Optional[float]:
        """Retrieves the R-squared quality metric from experiment results.
        
        Returns:
            R-squared value if available, otherwise None
        """
        return self.quality.get("r2")

    @property
    def snr(self) -> Optional[float]:
        """Retrieves the signal-to-noise ratio from experiment results.
        
        Returns:
            SNR value if available, otherwise None
        """
        return self.quality.get("snr")
    
    @property
    def gap(self) -> Optional[float]:
        """Retrieves frequency gap measurement for QubitFreqCalibration.
        
        Returns:
            Frequency gap value if available and relevant, otherwise None
        """
        if self.experiment == "QubitFreqCalibration":
            if min_gap := self.analysis_result.get("minimum_f01_gap"):
                return min_gap.get("value")
        return None

    @property
    def xpi(self) -> Optional[float]:
        """Retrieves X-pulse amplitude for XpiDetection experiments.
        
        Returns:
            X-pi value if available and relevant, otherwise None
        """
        if "XpiDetection" in self.experiment:
            if xpi_val := self.analysis_result.get("Xpi"):
                return xpi_val.get("value")
        return None

    @classmethod
    def from_experiment(cls, exp: 'BaseExperiment') -> 'ExperimentResult':
        """Creates an ExperimentResult instance from a BaseExperiment object.
        
        Args:
            exp: Experiment object to extract results from
            
        Returns:
            Populated ExperimentResult instance
        """
        return cls(
            unit=exp.record_meta.execute_meta.physical_units,
            experiment=exp.record_meta.execute_meta.exp_class,
            quality=exp.record_meta.execute_meta.quality,
            analysis_result=exp.record_meta.execute_meta.analysis_result,
        )


@dataclass
class Input:
    """Container for workflow input data.
    
    Attributes:
        physical_units: List of physical units (qubits) to process
        data: Collection of experiment results (empty for initial execution)
    """
    physical_units: List[str] = field(default_factory=list)
    data: List[ExperimentResult] = field(default_factory=list)


@dataclass
class Action:
    """Represents a feedback strategy for quantum experiment iteration.

    Attributes:
        id: Unique identifier for the action
        unit: Physical unit (qubit) the action applies to
        experiment: Experiment name the action relates to
        type: Action category (see ActionTypeEnum)
        mode: Value modification method (see ActionUpdateTypeEnum)
        key: Parameter key to be modified
        value: Modification value to apply
        limit: Optional [min, max] range for value validation
        
    The string representation shows key action details for logging.
    The validator method checks if new values stay within defined limits.
    """
    id: int = 0
    unit: str = ""
    experiment: str = ""
    type: ActionTypeEnum = ActionTypeEnum.NEXT
    mode: ActionUpdateTypeEnum = ActionUpdateTypeEnum.SET
    key: str = ""
    value: Any = None
    limit: List[float] = field(default_factory=list)

    def __repr__(self) -> str:
        """Generates human-readable action representation.
        
        Returns:
            Formatted string containing key action properties
        """
        return (f"ID<{self.id}> | {self.experiment}<{self.unit}> | "
                f"Type({self.type.value}) | Mode({self.mode.value}) | "
                f"{self.key}<{self.value}>")

    def validator(self, new_v: Any) -> bool:
        """Validates new values against defined constraints.
        
        Args:
            new_v: Proposed new value to validate
            
        Returns:
            True if value is within limits, False otherwise
        """
        if not self.limit:
            return True
            
        try:
            l_bound, r_bound = self.limit
        except ValueError:
            return True
            
        if isinstance(new_v, list):
            return all(l_bound <= v <= r_bound for v in new_v)
        return l_bound <= new_v <= r_bound
