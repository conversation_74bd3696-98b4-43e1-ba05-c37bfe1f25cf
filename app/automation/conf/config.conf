[system]
sample = "Test-102bit-v2"
env_name = "S251"
point_label = "idle_point_iqmix"
invoker_addr = "tcp://***********:8088"
baseband_freq = 1050
m_baseband_freq = 1050
qaio_type = 72
save_type = "local"
local_root = "/home/<USER>/data/data"
log_path = "/home/<USER>/data/log"
config_path = "/home/<USER>/data/config"

[naga]
url = "tcp://***********:31101"
web = "http://***********:8030"
log_port = 27017

[minio]
s3_root = "***********:9000"
s3_access_key = "admin"
s3_secret_key = "bylz@2021"

[run]
use_simulator = True
simulator_delay = 0.0
async_win_size = 10
reset_empty_xy_power = False
run_mode = None
special_tag = "fast"

[mongo]
inst_host = "***********"
inst_port = 27017

[auto]
att = -100
cavity_left = 7000
cavity_right = 8000
vna_high_power = -15
vna_low_power = -15
ifbw_high = 500
ifbw_low = 100
period_cavity = 10
period_qubit = 100
chip_type = "102bit-v2.0"
generate_ppt = True
save_db = True
max_batch_count = 1
coupler_tunable = "CouplerV1"
mic_source = "byfs"
dc_source = "qaio"
