{"meta": {"username": "zyc", "monster_version": "0.23.2", "sample": "221205-72bit-300pin-V8.2P1-base-4#-72bitSE3", "env_name": "A2"}, "nodes": {"BatchSaturationPower": {"meta": {"exp_class_name": "BatchSaturationPower", "description": "寻找网分饱和功率"}, "experiment_options": {}, "analysis_options": {}}, "BatchBusS21": {"meta": {"exp_class_name": "BatchBusS21", "description": "粗采 BUS S21 数据"}, "experiment_options": {}, "analysis_options": {}}, "BatchBusCavityQ": {"meta": {"exp_class_name": "BatchBusCavityQ", "description": "腔 Q 值拟合"}, "experiment_options": {}, "analysis_options": {}}, "BatchBusCavityCheck": {"meta": {"exp_class_name": "BatchBusCavityCheck", "description": "多腔少腔检测"}, "experiment_options": {"mode": "low_power"}, "analysis_options": {}}, "BatchBusImpaCalibrate": {"meta": {"exp_class_name": "BatchBusImpaCalibrate", "description": "Bus Impa 校准"}, "experiment_options": {}, "analysis_options": {}}, "BatchBusImpaOpen": {"meta": {"exp_class_name": "BatchBusImpaControl", "description": "Bus Impa 开启"}, "experiment_options": {"mode": "open"}, "analysis_options": {}}, "BatchBusImpaClose": {"meta": {"exp_class_name": "BatchBusImpaControl", "description": "Bus Impa 关闭"}, "experiment_options": {"mode": "close"}, "analysis_options": {}}, "BatchCavityQ": {"meta": {"exp_class_name": "BatchCavityQ", "description": "高低功率Q值采集"}, "experiment_options": {"low_point_label": "init_point", "high_point_label": "high_point", "cavity_check": true}, "analysis_options": {}}, "BatchImpaCalibrate": {"meta": {"exp_class_name": "BatchImpaCalibrate", "description": "IMPA 标定"}, "experiment_options": {}, "analysis_options": {}}, "OpenIMPA": {"meta": {"exp_class_name": "BatchIMPAControl", "description": "IMPA 增益一键开启"}, "experiment_options": {"mode": "open"}, "analysis_options": {}}, "CloseIMPA": {"meta": {"exp_class_name": "BatchIMPAControl", "description": "IMPA 增益一键关闭"}, "experiment_options": {"mode": "close"}, "analysis_options": {}}, "BatchCavityPowerScan_V0": {"meta": {"exp_class_name": "BatchCavityPowerScan", "description": "V0 位置腔功率谱"}, "experiment_options": {"point_mode": "zero"}, "analysis_options": {}}, "BatchCouplerTunable": {"meta": {"exp_class_name": "BatchCouplerTunable", "description": "<PERSON>upler 调制谱"}, "experiment_options": {"mode": "line"}, "analysis_options": {}}, "BatchQubitTunable": {"meta": {"exp_class_name": "BatchQubitTunable", "description": "Qubit 调制谱"}, "experiment_options": {}, "analysis_options": {}}, "BatchQubitCavityCheck": {"meta": {"exp_class_name": "BatchQubitCavityCheck", "description": "腔检测"}, "experiment_options": {}, "analysis_options": {}}, "BatchCavityPowerScan_VMax": {"meta": {"exp_class_name": "BatchCavityPowerScan", "description": "V Max 位置腔功率谱"}, "experiment_options": {"point_mode": "max"}, "analysis_options": {}}, "BatchQubitSpectrumZAmp": {"meta": {"exp_class_name": "BatchQubitSpectrumZAmp", "description": "初测比特二维能谱"}, "experiment_options": {"power_scan_flow": ["CavityFreqSpectrum_BSZ", "QubitSpectrumScanPower"], "max_f01_flow": ["QubitSpectrumVMax"], "min_f01_flow": ["QubitSpectrumVMin"], "amp_scan_flow": ["QubitSpectrumZAmp"], "v_min_f01_limit": 3750, "v_max_f01_limit": 5000, "scan_amp_step": 0.01}, "analysis_options": {}}, "BatchSearchIdlePoint": {"meta": {"exp_class_name": "BatchSearchIdlePoint", "description": "找 idle"}, "experiment_options": {"qs_flows": ["CavityFreqSpectrum_BSI", "QubitSpectrum_BSI"], "idle_flows": ["XpiDetection", "QubitFreqCalibration", "XpiDetection"], "idle_step": 0.01, "idle_points": 20, "is_qs_divide": true}, "analysis_options": {}}, "BatchReadout": {"meta": {"exp_class_name": "BatchReadout", "description": "找读取"}, "experiment_options": {"flows": ["ReadoutFreqCalibrate_01", "ReadoutPowerCalibrate_01"]}, "analysis_options": {}}, "BatchSearchF12": {"meta": {"exp_class_name": "BatchSearchF12", "description": "F12 搜索"}, "experiment_options": {"flows": ["QubitSpectrumF12", "RabiScanWidthF12", "F12XpiDetection", "F12Calibration"], "xpi_rate": [0.2, 0.5, 0.8, 1]}, "analysis_options": {}}, "BatchXYCrossRabiWidth": {"meta": {"exp_class_name": "BatchXYCrossRabiWidth", "description": "xy 串扰"}, "experiment_options": {"cali_freq_flag": true, "cali_freq_flows": ["QubitFreqCalibration_XC"], "strength_flows": ["RabiScanWidth_XC"], "xy_cross_flows": ["XYCrossRabiWidthOnce_XC"]}, "analysis_options": {}}, "BatchXZTiming": {"meta": {"exp_class_name": "BatchXZTiming", "description": "全局寻找 XYZTiming "}, "experiment_options": {"z_amp_list": [0.2, -0.2, 0.25, -0.25], "exp_retry": 0, "quality_block_exp": ["SingleShot_XT", "SingleShot_XT1"], "flows": ["SingleShot_XT"], "timing_flows": ["XYZTimingComposite"], "save_every_exp": true}, "analysis_options": {}}, "BatchACT1Spectrum": {"meta": {"exp_class_name": "BatchACT1Spectrum", "description": "AC T1 谱搜索 "}, "experiment_options": {"max_cali_flows": [], "ac_spec_flows": ["ACSpectrum"], "t1_spec_flows": [], "gap_points": 30, "band_points": 5, "t1_step": 20, "t1_points": 20, "save_every_exp": true}, "analysis_options": {}}, "BatchRBSpectrum": {"meta": {"exp_class_name": "BatchRBSpectrum", "description": "RB 谱搜索 "}, "experiment_options": {"max_batch_count": 1, "flows": ["XpiDetection_RBS0", "QubitFreqCalibration_RBS_0", "XpiDetection_RBS", "DetuneCalibration_RBS", "RabiScanAmp_RBS", "AmpComposite_RBS", "SingleShot_RBS", "T1", "T2Ramsey", "SpinEcho", "RBSingle"], "quality_block_exp": ["DetuneCalibration_RBS", "T1", "T2Ramsey", "SpinEcho", "RBSingle"]}, "analysis_options": {}}}}