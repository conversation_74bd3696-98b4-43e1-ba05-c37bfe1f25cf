{
    "FindSaturationPower": {
        "meta": {
            "exp_class_name": "FindSaturationPower"
        },
        "context_options": {
            "name": "sqmc_context",
            "readout_type": "",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "scan_power": "Points(51) | qarange | (-40, 10, 1)"
            },
            "analysis_options": {
                "quality_bounds": [
                    0.98,
                    0.95,
                    0.85
                ]
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "BusS21Collector": {
        "meta": {
            "exp_class_name": "BusS21Collector"
        },
        "context_options": {
            "name": "sqmc_context",
            "readout_type": "",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "wide_scope_freq": "Points(25) | qarange | (4000, 8500, 1)"
            },
            "analysis_options": {}
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "BusQFit": {
        "meta": {
            "exp_class_name": "BusQFit"
        },
        "context_options": {
            "name": "sqmc_context",
            "readout_type": "",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "high_power_cavity": null,
                "low_power_cavity": null
            },
            "analysis_options": {
                "quality_bounds": [
                    0.9,
                    0.7,
                    0.6
                ]
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "CavityCheck": {
        "meta": {
            "exp_class_name": "CavityCheck"
        },
        "context_options": {
            "name": "union_read_measure",
            "readout_type": "",
            "physical_unit": "bus-1"
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "flux_list": "Points(54) | qarange | (-0.4, 0.4, 0.05)",
                "child_exp_options": {
                    "readout_power": -30
                }
            },
            "analysis_options": {
                "child_ana_options": {
                    "quality_bounds": [
                        0.9,
                        0.7,
                        0.6
                    ]
                }
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "FindBusCavityFreq_rough": {
        "meta": {
            "exp_class_name": "FindBusCavityFreq"
        },
        "context_options": {
            "name": "union_read_measure",
            "readout_type": "",
            "physical_unit": "q1,q2,q3,q4,q5,q6"
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "segm_scan": false
            },
            "analysis_options": {
                "cavity_count": 6,
                "distance": 7
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "FindBusCavityFreq_segma": {
        "meta": {
            "exp_class_name": "FindBusCavityFreq"
        },
        "context_options": {
            "name": "union_read_measure",
            "readout_type": "",
            "physical_unit": "q1,q2,q3,q4,q5,q6"
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "segm_scan": true
            },
            "analysis_options": {
                "cavity_count": 6,
                "fit_q": true,
                "chi_square": 0.01,
                "distance": 7
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "ImpaCavityFluxScan": {
        "meta": {
            "exp_class_name": "ImpaCavityFluxScan"
        },
        "context_options": {
            "name": "sqmc_context",
            "readout_type": "",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "scan_name": "dc",
                "dc_list": "Points(501) | qarange | (-10, 10, 0.2)",
                "freq_list": "Points(1001) | qarange | (6000, 7000, 1)"
            },
            "analysis_options": {}
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "ImpaOptiParams": {
        "meta": {
            "exp_class_name": "ImpaOptiParams"
        },
        "context_options": {
            "name": "union_read_measure",
            "readout_type": "",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "freq_list": "Points(1001) | qarange | (6000, 7000, 1)",
            },
            "analysis_options": {
                "gain_bounds": 8
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "ImpaGain": {
        "meta": {
            "exp_class_name": "ImpaGain"
        },
        "context_options": {
            "name": "sqmc_context",
            "readout_type": "",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {},
            "analysis_options": {
                "threshold": 5
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "CavityFreqSpectrum": {
        "meta": {
            "exp_class_name": "CavityFreqSpectrum"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {},
            "analysis_options": {
                "quality_bounds": [
                    0.8,
                    0.6,
                    0.5
                ]
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "CavityPowerScan": {
        "meta": {
            "exp_class_name": "CavityPowerScan"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "power_list": "Points(0) | qarange | (-40, -10, 2)"
            },
            "analysis_options": {
                "child_ana_options": {
                    "quality_bounds": [
                        0.95,
                        0.9,
                        0.85
                    ]
                },
                "quality_bounds": [
                    0.9,
                    0.8,
                    0.7
                ],
                "f_threshold": 0.05
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "CavityTunable_for_coupler": {
        "meta": {
            "exp_class_name": "CavityTunable"
        },
        "context_options": {
            "name": "coupler_calibration",
            "physical_unit": "c1-2",
            "readout_type": "probeQ"
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "flux_list": "Points(0) | qarange | (-0.48, 0.48, 0.03)"
            },
            "analysis_options": {
                "child_ana_options": {
                    "quality_bounds": [
                        0.85,
                        0.75,
                        0.6
                    ]
                },
                "diff_threshold": 0.005,
                "quality_bounds": [
                    0.8,
                    0.65,
                    0.5
                ]
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "CavityTunable_for_qubit": {
        "meta": {
            "exp_class_name": "CavityTunable"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "",
            "physical_unit": "q7"
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "flux_list": "Points(0) | qarange | (-0.48, 0.48, 0.03)"
            },
            "analysis_options": {
                "child_ana_options": {
                    "quality_bounds": [
                        0.9,
                        0.85,
                        0.75
                    ]
                },
                "diff_threshold": 0.02,
                "tackle_type": "Qubit",
                "quality_bounds": [
                    0.98,
                    0.8,
                    0.5
                ]
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "QubitSpectrumScanPower": {
        "meta": {
            "exp_class_name": "QubitSpectrumScanPower"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "",
            "physical_unit": "q21"
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "child_exp_options": {
                    "freq_list": "Points(81) | qarange | (3800, 6000, 10)",
                    "xpulse_params": {
                        "time": 5000,
                        "offset": 15,
                        "amp": 1.0,
                        "detune": 0,
                        "freq": 466.667
                    },
                    "zpulse_params": {
                        "time": 6000,
                        "amp": 0.0,
                        "sigma": 5.0,
                        "fast_m": false
                    }
                },
                "drive_power_list": "Points(16) | qarange | (-40, -10, 2)"
            },
            "analysis_options": {
                "child_ana_options": {
                    "snr_bounds": 1.5
                }
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "QubitSpectrumVMax": {
        "meta": {
            "exp_class_name": "QubitSpectrum"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "freq_list": "Points(161) | qarange | (3800, 6000, 10)",
                "xpulse_params": {
                    "time": 5000,
                    "offset": 15,
                    "amp": 1.0,
                    "detune": 0,
                    "freq": 466.667
                },
                "zpulse_params": {
                    "time": 6000,
                    "amp": 0.0,
                    "sigma": 5.0,
                    "fast_m": false
                }
            },
            "analysis_options": {
                "snr_bounds": 1.5
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "QubitSpectrumVMin": {
        "meta": {
            "exp_class_name": "QubitSpectrum"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "freq_list": "Points(161) | qarange | (3800, 6000, 10)",
                "xpulse_params": {
                    "time": 5000,
                    "offset": 15,
                    "amp": 1.0,
                    "detune": 0,
                    "freq": 466.667
                },
                "zpulse_params": {
                    "time": 6000,
                    "amp": 0.0,
                    "sigma": 5.0,
                    "fast_m": false
                }
            },
            "analysis_options": {
                "snr_bounds": 1.5
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "QubitSpectrumZAmp": {
        "meta": {
            "exp_class_name": "QubitSpectrumZAmp"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "child_exp_options": {
                    "freq_list": "Points(121) | qarange | (3800, 6000, 5)",
                    "xpulse_params": {
                        "time": 5000,
                        "offset": 15,
                        "amp": 1.0,
                        "detune": 0,
                        "freq": 466.667
                    },
                    "zpulse_params": {
                        "time": 6000,
                        "amp": 0.0,
                        "sigma": 5.0,
                        "fast_m": false
                    }
                },
                "z_amp_list": "Points(0) | normal | None"
            },
            "analysis_options": {
                "child_ana_options": {
                    "quality_bounds": [
                        0.8,
                        0.6,
                        0.5
                    ],
                    "snr_bounds": 1.5
                },
                "diff_ratio": 0.5,
                "point_nums": 5
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "CavityFreqSpectrum_BSZ": {
        "meta": {
            "exp_class_name": "CavityFreqSpectrum"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "",
            "physical_unit": "q5"
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "points": 61,
                "scope": 3
            },
            "analysis_options": {
                "quality_bounds": [
                    0.95,
                    0.9,
                    0.85
                ]
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "CavityFreqSpectrum_BSI": {
        "meta": {
            "exp_class_name": "CavityFreqSpectrum"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "",
            "physical_unit": "q5"
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "points": 61,
                "scope": 3
            },
            "analysis_options": {
                "quality_bounds": [
                    0.95,
                    0.9,
                    0.85
                ]
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "QubitSpectrum_BSI": {
        "meta": {
            "exp_class_name": "QubitSpectrum"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "freq_list": "Points(141) | qarange | (3800, 6000, 5)",
                "xpulse_params": {
                    "time": 4000,
                    "offset": 15,
                    "amp": 1.0,
                    "detune": 0,
                    "freq": 466.667
                },
                "zpulse_params": {
                    "time": 5000,
                    "amp": 0.0,
                    "sigma": 5.0,
                    "fast_m": false
                }
            },
            "analysis_options": {
                "snr_bounds": 1.5
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "XpiDetection": {
        "meta": {
            "exp_class_name": "XpiDetection"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "expect_value": 0.7,
                "scope": 0.2,
                "max_loops": 5
            },
            "analysis_options": {
                "child_ana_options": {
                    "quality_bounds": [
                        0.9,
                        0.8,
                        0.7
                    ]
                }
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "RabiScanWidth": {
        "meta": {
            "exp_class_name": "RabiScanWidth"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {},
            "analysis_options": {
                "quality_bounds": [
                    0.9,
                    0.75,
                    0.65
                ]
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "SingleShot_BSI": {
        "meta": {
            "exp_class_name": "SingleShot"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {},
            "analysis_options": {
                "quality_bounds": [
                    2,
                    0.5,
                    0.05,
                    0.05
                ]
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "QubitFreqCalibration": {
        "meta": {
            "exp_class_name": "QubitFreqCalibration"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "fringes": "Points(2) | qarange | (41, -41, -82)",
                "delays": "Points(71) | qarange | (150, 300, 2.5)"
            },
            "analysis_options": {
                "child_ana_options": {
                    "quality_bounds": [
                        0.9,
                        0.7,
                        0.5
                    ]
                },
                "freq_gap_threshold": 1
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "ReadoutFreqCalibrate_01": {
        "meta": {
            "exp_class_name": "ReadoutFreqCalibrate"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "01",
            "physical_unit": "q5"
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "child_exp_options": {
                    "points": 101,
                    "scope": 5
                }
            },
            "analysis_options": {
                "child_ana_options": {
                    "quality_bounds": [
                        0.95,
                        0.9,
                        0.85
                    ]
                },
                "save_mode": "mean_point",
                "diff_threshold": 0.02
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "ReadoutPowerCalibrate_01": {
        "meta": {
            "exp_class_name": "ReadoutPowerCalibrate"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "01",
            "physical_unit": "q5"
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "sweep_list": "Points(21) | qarange | (-40, -10, 1)"
            },
            "analysis_options": {
                "child_ana_options": {
                    "quality_bounds": [
                        2,
                        0.85,
                        0.6,
                        0.015
                    ]
                }
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "QubitFreqCalibration_XC": {
        "meta": {
            "exp_class_name": "QubitFreqCalibration"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "01",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "fringes": "Points(2) | qarange | (50, -50, -100)",
                "delays": "Points(41) | qarange | (120, 240, 2.5)"
            },
            "analysis_options": {
                "child_ana_options": {
                    "quality_bounds": [
                        0.95,
                        0.9,
                        0.85
                    ]
                },
                "freq_gap_threshold": 0.5
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "RabiScanWidth_XC": {
        "meta": {
            "exp_class_name": "RabiScanWidth"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "01",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {},
            "analysis_options": {
                "quality_bounds": [
                    0.95,
                    0.9,
                    0.85
                ]
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "XYCrossRabiWidthOnce_XC": {
        "meta": {
            "exp_class_name": "XYCrossRabiWidthOnce"
        },
        "context_options": {
            "name": "crosstalk_measure",
            "readout_type": "01",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "widths": "Points(40) | qarange | (5, 300, 7.5)",
                "xy_name": "",
                "rd_name": ""
            },
            "analysis_options": {
                "quality_bounds": [
                    0.90,
                    0.80,
                    0.70
                ]
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "SingleShot_F12": {
        "meta": {
            "exp_class_name": "SingleShot"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {},
            "analysis_options": {
                "quality_bounds": [
                    2,
                    0.5,
                    0.05,
                    0.05
                ]
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "QubitSpectrumF12": {
        "meta": {
            "exp_class_name": "QubitSpectrumF12"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "01",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "f12_xpi": null,
                "f12_width": 500,
                "scope": {
                    "l": 300,
                    "r": -200,
                    "s": 1
                },
                "z_amp": 0,
                "delay": 0
            },
            "analysis_options": {
                "quality_bounds": [
                    0.8,
                    0.5,
                    0.4
                ]
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "RabiScanWidthF12": {
        "meta": {
            "exp_class_name": "RabiScanWidthF12"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "01",
            "physical_unit": "q16"
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "widths": "Points(41) | qarange | (100, 800, 5)"
            },
            "analysis_options": {}
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "F12XpiDetection": {
        "meta": {
            "exp_class_name": "F12XpiDetection"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "01",
            "physical_unit": "q32"
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "expect_value": 0.7,
                "scope": 0.1,
                "max_loops": 5,
                "name": "Xpi",
                "amps": "Points(51) | qarange | (0.0, 0.98, 0.02)"
            },
            "analysis_options": {
                "child_ana_options": {
                    "quality_bounds": [
                        0.95,
                        0.9,
                        0.85
                    ]
                }
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "F12Calibration": {
        "meta": {
            "exp_class_name": "F12Calibration"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "01",
            "physical_unit": "q16"
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "fringes": "Points(2) | qarange | (50, -50, -100)",
                "delays": "Points(81) | qarange | (100, 300, 2.5)"
            },
            "analysis_options": {
                "child_ana_options": {
                    "quality_bounds": [
                        0.95,
                        0.8,
                        0.7
                    ]
                },
                "freq_gap_threshold": 1
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "SingleShot_XT": {
        "meta": {
            "exp_class_name": "SingleShot"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {},
            "analysis_options": {
                "quality_bounds": [
                    2,
                    0.6,
                    0.3,
                    0.05
                ]
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "XYZTimingComposite": {
        "meta": {
            "exp_class_name": "XYZTimingComposite"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "01",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "child_exp_options": {
                    "delays": "Points(321) | qarange | (0, 200, 1.66666666)",
                    "const_delay": 100,
                    "z_pulse_params": {
                        "time": 15,
                        "amp": 0.2,
                        "sigma": 0.2,
                        "buffer": 2.5
                    }
                },
                "policy": "normal"
            },
            "analysis_options": {
                "child_ana_options": {
                    "peak_limit": 0.2,
                    "extract_mode": "fit_params",
                    "quality_bounds": [
                        0.95,
                        0.9,
                        0.85
                    ],
                    "fit_model_name": "gauss_lorentzian"
                }
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "SweetPointCalibration": {
        "meta": {
            "exp_class_name": "SweetPointCalibration"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "01",
            "physical_unit": "q1"
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "child_exp_options": {
                    "delays": "Points(41) | qarange | (120, 280, 2.5)",
                    "fringe": 40
                },
                "iteration": 5,
                "threshold": 0.05,
                "guess_step": 0.8,
                "cali_point": "sweet_point"
            },
            "analysis_options": {
                "child_ana_options": {
                    "quality_bounds": [
                        0.95,
                        0.9,
                        0.85
                    ]
                }
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "ACSpectrum": {
        "meta": {
            "exp_class_name": "ACSpectrum"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "01",
            "physical_unit": "q4"
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "z_amps": "Points(26) | qarange | (-0.02, 0.4, 0.02)",
                "delays": "Points(41) | qarange | (100, 200, 2.5)",
                "freq_bound": 1500,
                "osc_freq_limit": 800,
                "init_fringe": 66,
                "spectrum_type": "standard",
                "fit_model_name_list": [
                    "amp2freq_formula"
                ]
            },
            "analysis_options": {
                "child_ana_options": {
                    "quality_bounds": [
                        0.98,
                        0.93,
                        0.81
                    ]
                },
                "quality_bounds": [
                    0.995,
                    0.992,
                    0.99
                ],
                "fit_model_name": "amp2freq_formula"
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "SingleShot_RBS": {
        "meta": {
            "exp_class_name": "SingleShot"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "",
            "physical_unit": "q10"
        },
        "options_for_regular_exec": {
            "experiment_options": {},
            "analysis_options": {
                "quality_bounds": [
                    2,
                    0.5,
                    0.05,
                    0.05
                ]
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "QubitFreqCalibration_RBS_0": {
        "meta": {
            "exp_class_name": "QubitFreqCalibration"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "01",
            "physical_unit": "q18"
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "child_exp_options": {},
                "fringes": "Points(2) | qarange | (60, -60, -120)",
                "delays": "Points(57) | qarange | (110, 230, 2.5)"
            },
            "analysis_options": {
                "child_ana_options": {
                    "quality_bounds": [
                        0.7,
                        0.5,
                        0.4
                    ]
                },
                "freq_gap_threshold": 1
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "QubitFreqCalibration_RBS_1": {
        "meta": {
            "exp_class_name": "QubitFreqCalibration"
        },
        "context_options": {
            "name": "qubit_calibration",
            "physical_unit": "q1",
            "readout_type": "01"
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "child_exp_options": {},
                "fringes": "Points(2) | qarange | (60, -60, -120)",
                "delays": "Points(57) | qarange | (110, 230, 2.5)"
            },
            "analysis_options": {
                "child_ana_options": {
                    "quality_bounds": [
                        0.7,
                        0.5,
                        0.4
                    ]
                },
                "freq_gap_threshold": 1
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "DetuneCalibration_RBS": {
        "meta": {
            "exp_class_name": "DetuneCalibration"
        },
        "context_options": {
            "name": "qubit_calibration",
            "physical_unit": "q1",
            "readout_type": "01"
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "rough_n_list": "Points(3) | normal | [6, 7, 8]",
                "fine_n_list": "Points(2) | normal | [7, 9]",
                "theta_type": "Xpi",
                "fine_precision": 0.15,
                "child_exp_options": {
                    "child_exp_options": {}
                }
            },
            "analysis_options": {
                "child_ana_options": {
                    "diff_threshold": 0.3
                }
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "RabiScanAmp_RBS": {
        "meta": {
            "exp_class_name": "RabiScanAmp"
        },
        "context_options": {
            "name": "qubit_calibration",
            "physical_unit": "q1",
            "readout_type": "01"
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "amps": "Points(3) | qarange | (0, 0.98, 0.02)"
            },
            "analysis_options": {
                "quality_bounds": [
                    0.95,
                    0.8,
                    0.7
                ]
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "AmpComposite_RBS": {
        "meta": {
            "exp_class_name": "AmpComposite"
        },
        "context_options": {
            "name": "qubit_calibration",
            "physical_unit": "q1",
            "readout_type": "01"
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "n_list": "Points(2) | normal | [16, 20]",
                "theta_type": "Xpi/2",
                "child_exp_options": {
                    "points": 81,
                    "threshold_left": 0.9,
                    "threshold_right": 1.1
                }
            },
            "analysis_options": {
                "diff_threshold": 0.01
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "T1": {
        "meta": {
            "exp_class_name": "T1"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "01",
            "physical_unit": "q93"
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "delays": "Points(34) | qarange | (1000, 63000, 1000)"
            },
            "analysis_options": {
                "quality_bounds": [
                    0.95,
                    0.9,
                    0.85
                ]
            }
        }
    },
    "T2Ramsey": {
        "meta": {
            "exp_class_name": "T2Ramsey"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "01",
            "physical_unit": "q93"
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "delays": "Points(250) | qarange | (200, 12000, 100)",
                "fringe": 1,
                "rate_down": 0.3,
                "rate_up": 0.5,
                "max_loops": 3
            },
            "analysis_options": {
                "child_ana_options": {
                    "quality_bounds": [
                        0.9,
                        0.8,
                        0.7
                    ]
                }
            }
        }
    },
    "SpinEcho": {
        "meta": {
            "exp_class_name": "SpinEcho"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "01",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "delays": "Points(201) | qarange | (250, 60000, 500)",
                "fringe": 0.2
            },
            "analysis_options": {
                "quality_bounds": [
                    0.95,
                    0.9,
                    0.85
                ]
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "RBSingle": {
        "meta": {
            "exp_class_name": "RBSingle"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "01",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "times": 15,
                "depth1": "Points(201) | qarange | (2, 10, 2)",
                "depth2": "Points(201) | qarange | (15, 50, 5)",
                "depth3": "Points(201) | qarange | (60, 100, 20)",
                "depth4": "Points(201) | qarange | (150, 500, 50)",
                "gate_split": true,
                "mode": "cache"
            },
            "analysis_options": {
                "quality_bounds": [
                    0.9,
                    0.8,
                    0.7
                ],
                "std_bound": 0.08,
                "fidelity_threshold": 0.99
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "XpiDetection_RBS": {
        "meta": {
            "exp_class_name": "XpiDetection"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "01",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "expect_value": 0.7,
                "scope": 0.2,
                "max_loops": 5
            },
            "analysis_options": {
                "child_ana_options": {
                    "quality_bounds": [
                        0.9,
                        0.8,
                        0.7
                    ]
                }
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    },
    "XpiDetection_RBS0": {
        "meta": {
            "exp_class_name": "XpiDetection"
        },
        "context_options": {
            "name": "qubit_calibration",
            "readout_type": "01",
            "physical_unit": ""
        },
        "options_for_regular_exec": {
            "experiment_options": {
                "expect_value": 0.7,
                "scope": 0.2,
                "max_loops": 5
            },
            "analysis_options": {
                "child_ana_options": {
                    "quality_bounds": [
                        0.9,
                        0.8,
                        0.7
                    ]
                }
            }
        },
        "options_for_parallel_exec": {
            "experiment_options": {},
            "analysis_options": {}
        }
    }
}
