# -*- coding: utf-8 -*-
# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/06/12
# __author:       <PERSON><PERSON><PERSON>

from collections import OrderedDict
from copy import deepcopy
from functools import cmp_to_key
from pathlib import Path

import app.automation.nodes as batch_nodes
from app.tool.utils import read_json_file
from pyQCat.errors import ExperimentOptionsError
from pyQCat.executor import Backend
from pyQCat.experiments.batch.batch_runner import <PERSON><PERSON><PERSON><PERSON><PERSON>
from pyQCat.log import pyqlog
from pyQCat.processor.utilities import sort_bit
from pyQCat.structures import Options, QDict
from pyQCat.tools import qarange


def init_bus_data(bus_list, high_power, low_power, att):
    data = QDict(median_chi=3, low_power=low_power, ATT=att, bus_state=QDict())
    for bus in bus_list:
        data.bus_state[bus] = QDict(
            high_power=high_power,
            low_power_cavity=None,
            high_power_cavity=None,
            cavity_shift=None,
            s21_quality="bad",
            q_data=None,
            impa_params=None,
        )
    return data


class AutomationExecutor:
    def __init__(self, backend: Backend):
        self.backend = backend
        self._batch_config = {}
        self._batch_records = OrderedDict()
        self._experiment_options = self._default_experiment_options()
        self._run_options = self._default_run_options()
        self._bus_data = QDict()

        self._node_feedback_mapping = {
            "BatchSaturationPower": self._refresh_saturation_power,
            "BatchBusS21": self._refresh_s21_data,
        }

        self._runner = BatchRunner(backend)

    @property
    def auto_config(self) -> QDict:
        return self.backend._config.auto

    @property
    def experiment_options(self):
        return self._experiment_options

    @property
    def run_options(self):
        return self._run_options

    @classmethod
    def _default_experiment_options(cls) -> Options:
        """Default kwarg options for experiment."""
        options = Options()

        options.set_validator("node_config", str)
        options.node_config = ""

        options.set_validator("experiment_config", str)
        options.experiment_config = ""

        options.set_validator("nodes", list)
        options.nodes = []

        options.set_validator("_physical_unit_map", QDict)
        options.physical_unit_map = {}

        return options

    @classmethod
    def _default_run_options(cls) -> Options:
        """Default kwarg options for experiment."""
        options = Options()
        options.process_data = QDict()
        options.running_physical_unit_map = QDict()
        return options

    def set_experiment_options(self, **fields):
        for field in fields:
            if field not in self._experiment_options:
                raise ExperimentOptionsError(
                    self._label,
                    key=field,
                    value=fields.get(field),
                    msg=f"field {field} option must be defined in advance!",
                )
        self._experiment_options.update(**fields)

    def _check_options(self):
        self._batch_config = read_json_file(self.experiment_options.node_config)
        self._bind_physical_units()
        self._refresh_system()

    def _refresh_system(self):
        # 绑定历史 bus data
        self._bus_data = QDict(
            **(
                read_json_file(
                    Path(self.backend._config.system.config_path) / "bus_data.json"
                )
                or {}
            )
        ) or init_bus_data(
            self.run_options.running_physical_unit_map.bus,
            self.auto_config.vna_high_power,
            self.auto_config.vna_low_power,
            self.auto_config.att,
        )

        # 初始化默认 Batch，生成 ParamsManager 全节点共享
        self._runner.set_experiment_options(
            param_path=self.experiment_options.experiment_config
        )
        self._runner._init_params_manager()

        # 绑定自动化节点默认参数
        self._refresh_node_options()

        # 绑定实验默认参数
        self._refresh_experiment_options()

        # 刷新网分饱和功率
        self._refresh_saturation_power()

        # 刷新 S21 数据
        self._refresh_s21_data()

    def _refresh_node_options(self):
        retry_nodes = [
            "BatchImpaCalibrate",
            "BatchCouplerTunable",
            "BatchQubitTunable",
            "BatchReadout",
            "BatchSearchF12",
            "BatchXZTiming",
            "BatchACT1Spectrum",
            "BatchBusS21",
        ]
        cavity_nodes = [
            "BatchSaturationPower",
            "BatchBusS21",
            "BatchBusCavityQ",
            "BatchBusCavityCheck",
            "BatchBusImpaCalibrate",
            "BatchBusImpaOpen",
            "BatchBusImpaCloseBatchCavityQ",
            "BatchImpaCalibrate",
            "OpenIMPA",
            "CloseIMPABatchCavityPowerScan_V0",
            "BatchCouplerTunable",
            "BatchQubitTunable",
            "BatchQubitCavityCheck",
            "BatchCavityPowerScan_VMax",
        ]

        # 设置需要 retry 的几点
        for node_name in retry_nodes:
            self._set_node_exp_options(
                node_name, max_batch_count=self.auto_config.max_batch_count
            )

        for node_name in self._batch_config["nodes"]:
            # 设置是否生成 ppt 和持久化数据
            self._set_node_exp_options(
                node_name,
                generate_ppt=self.auto_config.generate_ppt,
                save_db=self.auto_config.save_db,
            )

            # 设置脉冲周期
            if node_name in cavity_nodes:
                self._set_node_exp_options(
                    node_name, period=self.auto_config.period_cavity
                )
            else:
                self._set_node_exp_options(
                    node_name, period=self.auto_config.period_qubit
                )

        # 设置芯片类型
        self._set_node_exp_options(
            "BatchBusCavityCheck", chip_type=self.auto_config.chip_type
        )

    def _refresh_experiment_options(self):
        cavity_scope = qarange(
            self.auto_config.cavity_left, self.auto_config.cavity_right, 1
        )

        # FindSaturationPower 选用腔左边界右偏 5 MHz 的位置；中频带宽使用低带宽
        self._runner.change_regular_exec_exp_options(
            exp_name="FindSaturationPower",
            freq=self.auto_config.cavity_left + 5,
            net_IFBW=self.auto_config.ifbw_low,
        )

        # BusS21Collector 设置细扫范围，网分饱和功率，网分低功率，饱和功率带宽 ，低功率带宽
        self._runner.change_regular_exec_exp_options(
            exp_name="BusS21Collector",
            small_scope_freq=cavity_scope,
            high_power=self.auto_config.vna_high_power,
            low_power=self.auto_config.vna_low_power,
            IFBW_high_power=self.auto_config.ifbw_high,
            IFBW_low_power=self.auto_config.ifbw_low,
        )

        # BusQFit 设置网分饱和功率，网分低功率，饱和功率带宽，低功率带宽，线路衰减
        self._runner.change_regular_exec_exp_options(
            exp_name="BusQFit",
            high_power=self.auto_config.vna_high_power,
            low_power=self.auto_config.vna_low_power,
            IFBW_high_power=self.auto_config.ifbw_high,
            IFBW_low_power=self.auto_config.ifbw_low,
            ATT=self.auto_config.att,
        )

        # CavityTunable_for_coupler 设置 Coupler 类型
        self._runner.change_regular_exec_ana_options(
            exp_name="CavityTunable_for_coupler",
            tackle_type=self.auto_config.coupler_tunable,
        )

        # ImpaCavityFluxScan 设置网分带宽, 功率, 微波源、DC源
        self._runner.change_regular_exec_ana_options(
            exp_name="ImpaCavityFluxScan",
            net_IFBW=self.auto_config.ifbw_low,
            net_power=self.auto_config.vna_low_power,
            dc_source=self.auto_config.dc_source,
            mic_source=self.auto_config.mic_source
        )

        # ImpaOptiParams 设置网分带宽，功率, 微波源、DC源
        self._runner.change_regular_exec_ana_options(
            exp_name="ImpaOptiParams",
            net_IFBW=self.auto_config.ifbw_low,
            net_power=self.auto_config.vna_low_power,
            dc_source=self.auto_config.dc_source,
            mic_source=self.auto_config.mic_source
        )
        
        # IMPAGain 设置扫描范围，设置网分带宽，功率, 微波源、DC源
        self._runner.change_regular_exec_exp_options(
            exp_name="ImpaGain",
            freq_list=cavity_scope,
            net_IFBW=self.auto_config.ifbw_low,
            net_power=self.auto_config.vna_low_power,
            dc_source=self.auto_config.dc_source,
            mic_source=self.auto_config.mic_source
        )

    def _refresh_saturation_power(self):
        """
        更新网分饱和功率, BatchSaturationPower 执行会触发更新

        1. BusS21Collector 更新高功率
        2. BusQFit 更新高功率
        """
        for bus, bus_state in self._bus_data.bus_state.items():
            for exp in ["BusS21Collector", "BusQFit"]:
                self._runner.change_parallel_exec_exp_options(
                    exp_name=exp, unit=bus, high_power=bus_state.high_power
                )

    def _refresh_s21_data(self):
        """
        BatchBusS21 执行会触发更新, 它包含：

        1. 统计出所有高低功率腔 shift 的中位数
        2. 计算出各 BUS 的功率功率腔位置

        - BusQFit 更新扫描范围为单边 1.5 倍 shift, 步长为 0.05 MHz
        - CavityCheck 更新扫描范围为单边 1.5 倍 shift, 步长为 0.1 MHz
        - CavityFreqSpectrum 更新扫描范围为单边 1 倍 shift, 步长为 0.1 MHz
        - CavityPowerScan 更新扫描范围为单边 2 倍 shift, 步长为 0.1 MHz
        - CavityTunable_for_coupler 更新扫描范围为单边 2 倍 shift, 步长为 0.1 MHz
        - CavityTunable_for_qubit 更新扫描范围为单边 0.5 倍 shift, 步长为 0.1 MHz
        """
        # 更新腔 shift, 默认最小值为 1, 最大值为 10
        median_chi = self._bus_data.median_chi or 3
        median_chi = max(1, median_chi)
        median_chi = min(10, median_chi)
        self._runner.change_regular_exec_exp_options(
            exp_name="BusQFit", scope=1.5 * median_chi, step=0.05
        )
        self._runner.change_regular_exec_exp_options(
            exp_name="CavityCheck",
            scope=1.5 * median_chi,
            points=int(median_chi * 3 / 0.15),
        )
        self._runner.change_regular_exec_exp_options(
            exp_name="CavityFreqSpectrum",
            scope=median_chi,
            points=int(median_chi * 2 / 0.1),
        )
        self._runner.change_regular_exec_exp_options(
            exp_name="CavityPowerScan",
            options={
                "child_exp_options.scope": median_chi * 2,
                "child_exp_options.points": int(median_chi * 4 / 0.1),
            },
        )
        self._runner.change_regular_exec_exp_options(
            exp_name="CavityTunable_for_coupler",
            options={
                "child_exp_options.scope": median_chi * 2,
                "child_exp_options.points": int(median_chi * 4 / 0.1),
            },
        )
        self._runner.change_regular_exec_exp_options(
            exp_name="CavityTunable_for_qubit",
            options={
                "child_exp_options.scope": median_chi * 1.5,
                "child_exp_options.points": int(median_chi * 3 / 0.1),
            },
        )

        # 更新 Q 值初始腔位置
        for bus, bus_state in self._bus_data.bus_state.items():
            self._runner.change_parallel_exec_exp_options(
                exp_name="BusQFit",
                unit=bus,
                high_power_cavity=bus_state.high_power_cavity,
                low_power_cavity=bus_state.low_power_cavity,
            )

    def _bind_physical_units(self):
        running_units = None
        if not self.experiment_options.physical_unit_map:
            bus = set()
            qubits = []
            couplers = []
            for unit, qubit in self.backend.chip_data.cache_qubit.items():
                if qubit.goodness is True:
                    bus.add(f"bus-{qubit.inst.bus}")
                    qubits.append(unit)
            for unit, coupler in self.backend.chip_data.cache_coupler.items():
                if coupler.goodness is True:
                    couplers.append(unit)
            running_units = QDict(
                bus=sorted(list(bus), key=cmp_to_key(sort_bit)),
                qubit=qubits,
                coupler=couplers,
                qubit_pair=list(self.backend.chip_data.cache_qubit_pair.keys()),
            )
        else:
            running_units = deepcopy(self.experiment_options.physical_unit_map)

        self.run_options.running_physical_unit_map = running_units

    def _build_batch(self, node: str):
        batch_class = getattr(
            batch_nodes, self._batch_config["nodes"][node]["meta"]["exp_class_name"]
        )
        batch_obj = batch_class(self.backend)
        batch_obj._label = node
        batch_obj.params_manager = self._runner.params_manager
        batch_obj.set_experiment_options(
            **self._batch_config["nodes"][node]["experiment_options"]
        )
        batch_obj.set_analysis_options(
            **self._batch_config["nodes"][node].get("analysis_options", {})
        )
        if self.experiment_options.experiment_config:
            batch_obj.set_experiment_options(
                param_path=self.experiment_options.experiment_config
            )
        if node in [
            "BatchSaturationPower",
            "BatchBusS21",
            "BatchBusCavityQ",
            "BatchBusImpaCalibrate",
            "BatchBusImpaControl",
            "BatchBusCavityCheck",
        ]:
            batch_obj.run_options.bus_data = self._bus_data
        return batch_obj

    def _run_node(self, node: str):
        if node not in self._batch_config["nodes"]:
            pyqlog.error(f"No find node: {node}")
        else:
            batch = self._build_batch(node)
            print(f"{batch} start ...")
            batch.bind_current_node(
                self.run_options.running_physical_unit_map,
                self.run_options.process_data,
            )
            batch.run()
            # batch.bind_pass_units(batch.experiment_options.physical_units)
            batch.callback_next_node(
                self.run_options.running_physical_unit_map,
                self.run_options.process_data,
            )
            self._batch_records[str(batch.record_meta)] = (
                batch.record_meta.execute_meta.result.to_dict()
            )
            if node in self._node_feedback_mapping:
                self._node_feedback_mapping.get(node)()

            pyqlog.info(f"{batch} end ...")
            # write_json_file("record.json", self._batch_records)

    def _set_node_exp_options(self, node_name, **kwargs):
        node_data = self._batch_config["nodes"][node_name]
        for k, v in kwargs.items():
            node_data["experiment_options"][k] = v

    def run(self):
        self._check_options()

        for node in self.experiment_options.nodes:
            self._run_node(node)
