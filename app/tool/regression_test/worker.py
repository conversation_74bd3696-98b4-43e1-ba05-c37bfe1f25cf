# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/04/09
# __author:       <PERSON><PERSON><PERSON>


import asyncio
import os
import re
from dataclasses import asdict, dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Union

from bson import ObjectId
from loguru import logger
from pymongo import MongoClient

import pyQCat.experiments as monster_exp_library
import pyQCat.preliminary as preliminary_exp_library
from app.tool.utils import DictComparator, read_json_file, write_json_file
from pyQCat import DEVICE
from pyQCat.analysis.algorithms import IQdiscriminator
from pyQCat.concurrent.concurrent import CONCURRENT_CACHE, check_and_start_merge_service
from pyQCat.concurrent.data_client import set_transfer_config
from pyQCat.concurrent.util import start_transfer_server_process
from pyQCat.config import PyqcatConfig
from pyQCat.executor.structures import ExperimentContext
from pyQCat.experiments.parallel_experiment import ParallelExperiment
from pyQCat.processor.topology import DiamondLattice
from pyQCat.pulse import PulseCorrection
from pyQCat.qaio_property import QAIO
from pyQCat.qm_protocol import EnvironmentBitResource
from pyQCat.qubit import NAME_PATTERN, Coupler, Qubit, QubitPair
from pyQCat.tools.record_structure import (
    ExperimentRecordMetaData,
    ExperimentStatus,
    ExperimentType,
)
from pyQCat.tools.s3storage import S3Storage
from pyQCat.tools.serialization import from_pick_binary_data

from .structure import TestCount, TestModeEnum, TestResult


def interrupt_wrapper(func):
    def wrapper(*args, **kwargs):
        _worker = args[0]

        try:
            return func(*args, **kwargs)
        except KeyboardInterrupt as e:
            pass
        except Exception as e:
            import traceback

            logger.error(str(traceback.format_exc()))
        finally:
            _worker.close()

    wrapper.__name__ = func.__name__

    return wrapper


class DataCollection:
    def __init__(
        self,
        host: str = "***********",
        port: int = 27017,
    ):
        client = MongoClient(f"mongodb://bylz:fjsaoJOIjiojj28hjj@{host}:{port}")
        db = client["UserData"]
        self.exp_record = db["ExpRecord"]
        self.user_env = db["UserEnv"]
        self.db = db
        self.client = client
        self.s3 = S3Storage()

    def find_one(self, record_id: str):
        return self.exp_record.find_one({"_id": ObjectId(record_id)})

    def find_from_query_data(self, query_data: dict):
        print(query_data)
        return self.exp_record.find(query_data)

    def find_from_query_data_random(self, query_data: dict, limit: int = 10):
        pipeline = [{"$match": query_data}, {"$sample": {"size": limit}}]
        return self.exp_record.aggregate(pipeline)

    def find_env(self, env_id: str):
        data = self.user_env.find_one({"_id": ObjectId(env_id)})
        return f"{data['sample']}_|_{data['env_name']}"


class FakeExperimentExecutor:
    def __init__(
        self, config_path: str, is_record: bool = True, is_collect: bool = False
    ):
        self._config = PyqcatConfig(config_path)

        self.db = DataCollection()
        self.output_file = None
        self.timestamp = datetime.now().strftime("%Y-%m-%d %H-%M-%S")

        if is_record is True:
            self.output_file = (
                Path(self._config.log_path)
                / "history"
                / f"comparison_results_{self.timestamp}.txt"
            )
            self.output_file.parent.mkdir(parents=True, exist_ok=True)

        self.compare = DictComparator(float_tol=0.1, output_file=self.output_file)

        self._test_mode = TestModeEnum.SEARCH
        self.test_result: Optional[TestResult] = None
        self.report_data_path = Path(self._config.log_path) / "Report" / "counter.json"

        self._filter_user = [
            "zyc",
            "job",
            "zyc_y2",
            "zyc_y3",
            "zyc_y4",
            "need",
            "zhangang",
        ]
        self._filter_env = [
            # "241118-设计验证-102bit-V2.1（Si+Nb+Ta）-Base-7#（接触式V2-Flip-15#-A1）_|_Y3"
        ]  # 只测这几个
        self._filter_exp = [
            # "Swap",
            # "LeakagePre",
            # "CPhaseTMSE",
            # "DePhaseRamseyComposite",
            # "PhotonScanReadoutFreq",
            # "PhotonScanReadoutFreqV2",
            # "PhotonNumMeasVsTime",
            # "PhotonNumMeasVsAmp",
            # "PhotonNumMeasVsFreq",
            # "AmpToPhoton",
            # "McmQubitDePhaseComposite",
            # "McmSpectatorComposite",
            # "McmSpectatorDePhaseComposite",
            # "SingleShotExtendComposite",
            # "SingleShotExtendVsSampleWidth"
        ]  # 只测这几个
        self._env_cache = {}

        QAIO.type = 72

        CONCURRENT_CACHE["open_record"] = False
        start_transfer_server_process(self._config.log_path)

        set_transfer_config(self._config.to_dict(), "token")

    @property
    def history(self) -> set:
        return self.test_result.history

    @property
    def s3(self):
        return self.db.s3

    def close(self):
        self.db.client.close()
        logger.info("关闭 Mongo 链接")

    def save_test_result(self, refresh_txt: bool = False):
        if self.report_data_path:
            self.test_result.to_json(self.report_data_path, refresh_txt=refresh_txt)

    def log(self, msg: str):
        if self.output_file:
            timestamp = datetime.now().strftime("%Y-%m-%d %H-%M-%S")
            with open(self.output_file, "a", encoding="utf-8") as f:
                f.write(f"[{timestamp}] {msg}\n")
        else:
            logger.log("FLOW", msg)

    def find_exp_record_data(self, record_id: str) -> ExperimentRecordMetaData:
        record = self.db.find_one(record_id)
        if record:
            meta_data = ExperimentRecordMetaData.from_data(record)
        else:
            meta_data = ExperimentRecordMetaData()
            meta_data.record_id = record_id
            meta_data.execute_meta.state = ExperimentStatus.EMPTY
        return meta_data

    def _get_child_experiment_data(self, meta_data: ExperimentRecordMetaData):
        child_exp_data = []
        if meta_data.execute_meta.child_record_ids:
            for cid in meta_data.execute_meta.child_record_ids:
                child_meta_data = self.find_exp_record_data(cid)
                child_exp_data.append(self._get_child_experiment_data(child_meta_data))
        else:
            env_id = meta_data.system_meta.env_id
            if meta_data.execute_meta.epd_results:
                epd_id = meta_data.execute_meta.epd_results[0]
                epd_data = self.s3.get_object("data", f"{env_id}/epd/{epd_id}")
                return from_pick_binary_data(epd_data)
            else:
                return None
        return child_exp_data

    async def _parallel_test(self, meta_data: ExperimentRecordMetaData):
        env_bit_resource = EnvironmentBitResource()

        compensate_data = meta_data.context_meta.compensates or {}
        compensate_obj_map = {}

        exp_list = []
        child_meta_data_list = []
        for child_id in meta_data.execute_meta.child_record_ids:
            child_compensate_data = {}
            child_meta_data = self.find_exp_record_data(child_id)
            if self._check_meta_data_state(child_meta_data) is False:
                self.log(f"❌ Task {child_meta_data} state no support")
                continue

            for bit_name in child_meta_data.context_meta.physical_units:
                if bit_name in compensate_data:
                    child_compensate_data[bit_name] = compensate_data[bit_name]
            child_meta_data.context_meta.compensates = child_compensate_data

            child_meta_data_list.append(child_meta_data)
            child_meta_data.context_meta.ac_bias = meta_data.context_meta.ac_bias
            child_meta_data.context_meta.crosstalk_dict = (
                meta_data.context_meta.crosstalk_dict
            )
            child_meta_data.context_meta.xy_crosstalk_dict = (
                meta_data.context_meta.xy_crosstalk_dict
            )
            child_exp = self.build_experiment(child_meta_data, self._config)
            child_exp_data = self._get_child_experiment_data(child_meta_data)
            child_exp._experiment_data = child_exp_data

            env_bit_resource.resource.update(child_exp.env_bit_resource.resource)
            exp_list.append(child_exp)
            compensate_obj_map.update(child_exp.compensates)

        if not exp_list:
            return False

        env_bit_resource.format_channel_map()
        parallel_exp = ParallelExperiment(exp_list, compensate_obj_map)
        parallel_exp.topology = DiamondLattice(18, 6, [f"q{i + 1}" for i in range(108)])
        parallel_exp.env_bit_resource = env_bit_resource
        parallel_exp.allocation_options = meta_data.context_meta.experiment_options.get(
            "allocation_options"
        )
        check_and_start_merge_service("")

        self._check_collect_meta(meta_data)
        await parallel_exp.run_experiment()

        is_diff = False
        for index, child_meta_data in enumerate(child_meta_data_list):
            child_exp = parallel_exp._experiments[index]
            if not child_exp.status.is_done():
                raise ValueError(child_exp.status.msg)
            is_diff = is_diff or self.compare.print_differences(
                child_meta_data.execute_meta.analysis_result or {},
                child_exp.record_meta.execute_meta.analysis_result,
                name=f"{child_meta_data.record_id} | {str(child_exp)}",
            )

        return is_diff

    async def _top_test(self, meta_data: ExperimentRecordMetaData):
        experiment_data = self._get_child_experiment_data(meta_data)
        experiment = self.build_experiment(meta_data, self._config)
        experiment._experiment_data = experiment_data
        self._check_collect_meta(meta_data)
        await experiment.run_experiment()
        if not experiment.status.is_done():
            raise ValueError(experiment.status.msg)
        return self.compare.print_differences(
            meta_data.execute_meta.analysis_result,
            experiment.record_meta.execute_meta.analysis_result,
            name=f"{meta_data.record_id} | {str(experiment)}",
        )

    async def _compensate_test(self, meta_data: ExperimentRecordMetaData):
        experiment = self.build_experiment(meta_data, self._config)
        child_exp_data = self._get_child_experiment_data(meta_data)
        experiment._experiment_data = child_exp_data
        self._check_collect_meta(meta_data)
        await experiment.run_experiment()
        if not experiment.status.is_done():
            raise ValueError(experiment.status.msg)
        return self.compare.print_differences(
            meta_data.execute_meta.analysis_result,
            experiment.record_meta.execute_meta.analysis_result,
            name=f"{meta_data.record_id} | {str(experiment)}",
        )

    @staticmethod
    def build_experiment(meta_data: ExperimentRecordMetaData, config: PyqcatConfig):
        experiment_context = ExperimentContext()
        env_bit_resource = EnvironmentBitResource()
        unit_name_to_obj = {}

        for (
            bit_name,
            bit_data,
        ) in meta_data.context_meta.physical_units.items():
            if re.match(NAME_PATTERN.qubit, bit_name):
                bit_obj = Qubit.from_dict(bit_data)
                env_bit_resource.resource[bit_name] = dict(
                    bit=bit_data.get("bit"),
                    name=bit_data.get("name"),
                    xy_channel=bit_data.get("xy_channel"),
                    z_dc_channel=bit_data.get("z_dc_channel"),
                    z_flux_channel=bit_data.get("z_flux_channel"),
                    readout_channel=bit_data.get("readout_channel"),
                    bus=bit_data.get("inst").get("bus"),
                    xy_lo=bit_data.get("inst").get("xy_lo"),
                    m_lo=bit_data.get("inst").get("m_lo"),
                    row=bit_data.get("_row"),
                    col=bit_data.get("_col"),
                )
                experiment_context.qubits.append(bit_obj)
            elif re.match(NAME_PATTERN.coupler, bit_name):
                bit_obj = Coupler.from_dict(bit_data)
                env_bit_resource.resource[bit_name] = dict(
                    bit=bit_data.get("bit"),
                    name=bit_data.get("name"),
                    z_dc_channel=bit_data.get("z_dc_channel"),
                    z_flux_channel=bit_data.get("z_flux_channel"),
                )
                experiment_context.couplers.append(bit_obj)
            elif re.match(NAME_PATTERN.qubit_pair, bit_name):
                bit_obj = QubitPair.from_dict(bit_data)
                experiment_context.qubit_pair.append(bit_obj)
            else:
                raise ValueError(f"No support {bit_name}")
            unit_name_to_obj[bit_name] = bit_obj

        # set context name and unit map
        experiment_context.context_name = meta_data.context_meta.context_name
        if meta_data.context_meta.unit_map:
            for name, bit_name in meta_data.context_meta.unit_map.items():
                experiment_context.unit_map[name] = unit_name_to_obj[bit_name]

        # bind compensate
        compensate_data = meta_data.context_meta.compensates
        if compensate_data:
            compensates = {}
            for bit_name, pulse_correct_data in compensate_data.items():
                bit_obj = unit_name_to_obj.get(bit_name)
                if not bit_obj:
                    if re.match(NAME_PATTERN.qubit, bit_name):
                        bit_obj = Qubit(bit=int(bit_name[1:]) - 1, name=bit_name)
                    else:
                        bit_obj = Coupler(bit=0, name=bit_name)
                compensates[bit_obj] = PulseCorrection.from_dict(
                    pulse_correct_data, bit_name
                )
            experiment_context.compensates = compensates

        # bind dcm
        dcm_data = meta_data.context_meta.dcms
        if dcm_data:
            dcm_list = []
            for bit_name, _ in dcm_data.items():
                dcm = IQdiscriminator(I_list=[], Q_list=[], name=bit_name)
                dcm._fidelity = [0.95, 0.90]
                dcm._k_recommend = 2
                dcm_list.append(dcm)
            if len(dcm_list) == 1:
                experiment_context.discriminators = dcm_list[0]
            else:
                experiment_context.discriminators = dcm_list

        experiment_context.config = config
        experiment_context.ac_bias = meta_data.context_meta.ac_bias
        experiment_context.crosstalk_dict = meta_data.context_meta.crosstalk_dict
        experiment_context.xy_crosstalk_dict = meta_data.context_meta.xy_crosstalk_dict
        env_bit_resource.format_channel_map()
        experiment_context.env_bit_resource = env_bit_resource

        exp_name = meta_data.execute_meta.exp_class
        exp_cls = getattr(monster_exp_library, exp_name, None) or getattr(
            preliminary_exp_library, exp_name, None
        )
        experiment = exp_cls.from_experiment_context(experiment_context)
        experiment.experiment_options.update(meta_data.context_meta.experiment_options)
        experiment.analysis_options.update(meta_data.context_meta.analysis_options)
        experiment.set_experiment_options(use_simulator=True)
        experiment.set_analysis_options(is_plot=False, save_exp_data=False)

        if experiment.EXP_TYPE == ExperimentType.COMP:
            experiment.analysis_options.child_ana_options.save_exp_data = False

        return experiment

    def _check_collect_meta(self, meta_data: ExperimentRecordMetaData):
        pass

    def _is_filter_env(self, meta_data: ExperimentRecordMetaData) -> bool:
        if self._filter_env:
            env_id = meta_data.system_meta.env_id
            if env_id not in self._env_cache:
                self._env_cache[env_id] = self.db.find_env(env_id)
            return self._env_cache[env_id] in self._filter_env
        else:
            return True

    def _is_filter_user(self, meta_data: ExperimentRecordMetaData) -> bool:
        return meta_data.system_meta.username in self._filter_user

    def _is_filter_exp(self, meta_data: ExperimentRecordMetaData) -> bool:
        if self._filter_exp:
            env_class = meta_data.execute_meta.exp_class
            return env_class in self._filter_exp
        return True

    def _check_exist(self, meta_data: ExperimentRecordMetaData) -> bool:
        meta_data_str = str(meta_data)
        return self._test_mode == TestModeEnum.SEARCH and (
            meta_data_str in self.test_result.task.success
            or meta_data_str in self.test_result.task.different
            or meta_data_str in self.test_result.task.fail
        )

    async def _test_from_record_meta_data(
        self, meta_data: ExperimentRecordMetaData, date: str
    ):
        try:
            if self._check_exist(meta_data):
                logger.warning(f"{meta_data} is repeat ...")
                return

            if self._check_meta_data_state(meta_data) is False:
                return

            if self._is_filter_user(meta_data):
                logger.warning(f"{meta_data} belongs to the blacklist user task ...")
                return

            if not self._is_filter_env(meta_data):
                logger.warning(f"{meta_data} not in filter env ...")
                return

            if not self._is_filter_exp(meta_data):
                logger.warning(f"{meta_data} in filter exp ...")
                return

            self.log(f"🧵 Start Test Task {meta_data}")

            if meta_data.execute_meta.exp_type == ExperimentType.PARALLEL:
                is_diff = await self._parallel_test(meta_data)
            elif meta_data.execute_meta.parallel_id:
                self.log(
                    f"Task {meta_data} has parallel group, jump to parallel experiment"
                )
                return await self._test_from_customer_set(
                    meta_data.execute_meta.parallel_id
                )
            elif meta_data.execute_meta.parent_id:
                self.log(
                    f"Task  {meta_data} has parent experiment, jump to compensate experiment"
                )
                return await self._test_from_customer_set(
                    meta_data.execute_meta.parent_id
                )
            elif meta_data.execute_meta.exp_type == ExperimentType.COMP:
                is_diff = await self._compensate_test(meta_data)
            elif meta_data.execute_meta.exp_type == ExperimentType.TOP:
                is_diff = await self._top_test(meta_data)
        except Exception:
            import traceback

            self.log(f"❌ Task  {meta_data} Test Fail: {traceback.format_exc()}\n")
            self.test_result.set_fail(meta_data, date)
        else:
            if is_diff is True:
                self.log(f"👀 Task {meta_data} Test SUC, but some different\n")
                self.test_result.set_different(meta_data, date)
            else:
                self.log(f"✅ Task {meta_data} Test SUC, all same\n")
                self.test_result.set_success(meta_data, date)
        self.save_test_result()

    def _check_meta_data_state(self, meta_data):
        if meta_data.execute_meta.state == ExperimentStatus.EMPTY:
            raise ValueError(f"{meta_data} is not find in database ...")
        elif meta_data.execute_meta.state != ExperimentStatus.DONE:
            logger.warning(
                f"{meta_data} state is not 3 | {meta_data.execute_meta.state} ..."
            )
            return False
        return True

    async def _test_from_customer_set(self, record_ids: Union[str, List[str]]):
        if isinstance(record_ids, str):
            record_ids = [record_ids]

        for rid in record_ids:
            meta_data = self.find_exp_record_data(rid)
            await self._test_from_record_meta_data(meta_data)

    async def _test_from_query_filed(self, query_data: Dict):
        records = self.db.find_from_query_data(query_data)
        await self._test_from_record_meta_data(records)

    async def _test_from_query_results(self, records: list):
        switch_path = self.report_data_path.parent / "switch.txt"
        for data in records:
            state = switch_path.read_text(encoding="utf-8").splitlines()
            if state and state[0] == "0":
                return False
            meta_data = ExperimentRecordMetaData.from_data(data)
            await self._test_from_record_meta_data(meta_data, date=data["date"])
        return True

    @interrupt_wrapper
    def regression_test(
        self,
        mode: TestModeEnum = TestModeEnum.SEARCH,
        head_date: str = "2025-07-01",
        **kwargs,
    ):
        self._test_mode = mode
        self._generate_report_data()
        self.test_result.play_tag(mode)

        if mode == TestModeEnum.SEARCH:
            self._regression_search_test(head_date)
        elif mode == TestModeEnum.CHECK:
            self._regression_check_test()
        elif mode == TestModeEnum.REGRESSION:
            self._regression_repeat_test()
        elif mode == TestModeEnum.CUSTOM:
            self._regression_custom_test(**kwargs)

    def _generate_report_data(self):
        if not self.report_data_path.exists():
            self.test_result = TestResult()
            self.save_test_result()
        self.test_result = TestResult.from_json(self.report_data_path)

    def _regression_search_test(self, head_date: str):
        test_date = datetime.now() - timedelta(days=1)

        while True:
            if test_date.strftime("%Y-%m-%d") < head_date:
                test_date = datetime.now() - timedelta(days=1)

            test_data_str = test_date.strftime("%Y-%m-%d")

            if test_data_str not in self.test_result.finish_date:
                result = list(
                    self.db.find_from_query_data(
                        dict(is_root=True, device=DEVICE, date=test_data_str)
                    )
                )
                if result:
                    state = asyncio.run(self._test_from_query_results(result))
                    if state is True:
                        self.test_result.finish_date.append(test_data_str)
                        self.save_test_result()
                    else:
                        self.save_test_result()
                        break
            else:
                logger.warning(f"{test_data_str} have check ...")

            test_date -= timedelta(days=1)

    def _regression_check_test(self):
        results = []
        for meta_str in self.test_result.task.fail:
            record_id = meta_str.split(" | ")[1]
            results.append(self.db.find_one(record_id))
        if results:
            asyncio.run(self._test_from_query_results(results))
            self.save_test_result(refresh_txt=True)

    def _regression_repeat_test(self):
        results = []
        for meta_str in self.test_result.task.success:
            record_id = meta_str.split(" | ")[1]
            results.append(self.db.find_one(record_id))
        for meta_str in self.test_result.task.different:
            record_id = meta_str.split(" | ")[1]
            results.append(self.db.find_one(record_id))
        if results:
            asyncio.run(self._test_from_query_results(results))
            self.save_test_result(refresh_txt=True)

    def _regression_custom_test(self, **kwargs):
        _id = kwargs.get("_id")
        if _id:
            results = [self.db.find_one(_id)]
        else:
            results = list(self.db.find_from_query_data(dict(**kwargs)))

        if results:
            asyncio.run(self._test_from_query_results(results))
            self.save_test_result(refresh_txt=True)
