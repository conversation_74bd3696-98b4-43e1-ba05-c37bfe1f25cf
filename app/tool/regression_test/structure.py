# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/08/06
# __author:       <PERSON><PERSON><PERSON>

import json
from dataclasses import asdict, dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, List, Set, Union

from loguru import logger

from pyQCat import DEVICE, get_version


class TestModeEnum(str, Enum):
    SEARCH = "search"
    CHECK = "check"
    REGRESSION = "regression"
    CUSTOM = "custom"


@dataclass
class TestCount:
    success: int = 0
    different: int = 0
    fail: int = 0


@dataclass
class TestTask:
    success: Set[str] = field(default_factory=set)
    different: Set[str] = field(default_factory=set)
    fail: Set[str] = field(default_factory=set)
    result_path: Path = None


@dataclass
class TestHistory:
    version: str = ""
    device: str = DEVICE
    success: int = 0
    different: int = 0
    fail: int = 0
    time: str = datetime.now().strftime("%Y-%m-%d %H-%M-%S")
    mode: TestModeEnum = TestModeEnum.SEARCH


@dataclass
class TestResult:
    task: TestTask = field(default_factory=TestTask)
    experiment: Dict[str, TestCount] = field(default_factory=dict)
    date: Dict[str, TestCount] = field(default_factory=dict)
    env: Dict[str, TestCount] = field(default_factory=dict)
    history: List[TestHistory] = field(default_factory=list)
    finish_date: List[str] = field(default_factory=list)

    def meta_exist(self, meta_data_str: str):
        return (
            meta_data_str in self.task.success
            or meta_data_str in self.task.different
            or meta_data_str in self.task.fail
        )

    def _set_count(self, meta_data, date, state: str):
        meta_data_str = str(meta_data)
        exp_class_name = meta_data.execute_meta.exp_class
        env_id = meta_data.system_meta.env_id
        experiment_test_count = self.experiment.setdefault(exp_class_name, TestCount())
        env_test_count = self.env.setdefault(env_id, TestCount())
        date_test_count = self.date.setdefault(date, TestCount())
        cur_history = self.history[-1]
        setattr(cur_history, state, getattr(cur_history, state) + 1)

        if cur_history.mode == TestModeEnum.CUSTOM:
            if self.meta_exist(meta_data_str):
                mode = TestModeEnum.CHECK
            else:
                mode = TestModeEnum.SEARCH
        else:
            mode = cur_history.mode

        if mode == TestModeEnum.SEARCH:
            setattr(
                experiment_test_count, state, getattr(experiment_test_count, state) + 1
            )
            setattr(env_test_count, state, getattr(env_test_count, state) + 1)
            setattr(date_test_count, state, getattr(date_test_count, state) + 1)
            getattr(self.task, state).add(meta_data_str)

            txt_path = self.task.result_path / f"{state}.txt"
            with txt_path.open("a", encoding="utf-8") as f:
                f.write(f"{meta_data_str}\n")
        else:
            origin_state = ""
            for key in ["success", "different", "fail"]:
                if meta_data_str in getattr(self.task, key):
                    origin_state = key
                    break
            if origin_state != state:
                logger.info(f"{meta_data_str} from `{origin_state}` to `{state}`")
                setattr(
                    experiment_test_count,
                    state,
                    getattr(experiment_test_count, state) + 1,
                )
                setattr(env_test_count, state, getattr(env_test_count, state) + 1)
                setattr(date_test_count, state, getattr(date_test_count, state) + 1)
                setattr(
                    experiment_test_count,
                    origin_state,
                    getattr(experiment_test_count, origin_state) - 1,
                )
                setattr(
                    env_test_count,
                    origin_state,
                    getattr(env_test_count, origin_state) - 1,
                )
                setattr(
                    date_test_count,
                    origin_state,
                    getattr(date_test_count, origin_state) - 1,
                )
                getattr(self.task, origin_state).remove(meta_data_str)
                getattr(self.task, state).add(meta_data_str)

    def set_fail(self, meta_data, date):
        self._set_count(meta_data, date, "fail")

    def set_different(self, meta_data, date):
        self._set_count(meta_data, date, "different")

    def set_success(self, meta_data, date):
        self._set_count(meta_data, date, "success")

    def play_tag(self, mode: TestModeEnum):
        self.history.append(TestHistory(version=get_version(), mode=mode))

    def date_exist(self, date: str):
        return date in self.date

    def to_json(
        self,
        json_path: Union[Path, str] = "test_data2.json",
        refresh_txt: bool = False,
        indent: int = 4,
    ) -> None:
        if isinstance(json_path, str):
            json_path = Path(json_path)
        json_path.parent.mkdir(parents=True, exist_ok=True)

        data = asdict(self)
        data.pop("task")

        with open(json_path, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=indent)

        if refresh_txt is True:
            for key in ["success", "different", "fail"]:
                txt_path = json_path.parent / f"{key}.txt"
                txt_path.write_text(
                    "\n".join(list(getattr(self.task, key))), encoding="utf-8"
                )

    @classmethod
    def from_json(cls, json_path: Union[str, Path]) -> "TestResult":
        with open(json_path, "r", encoding="utf-8") as f:
            data = json.load(f)

        obj = cls(
            experiment={k: TestCount(**v) for k, v in data.get("experiment").items()},
            date={k: TestCount(**v) for k, v in data.get("date").items()},
            env={k: TestCount(**v) for k, v in data.get("env").items()},
            history=[TestHistory(**v) for v in data.get("history")],
            finish_date=data.get("finish_date"),
        )
        obj.task.result_path = Path(json_path).parent

        for key in ["success", "different", "fail"]:
            txt_path = obj.task.result_path / f"{key}.txt"
            txt_path.touch(exist_ok=True)
            setattr(
                obj.task, key, set(txt_path.read_text(encoding="utf-8").splitlines())
            )

        return obj


if __name__ == "__main__":
    data = TestResult.from_json("/home/<USER>/project/pyqcat-apps/test_data.json")
    print(data.task)
