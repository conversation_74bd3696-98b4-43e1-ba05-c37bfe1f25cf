import json

def get_report_data():
    file_path = "/home/<USER>/project/pyqcat-apps/.settings/log/regression_test/Report/counter.json"
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            data = json.load(file)
            return data
    except FileNotFoundError:
        print(f"file {file_path} no find!")
    except json.JSONDecodeError:
        print(f"file {file_path} is not a valid JSON format!")
