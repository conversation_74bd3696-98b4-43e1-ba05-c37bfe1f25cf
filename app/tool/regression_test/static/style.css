:root {
    --primary: #3498db;
    --secondary: #2c3e50;
    --success: #2ecc71;
    --warning: #f39c12;
    --danger: #e74c3c;
    --light: #ecf0f1;
    --dark: #34495e;
    --bg: #f8f9fa;
    --card-bg: #ffffff;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: var(--bg);
    color: var(--dark);
    line-height: 1.6;
    padding: 20px;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
}

header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, var(--secondary), var(--primary));
    color: white;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.card {
    background: var(--card-bg);
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    padding: 25px;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.card-title {
    font-size: 1.4rem;
    color: var(--secondary);
    font-weight: 600;
}

.card-icon {
    font-size: 1.8rem;
    color: var(--primary);
}

.stats-container {
    display: flex;
    justify-content: space-around;
    text-align: center;
}

.stat {
    padding: 10px;
}

.stat-value {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.success .stat-value {
    color: var(--success);
}

.different .stat-value {
    color: var(--warning);
}

.fail .stat-value {
    color: var(--danger);
}

.stat-label {
    font-size: 1rem;
    color: #777;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.charts-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.chart-container {
    background: var(--card-bg);
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.chart-header {
    margin-bottom: 20px;
    text-align: center;
    color: var(--secondary);
    font-size: 1.3rem;
}

.tabs {
    display: flex;
    margin-bottom: 20px;
    background: var(--light);
    border-radius: 8px;
    overflow: hidden;
}

.tab {
    flex: 1;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
}

.tab.active {
    background: var(--primary);
    color: white;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 30px;
    background: var(--card-bg);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

th, td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

th {
    background-color: var(--primary);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

tr:nth-child(even) {
    background-color: rgba(236, 240, 241, 0.3);
}

tr:hover {
    background-color: rgba(52, 152, 219, 0.1);
}

.success-cell {
    color: var(--success);
    font-weight: 600;
}

.different-cell {
    color: var(--warning);
    font-weight: 600;
}

.fail-cell {
    color: var(--danger);
    font-weight: 600;
}

.search-box {
    margin: 20px 0;
    padding: 15px;
    background: var(--card-bg);
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.search-box input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.footer {
    text-align: center;
    padding: 20px;
    margin-top: 30px;
    color: #777;
    border-top: 1px solid #eee;
}

@media (max-width: 768px) {
    .charts-row {
        grid-template-columns: 1fr;
    }
    
    .summary-cards {
        grid-template-columns: 1fr;
    }
}
