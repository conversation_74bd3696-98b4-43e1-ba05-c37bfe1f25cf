from flask import Flask, render_template
from data import get_report_data

app = Flask(__name__)

@app.route('/')
def report():
    data = get_report_data()
    
    # 计算总体统计
    total_success = 0
    total_different = 0
    total_fail = 0
    
    # 计算实验总数
    for exp in data['experiment'].values():
        total_success += exp['success'] if exp['success'] > 0 else 0
        total_different += exp['different'] if exp['different'] > 0 else 0
        total_fail += exp['fail'] if exp['fail'] > 0 else 0
    
    # 准备图表数据
    daily_labels = list(data['date'].keys())
    daily_success = [day['success'] for day in data['date'].values()]
    daily_different = [day['different'] for day in data['date'].values()]
    daily_fail = [day['fail'] for day in data['date'].values()]
    
    # 准备饼图数据
    distribution_data = [total_success, total_different, total_fail]
    
    # 计算成功率
    def calculate_success_rate(success, different, fail):
        total = success + different + fail
        if total <= 0:
            return 0.0
        return round(success / total * 100, 2)
    
    # 为实验添加成功率
    for exp_name, exp_data in data['experiment'].items():
        exp_data['success_rate'] = calculate_success_rate(
            exp_data['success'], exp_data['different'], exp_data['fail']
        )
    
    # 为日期添加成功率
    for date, date_data in data['date'].items():
        date_data['success_rate'] = calculate_success_rate(
            date_data['success'], date_data['different'], date_data['fail']
        )
    
    # 为环境添加成功率
    for env_id, env_data in data['env'].items():
        env_data['success_rate'] = calculate_success_rate(
            env_data['success'], env_data['different'], env_data['fail']
        )
    
    return render_template(
        'report.html',
        data=data,
        total_success=total_success,
        total_different=total_different,
        total_fail=total_fail,
        daily_labels=daily_labels,
        daily_success=daily_success,
        daily_different=daily_different,
        daily_fail=daily_fail,
        distribution_data=distribution_data,
        num_dates=len(data['date']),
        num_envs=len(data['env']),
        num_experiments=len(data['experiment']),
        num_history=len(data['history']),
        current_version="0.23.2.2",
        device="B",
        current_date="2025年8月7日"
    )

if __name__ == '__main__':
    app.run(debug=True)
