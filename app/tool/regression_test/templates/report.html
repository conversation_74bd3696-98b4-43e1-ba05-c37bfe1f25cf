<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量子测控实验回归测试报告</title>
    <script src="https://cdn.bootcdn.net/ajax/libs/Chart.js/4.4.0/chart.umd.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-microchip"></i>量子测控实验回归测试报告</h1>
            <p>2025年8月1日 - 2025年8月7日</p>
        </header>
        
        <div class="summary-cards">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">总体统计</h2>
                    <div class="card-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                </div>
                <div class="stats-container">
                    <div class="stat success">
                        <div class="stat-value">{{ total_success }}</div>
                        <div class="stat-label">成功</div>
                    </div>
                    <div class="stat different">
                        <div class="stat-value">{{ total_different }}</div>
                        <div class="stat-label">不同</div>
                    </div>
                    <div class="stat fail">
                        <div class="stat-value">{{ total_fail }}</div>
                        <div class="stat-label">失败</div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">完成日期</h2>
                    <div class="card-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                </div>
                <div class="stats-container">
                    <div class="stat">
                        <div class="stat-value">{{ num_dates }}</div>
                        <div class="stat-label">日期</div>
                    </div>
                    <div class="stat">
                        <div class="stat-value">{{ num_envs }}</div>
                        <div class="stat-label">环境</div>
                    </div>
                    <div class="stat">
                        <div class="stat-value">{{ num_experiments }}</div>
                        <div class="stat-label">实验类型</div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">测试历史</h2>
                    <div class="card-icon">
                        <i class="fas fa-history"></i>
                    </div>
                </div>
                <div class="stats-container">
                    <div class="stat">
                        <div class="stat-value">{{ num_history }}</div>
                        <div class="stat-label">测试记录</div>
                    </div>
                    <div class="stat">
                        <div class="stat-value">{{ current_version }}</div>
                        <div class="stat-label">当前版本</div>
                    </div>
                    <div class="stat">
                        <div class="stat-value">{{ device }}</div>
                        <div class="stat-label">设备</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="charts-row">
            <div class="chart-container">
                <div class="chart-header">每日测试结果趋势</div>
                <canvas id="dailyChart"></canvas>
            </div>
            
            <div class="chart-container">
                <div class="chart-header">测试结果分布</div>
                <canvas id="distributionChart"></canvas>
            </div>
        </div>
        
        <div class="tabs">
            <div class="tab active" data-tab="experiment">实验统计</div>
            <div class="tab" data-tab="date">日期统计</div>
            <div class="tab" data-tab="env">环境统计</div>
            <div class="tab" data-tab="history">历史记录</div>
        </div>
        
        <div class="search-box">
            <input type="text" id="searchInput" placeholder="搜索实验名称、日期、环境ID或测试模式...">
        </div>
        
        <div class="tab-content active" id="experiment-tab">
            <table>
                <thead>
                    <tr>
                        <th>实验名称</th>
                        <th>成功</th>
                        <th>不同</th>
                        <th>失败</th>
                        <th>成功率</th>
                    </tr>
                </thead>
                <tbody>
                    {% for exp_name, exp_data in data.experiment.items() %}
                    <tr>
                        <td>{{ exp_name }}</td>
                        <td class="success-cell">{{ exp_data.success }}</td>
                        <td class="different-cell">{{ exp_data.different }}</td>
                        <td class="fail-cell">{{ exp_data.fail }}</td>
                        <td>{{ exp_data.success_rate }}%</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <div class="tab-content" id="date-tab">
            <table>
                <thead>
                    <tr>
                        <th>日期</th>
                        <th>成功</th>
                        <th>不同</th>
                        <th>失败</th>
                        <th>成功率</th>
                    </tr>
                </thead>
                <tbody>
                    {% for date, date_data in data.date.items() %}
                    <tr>
                        <td>{{ date }}</td>
                        <td class="success-cell">{{ date_data.success }}</td>
                        <td class="different-cell">{{ date_data.different }}</td>
                        <td class="fail-cell">{{ date_data.fail }}</td>
                        <td>{{ date_data.success_rate }}%</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <div class="tab-content" id="env-tab">
            <table>
                <thead>
                    <tr>
                        <th>环境ID</th>
                        <th>成功</th>
                        <th>不同</th>
                        <th>失败</th>
                        <th>成功率</th>
                    </tr>
                </thead>
                <tbody>
                    {% for env_id, env_data in data.env.items() %}
                    <tr>
                        <td>{{ env_id }}</td>
                        <td class="success-cell">{{ env_data.success }}</td>
                        <td class="different-cell">{{ env_data.different }}</td>
                        <td class="fail-cell">{{ env_data.fail }}</td>
                        <td>{{ env_data.success_rate }}%</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <div class="tab-content" id="history-tab">
            <table>
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>版本</th>
                        <th>设备</th>
                        <th>成功</th>
                        <th>不同</th>
                        <th>失败</th>
                        <th>模式</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in data.history %}
                    <tr>
                        <td>{{ record.time }}</td>
                        <td>{{ record.version }}</td>
                        <td>{{ record.device }}</td>
                        <td class="success-cell">{{ record.success }}</td>
                        <td class="different-cell">{{ record.different }}</td>
                        <td class="fail-cell">{{ record.fail }}</td>
                        <td>{{ record.mode }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <div class="footer">
            <p>量子计算测试报告 - 生成于 {{ current_date }}</p>
            <p>© 2025 量子计算实验室 | 版本 {{ current_version }}</p>
        </div>
    </div>
    
    <script>

        // 标签切换功能
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                // 移除所有活动标签和内容
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
                
                // 激活当前标签
                tab.classList.add('active');
                const tabId = tab.getAttribute('data-tab');
                document.getElementById(`${tabId}-tab`).classList.add('active');
            });
        });
        
        // 搜索功能
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const activeTab = document.querySelector('.tab-content.active');
            const rows = activeTab.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });

        document.addEventListener('DOMContentLoaded', function() {
            if (typeof Chart === 'undefined') {
                console.error('Chart.js 未加载');
                return;
            }

            // 每日测试图表
            const dailyCtx = document.getElementById('dailyChart');
            if (dailyCtx) {
                new Chart(dailyCtx.getContext('2d'), {
                    type: 'line',
                    data: {
                        labels: JSON.parse('{{ daily_labels | tojson | safe }}'),
                        datasets: [
                            {
                                label: '成功',
                                data: JSON.parse('{{ daily_success | tojson | safe }}'),
                                borderColor: '#2ecc71',
                                backgroundColor: 'rgba(46, 204, 113, 0.1)',
                                tension: 0.3,
                                fill: true
                            },
                            {
                                label: '不同',
                                data: JSON.parse('{{ daily_different | tojson | safe }}'),
                                borderColor: '#f39c12',
                                backgroundColor: 'rgba(243, 156, 18, 0.1)',
                                tension: 0.3,
                                fill: true
                            },
                            {
                                label: '失败',
                                data: JSON.parse('{{ daily_fail | tojson | safe }}'),
                                borderColor: '#e74c3c',
                                backgroundColor: 'rgba(231, 76, 60, 0.1)',
                                tension: 0.3,
                                fill: true
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: { position: 'top' }
                        },
                        scales: {
                            y: { beginAtZero: true }
                        }
                    }
                });
            }

            // 分布图
            const distCtx = document.getElementById('distributionChart');
            if (distCtx) {
                new Chart(distCtx.getContext('2d'), {
                    type: 'doughnut',
                    data: {
                        labels: ['成功', '不同', '失败'],
                        datasets: [{
                            data: JSON.parse('{{ distribution_data | tojson | safe }}'),
                            backgroundColor: ['#2ecc71', '#f39c12', '#e74c3c'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: { position: 'top' },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const value = context.raw || 0;
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = Math.round((value / total) * 100);
                                        return `${context.label}: ${value} (${percentage}%)`;
                                    }
                                }
                            }
                        }
                    }
                });
            }
        });
    </script>
</body>
</html>
