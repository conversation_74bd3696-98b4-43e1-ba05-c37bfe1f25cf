# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2025/03/20
# __author:       <PERSON><PERSON><PERSON>

import os
from app.config import init_backend
from pyQCat.qaio_property import QA<PERSON>
from pyQCat.tools.document_translate.translate import translate_experiment_document


def main(exp_id_list):
    QAIO.type = 72
    init_backend()

    if isinstance(exp_id_list, str):
        exp_id_list = [exp_id_list]

    program_path = os.path.join(os.path.dirname(__file__), "program")
    os.makedirs(program_path, exist_ok=True)

    for exp_id in exp_id_list:
        translate_experiment_document(exp_id, save_path=program_path)


if __name__ == "__main__":
    main(
        ["dc79ffea-40b2-4c3e-82d5-8602924844d2"]
    )
