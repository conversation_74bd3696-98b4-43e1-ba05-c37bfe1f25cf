# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2024/07/25
# __author:       <PERSON><PERSON><PERSON>

from pathlib import Path

import pyQCat.analysis as ana_libs
from pyQCat.concurrent.worker.analysis_interface import run_analysis_process
from pyQCat.structures import ExperimentData, QDict
from pyQCat.tools import format_results
from pyQCat.tools.serialization import from_pick_binary_data


class AnalysisTest:
    def __init__(self) -> None:
        self._options = QDict(
            ana_cls_name=None,
            analysis_options=QDict(pure_exp_mode=False),
            experiment_data=None,
            epd_path="",
            save_path=r"app\analysis_test\StandardResult",
        )

    @property
    def options(self):
        return self._options

    def run(self):
        if not (self.options.experiment_data or self.options.epd_path):
            raise ValueError("Please set experiment data!")

        if not self.options.experiment_data:
            experiment_data: ExperimentData = from_pick_binary_data(
                self.options.epd_path
            )
            self.options.experiment_data = experiment_data
            self.options.save_path = str(Path(self.options.epd_path).parent)

        analysis_class_name = (
            self.options.experiment_data.metadata.process_meta.get("ana_class_name")
            or self.options.ana_cls_name
        )

        if not analysis_class_name:
            raise ValueError("Please set analysis cls")

        meta_analysis_options = self.options.experiment_data.metadata.process_meta.get(
            "analysis_options", QDict()
        )
        meta_analysis_options.update(self.options.analysis_options.to_dict())

        if isinstance(analysis_class_name, str):
            ana_cls = getattr(ana_libs, analysis_class_name)
        else:
            ana_cls = analysis_class_name
        ana_cls_name = ana_cls.__name__
        ana = run_analysis_process(
            analysis_class=ana_cls,
            experiment_data=self.options.experiment_data,
            analysis_options=meta_analysis_options,
        )

        print(format_results(ana))
        if ana.options.is_plot is True:
            if isinstance(ana.drawer.figure, list):
                for i, fig in enumerate(ana.drawer.figure):
                    png_path = str(
                        Path(
                            self.options.save_path,
                            f"{ana_cls_name}-{experiment_data}-{i}.png",
                        )
                    )
                    fig.savefig(png_path)
            else:
                png_path = str(
                    Path(
                        self.options.save_path,
                        f"{ana_cls_name}-{experiment_data}.png",
                    )
                )
                ana.drawer.figure.savefig(png_path)
            print(f"Result Figure Save In: {png_path}")


def all_test():
    import os

    directory = r"E:\Y4Online\TempData\SingleShot_01"
    error_file = []

    for root, dirs, files in os.walk(directory):
        for name in files:
            if name.endswith(".epd"):
                file_path = os.path.join(root, name)
                try:
                    test = AnalysisTest()
                    test.options.ana_cls_name = "SingleShotAnalysis"
                    test.options.epd_path = file_path
                    test.run()
                except Exception:
                    error_file.append(file_path)

    for f in error_file:
        print(f)


if __name__ == "__main__":

    test = AnalysisTest()
    # test.options.ana_cls_name = "BusCavityAnalysis"
    test.options.epd_path = r"app/analysis_test/FindBusCavityFreq.epd"
    test.options.analysis_options.update(
        dict()
    )
    test.run()

    # all_test()
