# -*- coding: utf-8 -*-

# This code is part of pyQCat.
#
# Copyright (c) 2017-2030 Origin Quantum Computing. All Right Reserved.
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

# __date:         2023/10/19
# __author:       <PERSON><PERSON><PERSON>

import json
import sys
from pathlib import Path

from pydantic_settings import BaseSettings, SettingsConfigDict

from pyQCat.concurrent.util import start_transfer_server_process
from pyQCat.config import PyqcatConfig
from pyQCat.executor import Backend
from pyQCat.log import pyqlog
from pyQCat.structures import QDict

CONFIG_MANAGER_PATH = Path(__file__).resolve().parent.parent / ".context" / "env.json"
JSON_PATH = Path(__file__).resolve().parent / "batch_test" / "json_data"
DEFAULT_ENV_BITS = []
DEFAULT_MAX_POINT_BITS = []
DEFAULT_F12_OPT_BITS = []
DEFAULT_ONLINE_BITS = []
DEFAULT_CUSTOM_POINTS = {}
DEFAULT_READ_BITS = {}

SYSTEM_PARA = QDict(
    backend=QDict(
        username="simulator",
        password="123456",
        config_file="config.conf",
        use_cache=True,
    ),
    context=QDict(
        env_bits=DEFAULT_ENV_BITS,
        working_type="awg_bias",
        divide_type="calibrate_idle_point",
        max_point_unit=DEFAULT_MAX_POINT_BITS,
        online_unit=DEFAULT_ONLINE_BITS,
        f12_opt_bits=DEFAULT_F12_OPT_BITS,
        custom_points=DEFAULT_CUSTOM_POINTS,
        add_read_env_bits=DEFAULT_READ_BITS,
        custom=False,
        online=False,
        crosstalk=False,
        xy_crosstalk=False,
    ),
    parallel_divide=QDict(
        ParallelAllocationQC=QDict(
            allocation_policy="normal",
            mode="blame9",
            neighbor_limit=300,
            anharmonic=240,
            freq_split=50,
            freq_delta=10,
            max_limit=300,
            xy_cross_threshold_low=0.01,
            xy_cross_threshold_high=0.05,
            iter_times=4,
            check_xy_lo=True,
            xy_cross_coefficient_map=None,
            intermediate_freq_allocate=True,
        ),
        ParallelAllocationCGC=QDict(
            allocation_policy="normal", mode="horse", distance_limit=2
        ),
        ParallelAllocationCC=QDict(allocation_policy="normal", mode="recursive"),
        IntermediateFreqAllocation=QDict(
            intermediate_freq_accuracy=10,
            expect_intermediate_freq=1050,
            goal_gap=None,
            is_force=False,
        ),
        ReadoutAmpAllocation=QDict(standard_amp=0.15, set_index=False, sample_rate=3.2),
    ),
)


def init_backend(system=None):
    system = system or SYSTEM_PARA
    config_manager(system)
    py_config = PyqcatConfig(filename=system.backend.config_file)
    if hasattr(py_config, "log_path"):
        start_transfer_server_process(py_config.log_path)
        backend = Backend(
            username=system.backend.username,
            password=system.backend.password,
            config_file=py_config.settings,
            use_cache=system.backend.use_cache,
        )
    else:
        start_transfer_server_process()
        backend = Backend(**system.backend)
    backend.refresh()
    backend.system = system

    if not system.backend.use_cache:
        if not system.context.env_bits:
            system.context.env_bits = list(
                backend.context_manager.chip_data.cache_qubit.keys()
            ) + list(backend.context_manager.chip_data.cache_coupler.keys())
        backend.context_manager.set_global_options(**system.context)
        pyqlog.info("Start Backend Success | Use Local Config!")
    else:
        pyqlog.info("Start Backend Success | Use Cache Config!")

    backend.infos()
    return backend


def config_manager(system: QDict = None):
    try:
        if not CONFIG_MANAGER_PATH.exists():
            raise FileNotFoundError(f"Config file not found: {CONFIG_MANAGER_PATH}")

        system = system or SYSTEM_PARA
        _settings = json.loads(CONFIG_MANAGER_PATH.read_text())
        _path = CONFIG_MANAGER_PATH.parent

        for v in sys.argv[1:]:
            if v in _settings:
                config_file = _path / _settings[v]["env"] / "config.conf"
                system.backend.username = _settings[v]["username"]
                system.backend.password = _settings[v]["password"]
                system.backend.config_file = config_file
                pyqlog.warning(f"find cache {_settings[v]}")
                break
        else:
            pyqlog.warning(f"init v {sys.argv[1:]} not in manager, user default config")

    except Exception as e:
        pyqlog.warning(f"No find cache config manager data, user default config | {e}")


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_prefix="Naga_", env_file=".env")

    # naga account
    USERNAME: str = "admin"
    USER_PWD: str = "123456"

    BASE_NAGA_URL: str = "http://************:8030"

    # ------------------------ZMQ address------------------------
    # Naga
    NAGA_SOCK_ADDR: str = "tcp://127.0.0.1:9999"


settings = Settings()


def register_config(**kwargs):
    from pyQCat.invoker import DataCenter

    DataCenter().register_config(kwargs)


def query_config(**kwargs):
    from pyQCat.invoker import DataCenter

    DataCenter().query_config(kwargs)


def info_config(**kwargs):
    from pyQCat.invoker import DataCenter

    print(DataCenter().query_config(kwargs))
