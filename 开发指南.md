# Invoker 开发指南

## 修改接口

所有接口均存放与`api`模块中, 根据接口类型不同,目前分散在`chip`,`dag`,`experiment`, `user`四个文件内.

此处以实验接口为例:
```python
class Expeirment(ApiBase, prefix_url=b"/exp/", default_client=InvokerClient):
    URL_MAP = {
        "get_list": (b"list", b"get"),
        "exp_options": (b"exp", b"post"),
        "execute": (b"execute", b"post"),
        "exp_record": (b"history", b"get"),
        "exp_execute_history_list": (b"history/list", b"get")
    }
    def query_exp_list(self):
        """
        get experiment list and options.
        Returns:
            the standard response.{"code": (int)xxx, "data":(any){}, msg:(str) ok}
            the data such as
            {
                "exp_name": "",
                "exp_params": {},
                "official": false
              }
        """
        api_data = {}
        return self.template_requirement("get_list", api_data)
```
+ 在类声明中的`prefix_url`字段,声明了该类的`url`根目录,该类下的所有接口均在`/exp/`目录下.
+ `default_client`字段声明用于连接的客户端,目前均采用`InvokerClient`.
+ `URL_MAP`中存放了实际url与函数的映射关系,函数中仅仅需要带入`key`值即可.
+ 每个接口开发一个新的方法用于请求,可在方法中实现对携带数据的处理操作,最后将数据封装至`api_data`结构中,采用通用模板上传并获取数据,目前大多数接口仅进行数据封装,并未对数据进行过多处理.
+ 请求模板`template_requirement`包含两个参数,`api_data`和`identifier`
  + `identifier`接口名称为必填字段,填入`URL_MAP`中对应地址的key值;
  + `api_data`字段包含接口请求所需要的字段,如接口没有请求字段,可不填.



## 打包

项目打包脚本为`setup.py` 采用`setuptools`模块编写,基于`python3.9`的运行环境.

项目打包命令如下:
```shell
python setup.py sdist bdist_wheel

# 添加python版本和平台信息
python setup.py sdist bdist_wheel --python-tag=cp39 --plat-name=win_amd64
```

## 使用方式

`invoker`用于`monster`项目连接`courier`模块,采用zmq的通讯方式,登录后可直接后台连接至courier.
使用分为两种使用模式:
+ 开发`monster`代码过程中,直接将`qmq`文件夹**拷贝**至`pyQCat\invoker`文件夹下即可使用.
+ 第三方安装包的形式安装在`monster`同一环境中,项目会自动安装至`monster`项目代码的`invoker`文件夹下.

`Invoker`的使用参考`jupyter\invoker.ipynb`中的案例.