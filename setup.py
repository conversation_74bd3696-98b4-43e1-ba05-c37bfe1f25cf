import sys

from setuptools import setup, find_packages

# check python version
python_major_version, *_ = sys.version_info

if python_major_version != 3:
    raise EnvironmentError('PyQCat only support python 3')

DESCRIPTION = "PyQCat invoker is a Python package service link to OriginQ courier."

# get long description from README.rst
try:
    with open("README.md") as fin:
        LONG_DESCRIPTION = fin.read()
except IOError('read README.md error'):
    LONG_DESCRIPTION = None

package_datas = {}

# get build args
includes = ['pyQCat\invoker\qmq']


def is_build_pyd():
    argv = ""
    if len(sys.argv) > 1:
        argv = sys.argv[1]
    if argv == "build_ext":
        return True
    else:
        return False


package_list = ['pyQCat.invoker.' +
                x for x in find_packages(where='pyQCat\invoker',
                                         include=["qmq", "qmq.*"])]


def get_requirements():
    with open('requirements.txt') as f:
        require_str = f.read()
        print("require_str", require_str)
        if require_str:
            return require_str.split("\n")
        return []


REQUIREMENTS = get_requirements()

CLASSIFIERS = [
    'Development Status :: 2 - Beta',
    'Intended Audience :: Developers',
    'Topic :: Scientific/Engineering',
    "Operating System :: Microsoft :: Windows",
    # 'License :: OSI Approved :: MIT License',
    "Programming Language :: Python :: 3 :: Only",
    'Programming Language :: Python :: 3.9',
    "Programming Language :: Python :: Implementation :: CPython"
]

if is_build_pyd():
    pass
else:

    setup(
        name="pyqcat-invoker",
        version="0.0.1",
        author="Origin Quantum Development Team",
        author_email="<EMAIL>",
        # package_dir= {"": 'pyQCat\invoker'},
        packages=package_list,
        python_requires=">=3.7.*",
        include_package_data=False,
        package_data=package_datas,
        description=DESCRIPTION,
        long_description=LONG_DESCRIPTION,
        platforms=["win_amd64"],
        classifiers=CLASSIFIERS,
        install_requires=REQUIREMENTS,
    )
